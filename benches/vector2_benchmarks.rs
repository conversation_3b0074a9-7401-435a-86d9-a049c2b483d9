use criterion::{criterion_group, criterion_main, Criterion};
use std::hint::black_box;
use std::f32::consts::PI;

// Import the Vector2 implementation
use verturion::core::math::Vector2;

/// Generate test vectors with various characteristics for comprehensive benchmarking
fn generate_test_vectors() -> Vec<Vector2> {
    vec![
        // Unit vectors
        Vector2::new(1.0, 0.0),
        Vector2::new(0.0, 1.0),
        Vector2::new(0.707107, 0.707107), // 45 degrees

        // Small vectors
        Vector2::new(0.1, 0.2),
        Vector2::new(-0.3, 0.4),

        // Medium vectors
        Vector2::new(3.0, 4.0), // Classic 3-4-5 triangle
        Vector2::new(-5.0, 12.0), // 5-12-13 triangle
        Vector2::new(8.0, -6.0),

        // Large vectors
        Vector2::new(100.0, 200.0),
        Vector2::new(-500.0, 300.0),
        Vector2::new(1000.0, -1500.0),

        // Very small vectors (near zero)
        Vector2::new(1e-6, 1e-6),
        Vector2::new(-1e-5, 2e-5),

        // Edge cases
        Vector2::ZERO,
        Vector2::ONE,
        Vector2::LEFT,
        Vector2::RIGHT,
        Vector2::UP,
        Vector2::DOWN,
    ]
}

/// Generate pairs of vectors for binary operations
fn generate_vector_pairs() -> Vec<(Vector2, Vector2)> {
    let vectors = generate_test_vectors();
    let mut pairs = Vec::new();

    // Create representative pairs
    for i in 0..vectors.len().min(10) {
        for j in (i+1)..vectors.len().min(10) {
            pairs.push((vectors[i], vectors[j]));
        }
    }

    pairs
}

/// Benchmark core constructor and basic operations
fn bench_core_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("core_operations");

    // Constructor benchmark
    group.bench_function("new", |b| {
        b.iter(|| {
            black_box(Vector2::new(black_box(3.0), black_box(4.0)))
        })
    });

    // Length operations
    let test_vectors = generate_test_vectors();

    group.bench_function("length", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).length());
            }
        })
    });

    group.bench_function("length_squared", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).length_squared());
            }
        })
    });

    group.bench_function("normalized", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).normalized());
            }
        })
    });

    group.finish();
}

/// Benchmark mathematical operations
fn bench_math_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("math_operations");
    let vector_pairs = generate_vector_pairs();

    group.bench_function("dot_product", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                black_box(black_box(a).dot(black_box(*b)));
            }
        })
    });

    group.bench_function("cross_product", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                black_box(black_box(a).cross(black_box(*b)));
            }
        })
    });

    let test_vectors = generate_test_vectors();

    group.bench_function("angle", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).angle());
            }
        })
    });

    group.bench_function("angle_to", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                black_box(black_box(a).angle_to(black_box(*b)));
            }
        })
    });

    group.finish();
}

/// Benchmark interpolation methods
fn bench_interpolation(c: &mut Criterion) {
    let mut group = c.benchmark_group("interpolation");
    let vector_pairs = generate_vector_pairs();
    let weights = [0.0, 0.25, 0.5, 0.75, 1.0];

    group.bench_function("lerp", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                for &weight in &weights {
                    black_box(black_box(a).lerp(black_box(*b), black_box(weight)));
                }
            }
        })
    });

    group.bench_function("slerp", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                for &weight in &weights {
                    black_box(black_box(a).slerp(black_box(*b), black_box(weight)));
                }
            }
        })
    });

    group.bench_function("move_toward", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                black_box(black_box(a).move_toward(black_box(*b), black_box(1.0)));
            }
        })
    });

    group.finish();
}

/// Benchmark transformation operations
fn bench_transformations(c: &mut Criterion) {
    let mut group = c.benchmark_group("transformations");
    let test_vectors = generate_test_vectors();
    let angles = [0.0, PI/4.0, PI/2.0, PI, 3.0*PI/2.0, 2.0*PI];

    group.bench_function("rotated", |b| {
        b.iter(|| {
            for v in &test_vectors {
                for &angle in &angles {
                    black_box(black_box(v).rotated(black_box(angle)));
                }
            }
        })
    });

    group.bench_function("orthogonal", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).orthogonal());
            }
        })
    });

    let vector_pairs = generate_vector_pairs();

    group.bench_function("project", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                black_box(black_box(a).project(black_box(*b)));
            }
        })
    });

    group.bench_function("reflect", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                black_box(black_box(a).reflect(black_box(*b)));
            }
        })
    });

    group.finish();
}

/// Benchmark utility functions
fn bench_utilities(c: &mut Criterion) {
    let mut group = c.benchmark_group("utilities");
    let test_vectors = generate_test_vectors();
    let vector_pairs = generate_vector_pairs();

    group.bench_function("abs", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).abs());
            }
        })
    });

    group.bench_function("clamp", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).clamp(
                    black_box(Vector2::new(-10.0, -10.0)),
                    black_box(Vector2::new(10.0, 10.0))
                ));
            }
        })
    });

    group.bench_function("min_max", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                black_box(black_box(a).min(black_box(*b)));
                black_box(black_box(a).max(black_box(*b)));
            }
        })
    });

    group.bench_function("snapped", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).snapped(black_box(Vector2::new(0.5, 0.5))));
            }
        })
    });

    group.finish();
}

/// Benchmark comparison methods
fn bench_comparisons(c: &mut Criterion) {
    let mut group = c.benchmark_group("comparisons");
    let vector_pairs = generate_vector_pairs();
    let test_vectors = generate_test_vectors();

    group.bench_function("is_equal_approx", |b| {
        b.iter(|| {
            for (a, b) in &vector_pairs {
                black_box(black_box(a).is_equal_approx(black_box(*b)));
            }
        })
    });

    group.bench_function("is_normalized", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).is_normalized());
            }
        })
    });

    group.bench_function("is_finite", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).is_finite());
            }
        })
    });

    group.bench_function("is_zero_approx", |b| {
        b.iter(|| {
            for v in &test_vectors {
                black_box(black_box(v).is_zero_approx());
            }
        })
    });

    group.finish();
}

criterion_group!(
    benches,
    bench_core_operations,
    bench_math_operations,
    bench_interpolation,
    bench_transformations,
    bench_utilities,
    bench_comparisons
);

criterion_main!(benches);
