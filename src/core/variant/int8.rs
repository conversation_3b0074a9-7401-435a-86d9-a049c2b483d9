/// ### 8-bit Signed Integer Wrapper
///
/// A Godot-compatible wrapper around Rust's i8 primitive type that provides
/// type safety, utility methods, and seamless integration with the Variant system.
/// This type is designed for memory-efficient storage of very small integer values
/// while maintaining Godot's API compatibility.
///
/// ## Features
///
/// - **Memory Efficient**: Uses only 8 bits of storage
/// - **Type Safety**: Prevents accidental mixing of different numeric types
/// - **<PERSON>ot Compatibility**: Matches Godot's byte/int8 behavior patterns
/// - **Performance**: Zero-cost abstraction with Copy semantics
/// - **Integration**: Works seamlessly with Dictionary, Array, and Variant systems
/// - **Range**: Supports values from -128 to 127
///
/// ## Examples
///
/// ```
/// # use verturion::core::variant::Int8;
/// // Create int8s
/// let a = Int8::new(100);
/// let b = Int8::from_primitive(-50);
/// let c = Int8::default(); // 0
///
/// // Mathematical operations
/// let abs_val = b.abs();
/// let sign = b.sign();
/// let clamped = Int8::new(200).clamp(Int8::new(-100), Int8::new(100));
///
/// // Conversions
/// let as_int = a.to_int();
/// let as_float = a.to_float();
/// ```

use std::fmt;
use std::hash::Hash;

/// ### 8-bit signed integer wrapper type.
///
/// Provides a type-safe wrapper around i8 with Godot-compatible methods
/// and seamless integration with the Variant system.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub struct Int8 {
    /// The wrapped 8-bit integer value
    value: i8,
}

impl Int8 {
    /// ### Maximum value constant (127).
    pub const MAX: Self = Self { value: i8::MAX };

    /// ### Minimum value constant (-128).
    pub const MIN: Self = Self { value: i8::MIN };

    /// ### Zero constant.
    pub const ZERO: Self = Self { value: 0 };

    /// ### One constant.
    pub const ONE: Self = Self { value: 1 };

    /// ### Creates a new Int8 with the specified value.
    ///
    /// This is the primary constructor for creating Int8 instances.
    /// The value must be within the i8 range (-128 to 127).
    ///
    /// # Arguments
    /// * `value` - The 8-bit integer value to wrap
    ///
    /// # Returns
    /// A new Int8 instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let positive = Int8::new(100);
    /// let negative = Int8::new(-50);
    /// let zero = Int8::new(0);
    /// let max_val = Int8::new(127);
    /// ```
    #[inline]
    pub const fn new(value: i8) -> Self {
        Self { value }
    }

    /// ### Creates a new Int8 from a primitive i8 value.
    ///
    /// This method provides an alternative constructor that makes the
    /// conversion from primitive types explicit in the code.
    ///
    /// # Arguments
    /// * `value` - The primitive i8 value to wrap
    ///
    /// # Returns
    /// A new Int8 instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let from_literal = Int8::from_primitive(100);
    /// let from_variable = Int8::from_primitive(some_i8_value);
    /// ```
    #[inline]
    pub const fn from_primitive(value: i8) -> Self {
        Self::new(value)
    }

    /// ### Gets the wrapped 8-bit integer value.
    ///
    /// Returns the underlying i8 value contained in this Int8 wrapper.
    /// This is the primary method for extracting the primitive value.
    ///
    /// # Returns
    /// The wrapped i8 value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let int8_val = Int8::new(42);
    /// let primitive: i8 = int8_val.get();
    /// assert_eq!(primitive, 42);
    /// ```
    #[inline]
    pub const fn get(self) -> i8 {
        self.value
    }

    /// ### Sets the wrapped 8-bit integer value.
    ///
    /// Updates the value contained in this Int8 wrapper.
    /// This method provides mutable access to the wrapped value.
    ///
    /// # Arguments
    /// * `value` - The new value to store
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let mut int8_val = Int8::new(10);
    /// int8_val.set(20);
    /// assert_eq!(int8_val.get(), 20);
    /// ```
    #[inline]
    pub fn set(&mut self, value: i8) {
        self.value = value;
    }

    /// ### Returns the wrapped value as a primitive i8.
    ///
    /// This method provides an alternative to `get()` with a more explicit name
    /// indicating the conversion to primitive type.
    ///
    /// # Returns
    /// The wrapped i8 value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let int8_val = Int8::new(-42);
    /// let primitive = int8_val.as_primitive();
    /// assert_eq!(primitive, -42);
    /// ```
    #[inline]
    pub const fn as_primitive(self) -> i8 {
        self.value
    }
}

impl Int8 {
    /// ### Returns the absolute value of the 8-bit integer.
    ///
    /// Computes the absolute value, returning a positive Int8.
    /// For the minimum value (i8::MIN), this will wrap around due to
    /// two's complement representation limitations.
    ///
    /// # Returns
    /// An Int8 containing the absolute value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let negative = Int8::new(-42);
    /// let positive = Int8::new(17);
    ///
    /// assert_eq!(negative.abs(), Int8::new(42));
    /// assert_eq!(positive.abs(), Int8::new(17));
    /// assert_eq!(Int8::new(0).abs(), Int8::new(0));
    /// ```
    #[inline]
    pub fn abs(self) -> Self {
        Self::new(self.value.abs())
    }

    /// ### Returns the sign of the 8-bit integer.
    ///
    /// Returns -1 for negative numbers, 0 for zero, and 1 for positive numbers.
    /// This is useful for determining the direction or polarity of a value.
    ///
    /// # Returns
    /// An Int8 containing -1, 0, or 1 representing the sign.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// assert_eq!(Int8::new(-42).sign(), Int8::new(-1));
    /// assert_eq!(Int8::new(0).sign(), Int8::new(0));
    /// assert_eq!(Int8::new(42).sign(), Int8::new(1));
    /// ```
    #[inline]
    pub fn sign(self) -> Self {
        Self::new(self.value.signum())
    }

    /// ### Clamps the 8-bit integer to the specified range.
    ///
    /// Returns a value that is constrained to be between min and max (inclusive).
    /// If the value is less than min, returns min. If greater than max, returns max.
    /// Otherwise returns the original value.
    ///
    /// # Arguments
    /// * `min` - The minimum allowed value
    /// * `max` - The maximum allowed value
    ///
    /// # Returns
    /// An Int8 clamped to the specified range.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let value = Int8::new(150); // This would wrap to a negative value
    /// let min = Int8::new(0);
    /// let max = Int8::new(100);
    ///
    /// assert_eq!(Int8::new(50).clamp(min, max), Int8::new(50));
    /// assert_eq!(Int8::new(-10).clamp(min, max), Int8::new(0));
    /// ```
    #[inline]
    pub fn clamp(self, min: Self, max: Self) -> Self {
        Self::new(self.value.clamp(min.value, max.value))
    }

    /// ### Returns the minimum of two 8-bit integers.
    ///
    /// Compares two Int8 values and returns the smaller one.
    ///
    /// # Arguments
    /// * `other` - The other Int8 to compare with
    ///
    /// # Returns
    /// The smaller of the two Int8 values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let a = Int8::new(10);
    /// let b = Int8::new(20);
    ///
    /// assert_eq!(a.min(b), Int8::new(10));
    /// assert_eq!(b.min(a), Int8::new(10));
    /// ```
    #[inline]
    pub fn min(self, other: Self) -> Self {
        Self::new(self.value.min(other.value))
    }

    /// ### Returns the maximum of two 8-bit integers.
    ///
    /// Compares two Int8 values and returns the larger one.
    ///
    /// # Arguments
    /// * `other` - The other Int8 to compare with
    ///
    /// # Returns
    /// The larger of the two Int8 values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let a = Int8::new(10);
    /// let b = Int8::new(20);
    ///
    /// assert_eq!(a.max(b), Int8::new(20));
    /// assert_eq!(b.max(a), Int8::new(20));
    /// ```
    #[inline]
    pub fn max(self, other: Self) -> Self {
        Self::new(self.value.max(other.value))
    }

    /// ### Performs saturating addition.
    ///
    /// Adds another Int8 to this one, saturating at the numeric bounds
    /// instead of overflowing.
    ///
    /// # Arguments
    /// * `other` - The Int8 to add
    ///
    /// # Returns
    /// The result of saturating addition.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let a = Int8::new(120);
    /// let b = Int8::new(20);
    ///
    /// assert_eq!(a.saturating_add(b), Int8::MAX);
    /// ```
    #[inline]
    pub fn saturating_add(self, other: Self) -> Self {
        Self::new(self.value.saturating_add(other.value))
    }

    /// ### Performs saturating subtraction.
    ///
    /// Subtracts another Int8 from this one, saturating at the numeric bounds
    /// instead of overflowing.
    ///
    /// # Arguments
    /// * `other` - The Int8 to subtract
    ///
    /// # Returns
    /// The result of saturating subtraction.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let a = Int8::new(-120);
    /// let b = Int8::new(20);
    ///
    /// assert_eq!(a.saturating_sub(b), Int8::MIN);
    /// ```
    #[inline]
    pub fn saturating_sub(self, other: Self) -> Self {
        Self::new(self.value.saturating_sub(other.value))
    }
}

impl Int8 {
    /// ### Converts the 8-bit integer to a 64-bit integer.
    ///
    /// Creates a new Int wrapper containing the int8 value converted
    /// to i64. This conversion is always exact.
    ///
    /// # Returns
    /// An Int containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Int8, Int};
    /// let int8_val = Int8::new(100);
    /// let int_val = int8_val.to_int();
    /// assert_eq!(int_val.get(), 100);
    /// ```
    #[inline]
    pub fn to_int(self) -> super::Int {
        super::Int::new(self.value as i64)
    }

    /// ### Converts the 8-bit integer to a floating-point value.
    ///
    /// Creates a new Float wrapper containing the int8 value converted
    /// to f64. This conversion is always exact.
    ///
    /// # Returns
    /// A Float containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Int8, Float};
    /// let int8_val = Int8::new(42);
    /// let float_val = int8_val.to_float();
    /// assert_eq!(float_val.get(), 42.0);
    /// ```
    #[inline]
    pub fn to_float(self) -> super::Float {
        super::Float::new(self.value as f64)
    }

    /// ### Converts the 8-bit integer to a Short (16-bit integer).
    ///
    /// Creates a new Short wrapper containing the int8 value converted
    /// to i16. This conversion is always exact.
    ///
    /// # Returns
    /// A Short containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Int8, Short};
    /// let int8_val = Int8::new(100);
    /// let short_val = int8_val.to_short();
    /// assert_eq!(short_val.get(), 100);
    /// ```
    #[inline]
    pub fn to_short(self) -> super::Short {
        super::Short::new(self.value as i16)
    }

    /// ### Converts the 8-bit integer to a boolean.
    ///
    /// Creates a new Bool wrapper containing true for non-zero values
    /// and false for zero.
    ///
    /// # Returns
    /// A Bool containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Int8, Bool};
    /// let zero = Int8::new(0);
    /// let non_zero = Int8::new(42);
    ///
    /// assert_eq!(zero.to_bool().get(), false);
    /// assert_eq!(non_zero.to_bool().get(), true);
    /// ```
    #[inline]
    pub fn to_bool(self) -> super::Bool {
        super::Bool::new(self.value != 0)
    }
}

impl Default for Int8 {
    /// ### Creates a default Int8 with value 0.
    ///
    /// This implementation allows Int8 to be used in contexts where
    /// a default value is needed, such as in collections or when
    /// using the `Default::default()` method.
    ///
    /// # Returns
    /// An Int8 with value 0.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let default_int8 = Int8::default();
    /// assert_eq!(default_int8.get(), 0);
    ///
    /// let also_default: Int8 = Default::default();
    /// assert_eq!(also_default, Int8::new(0));
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new(0)
    }
}

impl fmt::Display for Int8 {
    /// ### Formats the Int8 for display.
    ///
    /// Provides a human-readable string representation of the 8-bit integer value.
    /// The output matches the standard formatting of i8 values.
    ///
    /// # Arguments
    /// * `f` - The formatter to write to
    ///
    /// # Returns
    /// A formatting result.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int8;
    /// let int8_val = Int8::new(42);
    /// assert_eq!(format!("{}", int8_val), "42");
    ///
    /// let negative = Int8::new(-17);
    /// assert_eq!(format!("{}", negative), "-17");
    /// ```
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.value)
    }
}

// Arithmetic operations
impl std::ops::Add for Int8 {
    type Output = Self;

    #[inline]
    fn add(self, rhs: Self) -> Self::Output {
        Self::new(self.value + rhs.value)
    }
}

impl std::ops::Sub for Int8 {
    type Output = Self;

    #[inline]
    fn sub(self, rhs: Self) -> Self::Output {
        Self::new(self.value - rhs.value)
    }
}

impl std::ops::Mul for Int8 {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: Self) -> Self::Output {
        Self::new(self.value * rhs.value)
    }
}

impl std::ops::Div for Int8 {
    type Output = Self;

    #[inline]
    fn div(self, rhs: Self) -> Self::Output {
        Self::new(self.value / rhs.value)
    }
}

impl std::ops::Rem for Int8 {
    type Output = Self;

    #[inline]
    fn rem(self, rhs: Self) -> Self::Output {
        Self::new(self.value % rhs.value)
    }
}

impl std::ops::Neg for Int8 {
    type Output = Self;

    #[inline]
    fn neg(self) -> Self::Output {
        Self::new(-self.value)
    }
}

// Assignment operations
impl std::ops::AddAssign for Int8 {
    #[inline]
    fn add_assign(&mut self, rhs: Self) {
        self.value += rhs.value;
    }
}

impl std::ops::SubAssign for Int8 {
    #[inline]
    fn sub_assign(&mut self, rhs: Self) {
        self.value -= rhs.value;
    }
}

impl std::ops::MulAssign for Int8 {
    #[inline]
    fn mul_assign(&mut self, rhs: Self) {
        self.value *= rhs.value;
    }
}

impl std::ops::DivAssign for Int8 {
    #[inline]
    fn div_assign(&mut self, rhs: Self) {
        self.value /= rhs.value;
    }
}

impl std::ops::RemAssign for Int8 {
    #[inline]
    fn rem_assign(&mut self, rhs: Self) {
        self.value %= rhs.value;
    }
}

// Conversions from primitive types
impl From<i8> for Int8 {
    #[inline]
    fn from(value: i8) -> Self {
        Self::new(value)
    }
}

impl From<u8> for Int8 {
    #[inline]
    fn from(value: u8) -> Self {
        Self::new(value as i8)
    }
}

// Conversions to primitive types
impl From<Int8> for i8 {
    #[inline]
    fn from(int8: Int8) -> Self {
        int8.value
    }
}

impl From<Int8> for i16 {
    #[inline]
    fn from(int8: Int8) -> Self {
        int8.value as i16
    }
}

impl From<Int8> for i32 {
    #[inline]
    fn from(int8: Int8) -> Self {
        int8.value as i32
    }
}

impl From<Int8> for i64 {
    #[inline]
    fn from(int8: Int8) -> Self {
        int8.value as i64
    }
}

impl From<Int8> for f32 {
    #[inline]
    fn from(int8: Int8) -> Self {
        int8.value as f32
    }
}

impl From<Int8> for f64 {
    #[inline]
    fn from(int8: Int8) -> Self {
        int8.value as f64
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_int8_creation() {
        let int8_1 = Int8::new(42);
        assert_eq!(int8_1.get(), 42);

        let int8_2 = Int8::from_primitive(-17);
        assert_eq!(int8_2.get(), -17);

        let int8_3 = Int8::default();
        assert_eq!(int8_3.get(), 0);

        let int8_4: Int8 = 100i8.into();
        assert_eq!(int8_4.get(), 100);
    }

    #[test]
    fn test_int8_constants() {
        assert_eq!(Int8::MAX.get(), i8::MAX);
        assert_eq!(Int8::MIN.get(), i8::MIN);
        assert_eq!(Int8::ZERO.get(), 0);
        assert_eq!(Int8::ONE.get(), 1);
    }

    #[test]
    fn test_int8_value_access() {
        let mut int8_val = Int8::new(10);
        assert_eq!(int8_val.get(), 10);
        assert_eq!(int8_val.as_primitive(), 10);

        int8_val.set(20);
        assert_eq!(int8_val.get(), 20);
    }

    #[test]
    fn test_int8_mathematical_operations() {
        let positive = Int8::new(42);
        let negative = Int8::new(-17);
        let zero = Int8::new(0);

        // Test abs
        assert_eq!(positive.abs(), Int8::new(42));
        assert_eq!(negative.abs(), Int8::new(17));
        assert_eq!(zero.abs(), Int8::new(0));

        // Test sign
        assert_eq!(positive.sign(), Int8::new(1));
        assert_eq!(negative.sign(), Int8::new(-1));
        assert_eq!(zero.sign(), Int8::new(0));

        // Test min/max
        assert_eq!(positive.min(negative), negative);
        assert_eq!(positive.max(negative), positive);
        assert_eq!(positive.min(positive), positive);
    }

    #[test]
    fn test_int8_clamp() {
        let min = Int8::new(0);
        let max = Int8::new(100);

        assert_eq!(Int8::new(50).clamp(min, max), Int8::new(50));
        assert_eq!(Int8::new(-10).clamp(min, max), Int8::new(0));
        assert_eq!(Int8::new(127).clamp(min, max), Int8::new(100));
        assert_eq!(Int8::new(0).clamp(min, max), Int8::new(0));
        assert_eq!(Int8::new(100).clamp(min, max), Int8::new(100));
    }

    #[test]
    fn test_int8_saturating_operations() {
        let large = Int8::new(120);
        let small = Int8::new(20);

        assert_eq!(large.saturating_add(small), Int8::MAX);
        assert_eq!(Int8::MIN.saturating_sub(small), Int8::MIN);
    }

    #[test]
    fn test_int8_arithmetic_operations() {
        let a = Int8::new(10);
        let b = Int8::new(3);

        assert_eq!(a + b, Int8::new(13));
        assert_eq!(a - b, Int8::new(7));
        assert_eq!(a * b, Int8::new(30));
        assert_eq!(a / b, Int8::new(3));
        assert_eq!(a % b, Int8::new(1));
        assert_eq!(-a, Int8::new(-10));
    }

    #[test]
    fn test_int8_assignment_operations() {
        let mut a = Int8::new(10);
        let b = Int8::new(3);

        a += b;
        assert_eq!(a, Int8::new(13));

        a -= b;
        assert_eq!(a, Int8::new(10));

        a *= b;
        assert_eq!(a, Int8::new(30));

        a /= b;
        assert_eq!(a, Int8::new(10));

        a %= b;
        assert_eq!(a, Int8::new(1));
    }

    #[test]
    fn test_int8_conversions() {
        let int8_val = Int8::new(42);

        // Test to other wrapper types
        let int_val = int8_val.to_int();
        assert_eq!(int_val.get(), 42);

        let float_val = int8_val.to_float();
        assert_eq!(float_val.get(), 42.0);

        let short_val = int8_val.to_short();
        assert_eq!(short_val.get(), 42);

        let bool_val = int8_val.to_bool();
        assert_eq!(bool_val.get(), true);

        let zero_bool = Int8::new(0).to_bool();
        assert_eq!(zero_bool.get(), false);

        // Test from primitive types
        let from_u8: Int8 = (42u8).into();
        assert_eq!(from_u8.get(), 42);

        // Test to primitive types
        let to_i8: i8 = int8_val.into();
        assert_eq!(to_i8, 42);

        let to_i16: i16 = int8_val.into();
        assert_eq!(to_i16, 42);

        let to_i32: i32 = int8_val.into();
        assert_eq!(to_i32, 42);

        let to_i64: i64 = int8_val.into();
        assert_eq!(to_i64, 42);

        let to_f32: f32 = int8_val.into();
        assert_eq!(to_f32, 42.0);

        let to_f64: f64 = int8_val.into();
        assert_eq!(to_f64, 42.0);
    }

    #[test]
    fn test_int8_comparison() {
        let a = Int8::new(10);
        let b = Int8::new(20);
        let c = Int8::new(10);

        assert!(a < b);
        assert!(b > a);
        assert_eq!(a, c);
        assert_ne!(a, b);
        assert!(a <= c);
        assert!(a >= c);
    }

    #[test]
    fn test_int8_display() {
        assert_eq!(format!("{}", Int8::new(42)), "42");
        assert_eq!(format!("{}", Int8::new(-17)), "-17");
        assert_eq!(format!("{}", Int8::new(0)), "0");
        assert_eq!(format!("{}", Int8::MAX), "127");
        assert_eq!(format!("{}", Int8::MIN), "-128");
    }

    #[test]
    fn test_int8_hash() {
        use std::collections::HashMap;

        let mut map = HashMap::new();
        let key1 = Int8::new(42);
        let key2 = Int8::new(17);

        map.insert(key1, "forty-two");
        map.insert(key2, "seventeen");

        assert_eq!(map.get(&Int8::new(42)), Some(&"forty-two"));
        assert_eq!(map.get(&Int8::new(17)), Some(&"seventeen"));
        assert_eq!(map.get(&Int8::new(100)), None);
    }

    #[test]
    fn test_int8_edge_cases() {
        // Test with extreme values
        let max_val = Int8::MAX;
        let min_val = Int8::MIN;

        assert_eq!(max_val.get(), i8::MAX);
        assert_eq!(min_val.get(), i8::MIN);

        // Test sign of extreme values
        assert_eq!(max_val.sign(), Int8::new(1));
        assert_eq!(min_val.sign(), Int8::new(-1));

        // Test abs of near-min value (since MIN.abs() panics in debug mode)
        let near_min = Int8::new(i8::MIN + 1);
        assert_eq!(near_min.abs(), Int8::new(i8::MAX));

        // Test overflow behavior (wrapping) - use wrapping_add to avoid panic
        let near_max = Int8::new(126);
        let overflow_result = Int8::new(near_max.get().wrapping_add(2));
        assert_eq!(overflow_result.get(), -128); // Wraps around

        // For MIN value, abs() will wrap around in release mode or panic in debug mode
        // This is expected behavior for two's complement arithmetic
    }

    #[test]
    fn test_int8_copy_semantics() {
        let original = Int8::new(42);
        let copied = original;

        // Both should have the same value
        assert_eq!(original.get(), 42);
        assert_eq!(copied.get(), 42);

        // Modifying one shouldn't affect the other
        let mut mutable_copy = original;
        mutable_copy.set(100);
        assert_eq!(original.get(), 42);
        assert_eq!(mutable_copy.get(), 100);
    }

    #[test]
    fn test_int8_range_limits() {
        // Test that values are properly constrained to i8 range
        assert_eq!(Int8::MAX.get(), 127);
        assert_eq!(Int8::MIN.get(), -128);

        // Test that conversion from u8 handles the full range
        let from_u8_max: Int8 = (255u8).into();
        assert_eq!(from_u8_max.get(), -1); // 255 as i8 is -1

        let from_u8_127: Int8 = (127u8).into();
        assert_eq!(from_u8_127.get(), 127);
    }
}
