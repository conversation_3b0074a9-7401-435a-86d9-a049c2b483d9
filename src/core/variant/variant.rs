//! Comprehensive Variant implementation for dynamic typing.
//!
//! This module provides a Variant enum that can hold any of the common Godot data types,
//! enabling dynamic typing for Dictionary and Array collections. The Variant type supports
//! type checking, conversion, and safe access to the underlying values.

use std::fmt;
use std::collections::HashMap;
use std::hash::Hasher;
use crate::core::math::{Vector2, Vector2i, Vector3, Vector3i, Vector4, Vector4i, Rect2, Rect2i, Transform2D, AABB, Projection, Quaternion, Basis, Plane};
use crate::core::error::Error;
use crate::core::input::InputEvent;
use super::{Color, Int, Float, Bool, Short, Int8};
use super::random_number_generator::RandomNumberGenerator;



/// ### A variant type that can hold any Godot-compatible value.
///
/// Variant provides dynamic typing capabilities,
/// it can store any of the common data types used in game development and provides
/// safe type checking and conversion methods.
///
/// ## Supported Types
///
/// - **Nil**: Represents null/empty values
/// - **Bool**: Boolean wrapper type
/// - **Int**: 64-bit signed integer wrapper
/// - **Float**: 64-bit floating-point wrapper
/// - **Short**: 16-bit signed integer wrapper
/// - **Int8**: 8-bit signed integer wrapper
/// - **String**: UTF-8 text strings
/// - **Vector2**: 2D floating-point vectors
/// - **Vector2i**: 2D integer vectors
/// - **Vector3**: 3D floating-point vectors
/// - **Vector3i**: 3D integer vectors
/// - **Vector4**: 4D floating-point vectors
/// - **Vector4i**: 4D integer vectors
/// - **Color**: RGBA color values
/// - **Rect2**: 2D floating-point rectangles
/// - **Rect2i**: 2D integer rectangles
/// - **Transform2D**: 2D transformation matrices
/// - **Array**: Dynamic arrays of Variants
/// - **Dictionary**: Key-value maps with Variant keys and values
///
/// ## Use Cases
///
/// Variant is ideal for:
/// - **Dynamic Collections**: Arrays and dictionaries with mixed types
/// - **Scripting Integration**: Runtime type flexibility
/// - **Configuration Data**: Settings and parameters with various types
/// - **Serialization**: Converting between different data formats
/// - **Game State**: Flexible data storage for game objects
#[derive(Debug, Clone, PartialEq)]
pub enum Variant {
    /// Represents a null or empty value.
    Nil,
    /// Boolean value (true or false).
    Bool(bool),
    /// 64-bit signed integer value.
    Int(i64),
    /// 64-bit floating-point value.
    Float(f64),
    /// UTF-8 string value.
    String(String),
    /// 2D floating-point vector.
    Vector2(Vector2),
    /// 2D integer vector
    Vector2i(Vector2i),
    /// 3D floating-point vector.
    Vector3(Vector3),
    /// 3D integer vector
    Vector3i(Vector3i),
    /// 4D floating-point vector.
    Vector4(Vector4),
    /// 4D integer vector
    Vector4i(Vector4i),
    /// RGBA color value.
    Color(Color),
    /// 2D floating-point rectangle
    Rect2(Rect2),
    /// 2D integer rectangle
    Rect2i(Rect2i),
    /// 2D transformation matrix
    Transform2D(Transform2D),
    /// 3D axis-aligned bounding box for collision detection.
    AABB(AABB),
    /// 4x4 projection matrix for 3D rendering.
    Projection(Projection),
    /// Quaternion for 3D rotations.
    Quaternion(Quaternion),
    /// 3x3 basis matrix for 3D transformations.
    Basis(Basis),
    /// 3D plane for spatial calculations.
    Plane(Plane),
    /// Dynamic array of variants.
    Array(Vec<Variant>),
    /// Key-value dictionary with variant keys and values.
    Dictionary(HashMap<Variant, Variant>),
    /// Boolean wrapper type.
    BoolWrapper(Bool),
    /// 64-bit signed integer wrapper.
    IntWrapper(Int),
    /// 64-bit floating-point wrapper.
    FloatWrapper(Float),
    /// 16-bit signed integer wrapper.
    Short(Short),
    /// 8-bit signed integer wrapper.
    Int8(Int8),
    /// Godot-compatible error code.
    Error(Error),
    /// Input event for keyboard, mouse, and gamepad input.
    InputEvent(InputEvent),
    /// Godot-compatible String wrapper.
    GodotString(super::string::String),
    /// Interned string name for performance.
    StringName(super::string_name::StringName),
    /// Scene tree node path for navigation.
    NodePath(crate::core::scene::NodePath),
    /// Random number generator for seeded randomization.
    RandomNumberGenerator(RandomNumberGenerator),
    /// Base node for scene tree management.
    Node(crate::core::scene::Node),
    /// 2D sprite for texture rendering.
    Sprite2D(crate::core::scene::nodes::Sprite2D),
    /// UI label for text display.
    Label(crate::core::scene::nodes::ui::Label),
    /// UI button for interactive elements.
    Button(crate::core::scene::nodes::ui::Button),
    /// 2D collision shape for physics.
    CollisionShape2D(crate::core::scene::nodes::physics::CollisionShape2D),
    /// 2D area for trigger zones.
    Area2D(crate::core::scene::nodes::physics::Area2D),
    /// 2D rigid body for physics simulation.
    RigidBody2D(crate::core::scene::nodes::physics::RigidBody2D),
}

impl Variant {
    /// ### Creates a new Nil variant.
    ///
    /// Returns a Variant containing no value, equivalent to null or None.
    ///
    /// # Returns
    /// A Variant::Nil instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// let nil_value = Variant::nil();
    /// assert!(nil_value.is_nil());
    /// ```
    #[inline]
    pub fn nil() -> Self {
        Variant::Nil
    }

    /// ### Checks if the variant is Nil.
    ///
    /// Returns true if the variant contains no value (Nil type).
    ///
    /// # Returns
    /// True if the variant is Nil, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// let nil_value = Variant::nil();
    /// let int_value = Variant::Int(42);
    /// assert!(nil_value.is_nil());
    /// assert!(!int_value.is_nil());
    /// ```
    #[inline]
    pub fn is_nil(&self) -> bool {
        matches!(self, Variant::Nil)
    }

    /// ### Checks if the variant is a boolean.
    ///
    /// Returns true if the variant contains a boolean value.
    ///
    /// # Returns
    /// True if the variant is Bool, false otherwise.
    #[inline]
    pub fn is_bool(&self) -> bool {
        matches!(self, Variant::Bool(_))
    }

    /// ### Checks if the variant is an integer.
    ///
    /// Returns true if the variant contains an integer value.
    ///
    /// # Returns
    /// True if the variant is Int, false otherwise.
    #[inline]
    pub fn is_int(&self) -> bool {
        matches!(self, Variant::Int(_))
    }

    /// ### Checks if the variant is a float.
    ///
    /// Returns true if the variant contains a floating-point value.
    ///
    /// # Returns
    /// True if the variant is Float, false otherwise.
    #[inline]
    pub fn is_float(&self) -> bool {
        matches!(self, Variant::Float(_))
    }

    /// ### Checks if the variant is a string.
    ///
    /// Returns true if the variant contains a string value.
    ///
    /// # Returns
    /// True if the variant is String, false otherwise.
    #[inline]
    pub fn is_string(&self) -> bool {
        matches!(self, Variant::String(_))
    }

    /// ### Checks if the variant is a Vector2.
    ///
    /// Returns true if the variant contains a 2D vector value.
    ///
    /// # Returns
    /// True if the variant is Vector2, false otherwise.
    #[inline]
    pub fn is_vector2(&self) -> bool {
        matches!(self, Variant::Vector2(_))
    }

    /// ### Checks if the variant is a Vector3.
    ///
    /// Returns true if the variant contains a 3D vector value.
    ///
    /// # Returns
    /// True if the variant is Vector3, false otherwise.
    #[inline]
    pub fn is_vector3(&self) -> bool {
        matches!(self, Variant::Vector3(_))
    }

    /// ### Checks if the variant is a Vector4.
    ///
    /// Returns true if the variant contains a 4D vector value.
    ///
    /// # Returns
    /// True if the variant is Vector4, false otherwise.
    #[inline]
    pub fn is_vector4(&self) -> bool {
        matches!(self, Variant::Vector4(_))
    }

    /// ### Checks if the variant is a Vector2i.
    ///
    /// Returns true if the variant contains a 2D integer vector value.
    ///
    /// # Returns
    /// True if the variant is Vector2i, false otherwise.
    #[inline]
    pub fn is_vector2i(&self) -> bool {
        matches!(self, Variant::Vector2i(_))
    }

    /// ### Checks if the variant is a Vector3i.
    ///
    /// Returns true if the variant contains a 3D integer vector value.
    ///
    /// # Returns
    /// True if the variant is Vector3i, false otherwise.
    #[inline]
    pub fn is_vector3i(&self) -> bool {
        matches!(self, Variant::Vector3i(_))
    }

    /// ### Checks if the variant is a Vector4i.
    ///
    /// Returns true if the variant contains a 4D integer vector value.
    ///
    /// # Returns
    /// True if the variant is Vector4i, false otherwise.
    #[inline]
    pub fn is_vector4i(&self) -> bool {
        matches!(self, Variant::Vector4i(_))
    }

    /// ### Checks if the variant is a Color.
    ///
    /// Returns true if the variant contains a color value.
    ///
    /// # Returns
    /// True if the variant is Color, false otherwise.
    #[inline]
    pub fn is_color(&self) -> bool {
        matches!(self, Variant::Color(_))
    }

    /// ### Checks if the variant is a Rect2.
    ///
    /// Returns true if the variant contains a 2D floating-point rectangle value.
    ///
    /// # Returns
    /// True if the variant is Rect2, false otherwise.
    #[inline]
    pub fn is_rect2(&self) -> bool {
        matches!(self, Variant::Rect2(_))
    }

    /// ### Checks if the variant is a Rect2i.
    ///
    /// Returns true if the variant contains a 2D integer rectangle value.
    ///
    /// # Returns
    /// True if the variant is Rect2i, false otherwise.
    #[inline]
    pub fn is_rect2i(&self) -> bool {
        matches!(self, Variant::Rect2i(_))
    }

    /// ### Checks if the variant is a Transform2D.
    ///
    /// Returns true if the variant contains a 2D transformation matrix value.
    ///
    /// # Returns
    /// True if the variant is Transform2D, false otherwise.
    #[inline]
    pub fn is_transform2d(&self) -> bool {
        matches!(self, Variant::Transform2D(_))
    }

    /// ### Checks if the variant is an AABB.
    ///
    /// Returns true if the variant contains a 3D axis-aligned bounding box value.
    ///
    /// # Returns
    /// True if the variant is AABB, false otherwise.
    #[inline]
    pub fn is_aabb(&self) -> bool {
        matches!(self, Variant::AABB(_))
    }

    /// ### Checks if the variant is a Projection.
    ///
    /// Returns true if the variant contains a 4x4 projection matrix value.
    ///
    /// # Returns
    /// True if the variant is Projection, false otherwise.
    #[inline]
    pub fn is_projection(&self) -> bool {
        matches!(self, Variant::Projection(_))
    }

    /// ### Checks if the variant is a Quaternion.
    ///
    /// Returns true if the variant contains a quaternion rotation value.
    ///
    /// # Returns
    /// True if the variant is Quaternion, false otherwise.
    #[inline]
    pub fn is_quaternion(&self) -> bool {
        matches!(self, Variant::Quaternion(_))
    }

    /// ### Checks if the variant is a Basis.
    ///
    /// Returns true if the variant contains a 3x3 basis matrix value.
    ///
    /// # Returns
    /// True if the variant is Basis, false otherwise.
    #[inline]
    pub fn is_basis(&self) -> bool {
        matches!(self, Variant::Basis(_))
    }

    /// ### Checks if the variant is a Plane.
    ///
    /// Returns true if the variant contains a 3D plane value.
    ///
    /// # Returns
    /// True if the variant is Plane, false otherwise.
    #[inline]
    pub fn is_plane(&self) -> bool {
        matches!(self, Variant::Plane(_))
    }

    /// ### Checks if the variant is an Array.
    ///
    /// Returns true if the variant contains an array value.
    ///
    /// # Returns
    /// True if the variant is Array, false otherwise.
    #[inline]
    pub fn is_array(&self) -> bool {
        matches!(self, Variant::Array(_))
    }

    /// ### Checks if the variant is a Dictionary.
    ///
    /// Returns true if the variant contains a dictionary value.
    ///
    /// # Returns
    /// True if the variant is Dictionary, false otherwise.
    #[inline]
    pub fn is_dictionary(&self) -> bool {
        matches!(self, Variant::Dictionary(_))
    }

    /// ### Checks if the variant contains a Bool wrapper.
    ///
    /// Returns true if this variant holds a Bool wrapper value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a Bool wrapper, false otherwise.
    #[inline]
    pub fn is_bool_wrapper(&self) -> bool {
        matches!(self, Variant::BoolWrapper(_))
    }

    /// ### Checks if the variant contains an Int wrapper.
    ///
    /// Returns true if this variant holds an Int wrapper value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is an Int wrapper, false otherwise.
    #[inline]
    pub fn is_int_wrapper(&self) -> bool {
        matches!(self, Variant::IntWrapper(_))
    }

    /// ### Checks if the variant contains a Float wrapper.
    ///
    /// Returns true if this variant holds a Float wrapper value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a Float wrapper, false otherwise.
    #[inline]
    pub fn is_float_wrapper(&self) -> bool {
        matches!(self, Variant::FloatWrapper(_))
    }

    /// ### Checks if the variant contains a Short.
    ///
    /// Returns true if this variant holds a Short value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a Short, false otherwise.
    #[inline]
    pub fn is_short(&self) -> bool {
        matches!(self, Variant::Short(_))
    }

    /// ### Checks if the variant contains an Int8.
    ///
    /// Returns true if this variant holds an Int8 value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is an Int8, false otherwise.
    #[inline]
    pub fn is_int8(&self) -> bool {
        matches!(self, Variant::Int8(_))
    }

    /// ### Checks if the variant contains an Error.
    ///
    /// Returns true if this variant holds an Error value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is an Error, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::error::Error;
    /// let error_var = Variant::from(Error::ERR_FILE_NOT_FOUND);
    /// assert!(error_var.is_error());
    ///
    /// let string_var = Variant::from("hello");
    /// assert!(!string_var.is_error());
    /// ```
    #[inline]
    pub fn is_error(&self) -> bool {
        matches!(self, Variant::Error(_))
    }

    /// ### Checks if the variant contains an InputEvent.
    ///
    /// Returns true if this variant holds an InputEvent value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is an InputEvent, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::input::{InputEvent, KeyCode};
    /// let input_var = Variant::from(InputEvent::key(KeyCode::Space, true, false));
    /// assert!(input_var.is_input_event());
    ///
    /// let string_var = Variant::from("hello");
    /// assert!(!string_var.is_input_event());
    /// ```
    #[inline]
    pub fn is_input_event(&self) -> bool {
        matches!(self, Variant::InputEvent(_))
    }

    /// ### Checks if the variant contains a Node.
    ///
    /// Returns true if this variant holds a Node value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a Node, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::scene::Node;
    /// let node_var = Variant::from(Node::new("TestNode"));
    /// assert!(node_var.is_node());
    ///
    /// let string_var = Variant::from("hello");
    /// assert!(!string_var.is_node());
    /// ```
    #[inline]
    pub fn is_node(&self) -> bool {
        matches!(self, Variant::Node(_))
    }

    /// ### Checks if the variant contains a Sprite2D.
    ///
    /// Returns true if this variant holds a Sprite2D value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a Sprite2D, false otherwise.
    #[inline]
    pub fn is_sprite2d(&self) -> bool {
        matches!(self, Variant::Sprite2D(_))
    }

    /// ### Checks if the variant contains a Label.
    ///
    /// Returns true if this variant holds a Label value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a Label, false otherwise.
    #[inline]
    pub fn is_label(&self) -> bool {
        matches!(self, Variant::Label(_))
    }

    /// ### Checks if the variant contains a Button.
    ///
    /// Returns true if this variant holds a Button value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a Button, false otherwise.
    #[inline]
    pub fn is_button(&self) -> bool {
        matches!(self, Variant::Button(_))
    }

    /// ### Checks if the variant contains a CollisionShape2D.
    ///
    /// Returns true if this variant holds a CollisionShape2D value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a CollisionShape2D, false otherwise.
    #[inline]
    pub fn is_collision_shape2d(&self) -> bool {
        matches!(self, Variant::CollisionShape2D(_))
    }

    /// ### Checks if the variant contains an Area2D.
    ///
    /// Returns true if this variant holds an Area2D value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is an Area2D, false otherwise.
    #[inline]
    pub fn is_area2d(&self) -> bool {
        matches!(self, Variant::Area2D(_))
    }

    /// ### Checks if the variant contains a RigidBody2D.
    ///
    /// Returns true if this variant holds a RigidBody2D value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a RigidBody2D, false otherwise.
    #[inline]
    pub fn is_rigid_body2d(&self) -> bool {
        matches!(self, Variant::RigidBody2D(_))
    }

    /// ### Attempts to extract a boolean value from the variant.
    ///
    /// Returns the boolean value if the variant is Bool, otherwise returns None.
    ///
    /// # Returns
    /// Some(bool) if the variant is Bool, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// let bool_variant = Variant::Bool(true);
    /// let int_variant = Variant::Int(42);
    /// assert_eq!(bool_variant.as_bool(), Some(true));
    /// assert_eq!(int_variant.as_bool(), None);
    /// ```
    #[inline]
    pub fn as_bool(&self) -> Option<bool> {
        match self {
            Variant::Bool(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract an integer value from the variant.
    ///
    /// Returns the integer value if the variant is Int, otherwise returns None.
    ///
    /// # Returns
    /// Some(i64) if the variant is Int, None otherwise.
    #[inline]
    pub fn as_int(&self) -> Option<i64> {
        match self {
            Variant::Int(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a float value from the variant.
    ///
    /// Returns the float value if the variant is Float, otherwise returns None.
    ///
    /// # Returns
    /// Some(f64) if the variant is Float, None otherwise.
    #[inline]
    pub fn as_float(&self) -> Option<f64> {
        match self {
            Variant::Float(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a string reference from the variant.
    ///
    /// Returns a reference to the string value if the variant is String, otherwise returns None.
    ///
    /// # Returns
    /// Some(&String) if the variant is String, None otherwise.
    #[inline]
    pub fn as_string(&self) -> Option<&String> {
        match self {
            Variant::String(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Vector2 value from the variant.
    ///
    /// Returns the Vector2 value if the variant is Vector2, otherwise returns None.
    ///
    /// # Returns
    /// Some(Vector2) if the variant is Vector2, None otherwise.
    #[inline]
    pub fn as_vector2(&self) -> Option<Vector2> {
        match self {
            Variant::Vector2(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Vector3 value from the variant.
    ///
    /// Returns the Vector3 value if the variant is Vector3, otherwise returns None.
    ///
    /// # Returns
    /// Some(Vector3) if the variant is Vector3, None otherwise.
    #[inline]
    pub fn as_vector3(&self) -> Option<Vector3> {
        match self {
            Variant::Vector3(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Vector4 value from the variant.
    ///
    /// Returns the Vector4 value if the variant is Vector4, otherwise returns None.
    ///
    /// # Returns
    /// Some(Vector4) if the variant is Vector4, None otherwise.
    #[inline]
    pub fn as_vector4(&self) -> Option<Vector4> {
        match self {
            Variant::Vector4(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Vector2i value from the variant.
    ///
    /// Returns the Vector2i value if the variant is Vector2i, otherwise returns None.
    ///
    /// # Returns
    /// Some(Vector2i) if the variant is Vector2i, None otherwise.
    #[inline]
    pub fn as_vector2i(&self) -> Option<Vector2i> {
        match self {
            Variant::Vector2i(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Vector3i value from the variant.
    ///
    /// Returns the Vector3i value if the variant is Vector3i, otherwise returns None.
    ///
    /// # Returns
    /// Some(Vector3i) if the variant is Vector3i, None otherwise.
    #[inline]
    pub fn as_vector3i(&self) -> Option<Vector3i> {
        match self {
            Variant::Vector3i(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Vector4i value from the variant.
    ///
    /// Returns the Vector4i value if the variant is Vector4i, otherwise returns None.
    ///
    /// # Returns
    /// Some(Vector4i) if the variant is Vector4i, None otherwise.
    #[inline]
    pub fn as_vector4i(&self) -> Option<Vector4i> {
        match self {
            Variant::Vector4i(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Color value from the variant.
    ///
    /// Returns the Color value if the variant is Color, otherwise returns None.
    ///
    /// # Returns
    /// Some(Color) if the variant is Color, None otherwise.
    #[inline]
    pub fn as_color(&self) -> Option<Color> {
        match self {
            Variant::Color(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Rect2 value from the variant.
    ///
    /// Returns the Rect2 value if the variant is Rect2, otherwise returns None.
    ///
    /// # Returns
    /// Some(Rect2) if the variant is Rect2, None otherwise.
    #[inline]
    pub fn as_rect2(&self) -> Option<Rect2> {
        match self {
            Variant::Rect2(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Rect2i value from the variant.
    ///
    /// Returns the Rect2i value if the variant is Rect2i, otherwise returns None.
    ///
    /// # Returns
    /// Some(Rect2i) if the variant is Rect2i, None otherwise.
    #[inline]
    pub fn as_rect2i(&self) -> Option<Rect2i> {
        match self {
            Variant::Rect2i(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Transform2D value from the variant.
    ///
    /// Returns the Transform2D value if the variant is Transform2D, otherwise returns None.
    ///
    /// # Returns
    /// Some(Transform2D) if the variant is Transform2D, None otherwise.
    #[inline]
    pub fn as_transform2d(&self) -> Option<Transform2D> {
        match self {
            Variant::Transform2D(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract an AABB value from the variant.
    ///
    /// Returns the AABB value if the variant is AABB, otherwise returns None.
    ///
    /// # Returns
    /// Some(AABB) if the variant is AABB, None otherwise.
    #[inline]
    pub fn as_aabb(&self) -> Option<AABB> {
        match self {
            Variant::AABB(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Projection value from the variant.
    ///
    /// Returns the Projection value if the variant is Projection, otherwise returns None.
    ///
    /// # Returns
    /// Some(Projection) if the variant is Projection, None otherwise.
    #[inline]
    pub fn as_projection(&self) -> Option<Projection> {
        match self {
            Variant::Projection(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Quaternion value from the variant.
    ///
    /// Returns the Quaternion value if the variant is Quaternion, otherwise returns None.
    ///
    /// # Returns
    /// Some(Quaternion) if the variant is Quaternion, None otherwise.
    #[inline]
    pub fn as_quaternion(&self) -> Option<Quaternion> {
        match self {
            Variant::Quaternion(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Basis value from the variant.
    ///
    /// Returns the Basis value if the variant is Basis, otherwise returns None.
    ///
    /// # Returns
    /// Some(Basis) if the variant is Basis, None otherwise.
    #[inline]
    pub fn as_basis(&self) -> Option<Basis> {
        match self {
            Variant::Basis(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Plane value from the variant.
    ///
    /// Returns the Plane value if the variant is Plane, otherwise returns None.
    ///
    /// # Returns
    /// Some(Plane) if the variant is Plane, None otherwise.
    #[inline]
    pub fn as_plane(&self) -> Option<Plane> {
        match self {
            Variant::Plane(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract an array reference from the variant.
    ///
    /// Returns a reference to the array if the variant is Array, otherwise returns None.
    ///
    /// # Returns
    /// Some(&Vec<Variant>) if the variant is Array, None otherwise.
    #[inline]
    pub fn as_array(&self) -> Option<&Vec<Variant>> {
        match self {
            Variant::Array(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a dictionary reference from the variant.
    ///
    /// Returns a reference to the dictionary if the variant is Dictionary, otherwise returns None.
    ///
    /// # Returns
    /// Some(&HashMap<Variant, Variant>) if the variant is Dictionary, None otherwise.
    #[inline]
    pub fn as_dictionary(&self) -> Option<&HashMap<Variant, Variant>> {
        match self {
            Variant::Dictionary(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Bool wrapper value from the variant.
    ///
    /// Returns the Bool wrapper value if the variant is BoolWrapper, otherwise returns None.
    ///
    /// # Returns
    /// Some(Bool) if the variant is BoolWrapper, None otherwise.
    #[inline]
    pub fn as_bool_wrapper(&self) -> Option<Bool> {
        match self {
            Variant::BoolWrapper(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract an Int wrapper value from the variant.
    ///
    /// Returns the Int wrapper value if the variant is IntWrapper, otherwise returns None.
    ///
    /// # Returns
    /// Some(Int) if the variant is IntWrapper, None otherwise.
    #[inline]
    pub fn as_int_wrapper(&self) -> Option<Int> {
        match self {
            Variant::IntWrapper(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Float wrapper value from the variant.
    ///
    /// Returns the Float wrapper value if the variant is FloatWrapper, otherwise returns None.
    ///
    /// # Returns
    /// Some(Float) if the variant is FloatWrapper, None otherwise.
    #[inline]
    pub fn as_float_wrapper(&self) -> Option<Float> {
        match self {
            Variant::FloatWrapper(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Short value from the variant.
    ///
    /// Returns the Short value if the variant is Short, otherwise returns None.
    ///
    /// # Returns
    /// Some(Short) if the variant is Short, None otherwise.
    #[inline]
    pub fn as_short(&self) -> Option<Short> {
        match self {
            Variant::Short(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract an Int8 value from the variant.
    ///
    /// Returns the Int8 value if the variant is Int8, otherwise returns None.
    ///
    /// # Returns
    /// Some(Int8) if the variant is Int8, None otherwise.
    #[inline]
    pub fn as_int8(&self) -> Option<Int8> {
        match self {
            Variant::Int8(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract an Error value from the variant.
    ///
    /// Returns the Error value if the variant is Error, otherwise returns None.
    ///
    /// # Returns
    /// Some(Error) if the variant is Error, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::error::Error;
    /// let error_var = Variant::from(Error::ERR_FILE_NOT_FOUND);
    /// assert_eq!(error_var.as_error(), Some(Error::ERR_FILE_NOT_FOUND));
    ///
    /// let string_var = Variant::from("hello");
    /// assert_eq!(string_var.as_error(), None);
    /// ```
    #[inline]
    pub fn as_error(&self) -> Option<Error> {
        match self {
            Variant::Error(value) => Some(*value),
            _ => None,
        }
    }

    /// ### Attempts to extract an InputEvent value from the variant.
    ///
    /// Returns a reference to the InputEvent if the variant is InputEvent, otherwise returns None.
    ///
    /// # Returns
    /// Some(&InputEvent) if the variant is InputEvent, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::input::{InputEvent, KeyCode};
    /// let input_var = Variant::from(InputEvent::key(KeyCode::Space, true, false));
    /// assert!(input_var.as_input_event().is_some());
    ///
    /// let string_var = Variant::from("hello");
    /// assert_eq!(string_var.as_input_event(), None);
    /// ```
    #[inline]
    pub fn as_input_event(&self) -> Option<&InputEvent> {
        match self {
            Variant::InputEvent(value) => Some(value),
            _ => None,
        }
    }

    /// ### Checks if the variant contains a GodotString.
    ///
    /// Returns true if this variant holds a GodotString value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a GodotString, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::variant::string::String as GodotString;
    /// let string_var = Variant::from(GodotString::from("hello"));
    /// assert!(string_var.is_godot_string());
    ///
    /// let int_var = Variant::from(42);
    /// assert!(!int_var.is_godot_string());
    /// ```
    #[inline]
    pub fn is_godot_string(&self) -> bool {
        matches!(self, Variant::GodotString(_))
    }

    /// ### Checks if the variant contains a StringName.
    ///
    /// Returns true if this variant holds a StringName value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a StringName, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::variant::string_name::StringName;
    /// let name_var = Variant::from(StringName::from("node_name"));
    /// assert!(name_var.is_string_name());
    ///
    /// let int_var = Variant::from(42);
    /// assert!(!int_var.is_string_name());
    /// ```
    #[inline]
    pub fn is_string_name(&self) -> bool {
        matches!(self, Variant::StringName(_))
    }

    /// ### Checks if the variant contains a NodePath.
    ///
    /// Returns true if this variant holds a NodePath value, false otherwise.
    ///
    /// # Returns
    /// True if the variant is a NodePath, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::scene::NodePath;
    /// let path_var = Variant::from(NodePath::from("/root/Player"));
    /// assert!(path_var.is_node_path());
    ///
    /// let int_var = Variant::from(42);
    /// assert!(!int_var.is_node_path());
    /// ```
    #[inline]
    pub fn is_node_path(&self) -> bool {
        matches!(self, Variant::NodePath(_))
    }

    /// ### Attempts to extract a GodotString reference from the variant.
    ///
    /// Returns a reference to the GodotString if the variant is GodotString, otherwise returns None.
    ///
    /// # Returns
    /// Some(&GodotString) if the variant is GodotString, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::variant::string::String as GodotString;
    /// let string_var = Variant::from(GodotString::from("hello"));
    /// assert_eq!(string_var.as_godot_string().unwrap().as_str(), "hello");
    ///
    /// let int_var = Variant::from(42);
    /// assert_eq!(int_var.as_godot_string(), None);
    /// ```
    #[inline]
    pub fn as_godot_string(&self) -> Option<&super::string::String> {
        match self {
            Variant::GodotString(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a StringName reference from the variant.
    ///
    /// Returns a reference to the StringName if the variant is StringName, otherwise returns None.
    ///
    /// # Returns
    /// Some(&StringName) if the variant is StringName, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::variant::string_name::StringName;
    /// let name_var = Variant::from(StringName::from("node_name"));
    /// assert_eq!(name_var.as_string_name().unwrap().as_str(), "node_name");
    ///
    /// let int_var = Variant::from(42);
    /// assert_eq!(int_var.as_string_name(), None);
    /// ```
    #[inline]
    pub fn as_string_name(&self) -> Option<&super::string_name::StringName> {
        match self {
            Variant::StringName(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a NodePath reference from the variant.
    ///
    /// Returns a reference to the NodePath if the variant is NodePath, otherwise returns None.
    ///
    /// # Returns
    /// Some(&NodePath) if the variant is NodePath, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::scene::NodePath;
    /// let path_var = Variant::from(NodePath::from("/root/Player"));
    /// assert_eq!(path_var.as_node_path().unwrap().as_str(), "/root/Player");
    ///
    /// let int_var = Variant::from(42);
    /// assert_eq!(int_var.as_node_path(), None);
    /// ```
    #[inline]
    pub fn as_node_path(&self) -> Option<&crate::core::scene::NodePath> {
        match self {
            Variant::NodePath(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Node reference from the variant.
    ///
    /// Returns a reference to the Node if the variant is Node, otherwise returns None.
    ///
    /// # Returns
    /// Some(&Node) if the variant is Node, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Variant;
    /// # use verturion::core::scene::Node;
    /// let node = Node::new("TestNode");
    /// let node_var = Variant::from(node.clone());
    /// assert_eq!(node_var.as_node().unwrap().get_name(), "TestNode");
    ///
    /// let int_var = Variant::from(42);
    /// assert_eq!(int_var.as_node(), None);
    /// ```
    #[inline]
    pub fn as_node(&self) -> Option<&crate::core::scene::Node> {
        match self {
            Variant::Node(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Sprite2D reference from the variant.
    ///
    /// Returns a reference to the Sprite2D if the variant is Sprite2D, otherwise returns None.
    ///
    /// # Returns
    /// Some(&Sprite2D) if the variant is Sprite2D, None otherwise.
    #[inline]
    pub fn as_sprite2d(&self) -> Option<&crate::core::scene::nodes::Sprite2D> {
        match self {
            Variant::Sprite2D(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Label reference from the variant.
    ///
    /// Returns a reference to the Label if the variant is Label, otherwise returns None.
    ///
    /// # Returns
    /// Some(&Label) if the variant is Label, None otherwise.
    #[inline]
    pub fn as_label(&self) -> Option<&crate::core::scene::nodes::ui::Label> {
        match self {
            Variant::Label(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a Button reference from the variant.
    ///
    /// Returns a reference to the Button if the variant is Button, otherwise returns None.
    ///
    /// # Returns
    /// Some(&Button) if the variant is Button, None otherwise.
    #[inline]
    pub fn as_button(&self) -> Option<&crate::core::scene::nodes::ui::Button> {
        match self {
            Variant::Button(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a CollisionShape2D reference from the variant.
    ///
    /// Returns a reference to the CollisionShape2D if the variant is CollisionShape2D, otherwise returns None.
    ///
    /// # Returns
    /// Some(&CollisionShape2D) if the variant is CollisionShape2D, None otherwise.
    #[inline]
    pub fn as_collision_shape2d(&self) -> Option<&crate::core::scene::nodes::physics::CollisionShape2D> {
        match self {
            Variant::CollisionShape2D(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract an Area2D reference from the variant.
    ///
    /// Returns a reference to the Area2D if the variant is Area2D, otherwise returns None.
    ///
    /// # Returns
    /// Some(&Area2D) if the variant is Area2D, None otherwise.
    #[inline]
    pub fn as_area2d(&self) -> Option<&crate::core::scene::nodes::physics::Area2D> {
        match self {
            Variant::Area2D(value) => Some(value),
            _ => None,
        }
    }

    /// ### Attempts to extract a RigidBody2D reference from the variant.
    ///
    /// Returns a reference to the RigidBody2D if the variant is RigidBody2D, otherwise returns None.
    ///
    /// # Returns
    /// Some(&RigidBody2D) if the variant is RigidBody2D, None otherwise.
    #[inline]
    pub fn as_rigid_body2d(&self) -> Option<&crate::core::scene::nodes::physics::RigidBody2D> {
        match self {
            Variant::RigidBody2D(value) => Some(value),
            _ => None,
        }
    }
}

impl fmt::Display for Variant {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Variant::Nil => write!(f, "nil"),
            Variant::Bool(value) => write!(f, "{}", value),
            Variant::Int(value) => write!(f, "{}", value),
            Variant::Float(value) => write!(f, "{}", value),
            Variant::String(value) => write!(f, "\"{}\"", value),
            Variant::Vector2(value) => write!(f, "{}", value),
            Variant::Vector2i(value) => write!(f, "{}", value),
            Variant::Vector3(value) => write!(f, "{}", value),
            Variant::Vector3i(value) => write!(f, "{}", value),
            Variant::Vector4(value) => write!(f, "{}", value),
            Variant::Vector4i(value) => write!(f, "{}", value),
            Variant::Color(value) => write!(f, "{}", value),
            Variant::Rect2(value) => write!(f, "{}", value),
            Variant::Rect2i(value) => write!(f, "{}", value),
            Variant::Transform2D(value) => write!(f, "{}", value),
            Variant::AABB(value) => write!(f, "{}", value),
            Variant::Projection(value) => write!(f, "{}", value),
            Variant::Quaternion(value) => write!(f, "{}", value),
            Variant::Basis(value) => write!(f, "{}", value),
            Variant::Plane(value) => write!(f, "{}", value),
            Variant::Array(value) => {
                write!(f, "[")?;
                for (i, item) in value.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", item)?;
                }
                write!(f, "]")
            }
            Variant::Dictionary(value) => {
                write!(f, "{{")?;
                for (i, (key, val)) in value.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}: {}", key, val)?;
                }
                write!(f, "}}")
            }
            Variant::BoolWrapper(value) => write!(f, "{}", value),
            Variant::IntWrapper(value) => write!(f, "{}", value),
            Variant::FloatWrapper(value) => write!(f, "{}", value),
            Variant::Short(value) => write!(f, "{}", value),
            Variant::Int8(value) => write!(f, "{}", value),
            Variant::Error(value) => write!(f, "{}", value),
            Variant::InputEvent(value) => write!(f, "{}", value),
            Variant::GodotString(value) => write!(f, "{}", value),
            Variant::StringName(value) => write!(f, "{}", value),
            Variant::NodePath(value) => write!(f, "{}", value),
            Variant::RandomNumberGenerator(value) => write!(f, "{}", value),
            Variant::Node(value) => write!(f, "{}", value),
            Variant::Sprite2D(value) => write!(f, "{}", value),
            Variant::Label(value) => write!(f, "{}", value),
            Variant::Button(value) => write!(f, "{}", value),
            Variant::CollisionShape2D(value) => write!(f, "{}", value),
            Variant::Area2D(value) => write!(f, "{}", value),
            Variant::RigidBody2D(value) => write!(f, "{}", value),
        }
    }
}

impl From<bool> for Variant {
    #[inline]
    fn from(value: bool) -> Self {
        Variant::Bool(value)
    }
}

impl From<i64> for Variant {
    #[inline]
    fn from(value: i64) -> Self {
        Variant::Int(value)
    }
}

impl From<i32> for Variant {
    #[inline]
    fn from(value: i32) -> Self {
        Variant::Int(value as i64)
    }
}

impl From<f64> for Variant {
    #[inline]
    fn from(value: f64) -> Self {
        Variant::Float(value)
    }
}

impl From<f32> for Variant {
    #[inline]
    fn from(value: f32) -> Self {
        Variant::Float(value as f64)
    }
}

impl From<String> for Variant {
    #[inline]
    fn from(value: String) -> Self {
        Variant::String(value)
    }
}

impl From<&str> for Variant {
    #[inline]
    fn from(value: &str) -> Self {
        Variant::String(value.to_string())
    }
}

impl From<Vector2> for Variant {
    #[inline]
    fn from(value: Vector2) -> Self {
        Variant::Vector2(value)
    }
}

impl From<Vector3> for Variant {
    #[inline]
    fn from(value: Vector3) -> Self {
        Variant::Vector3(value)
    }
}

impl From<Vector4> for Variant {
    #[inline]
    fn from(value: Vector4) -> Self {
        Variant::Vector4(value)
    }
}

impl From<Vector2i> for Variant {
    #[inline]
    fn from(value: Vector2i) -> Self {
        Variant::Vector2i(value)
    }
}

impl From<Vector3i> for Variant {
    #[inline]
    fn from(value: Vector3i) -> Self {
        Variant::Vector3i(value)
    }
}

impl From<Vector4i> for Variant {
    #[inline]
    fn from(value: Vector4i) -> Self {
        Variant::Vector4i(value)
    }
}

impl From<Color> for Variant {
    #[inline]
    fn from(value: Color) -> Self {
        Variant::Color(value)
    }
}

impl From<Rect2> for Variant {
    #[inline]
    fn from(value: Rect2) -> Self {
        Variant::Rect2(value)
    }
}

impl From<Rect2i> for Variant {
    #[inline]
    fn from(value: Rect2i) -> Self {
        Variant::Rect2i(value)
    }
}

impl From<Transform2D> for Variant {
    #[inline]
    fn from(value: Transform2D) -> Self {
        Variant::Transform2D(value)
    }
}

impl From<AABB> for Variant {
    #[inline]
    fn from(value: AABB) -> Self {
        Variant::AABB(value)
    }
}

impl From<Projection> for Variant {
    #[inline]
    fn from(value: Projection) -> Self {
        Variant::Projection(value)
    }
}

impl From<Quaternion> for Variant {
    #[inline]
    fn from(value: Quaternion) -> Self {
        Variant::Quaternion(value)
    }
}

impl From<Basis> for Variant {
    #[inline]
    fn from(value: Basis) -> Self {
        Variant::Basis(value)
    }
}

impl From<Plane> for Variant {
    #[inline]
    fn from(value: Plane) -> Self {
        Variant::Plane(value)
    }
}

// From implementations for wrapper types
impl From<Bool> for Variant {
    #[inline]
    fn from(value: Bool) -> Self {
        Variant::BoolWrapper(value)
    }
}

impl From<Int> for Variant {
    #[inline]
    fn from(value: Int) -> Self {
        Variant::IntWrapper(value)
    }
}

impl From<Float> for Variant {
    #[inline]
    fn from(value: Float) -> Self {
        Variant::FloatWrapper(value)
    }
}

impl From<Short> for Variant {
    #[inline]
    fn from(value: Short) -> Self {
        Variant::Short(value)
    }
}

impl From<Int8> for Variant {
    #[inline]
    fn from(value: Int8) -> Self {
        Variant::Int8(value)
    }
}

impl From<Error> for Variant {
    #[inline]
    fn from(value: Error) -> Self {
        Variant::Error(value)
    }
}

impl From<InputEvent> for Variant {
    #[inline]
    fn from(value: InputEvent) -> Self {
        Variant::InputEvent(value)
    }
}

impl From<super::string::String> for Variant {
    #[inline]
    fn from(value: super::string::String) -> Self {
        Variant::GodotString(value)
    }
}

impl From<super::string_name::StringName> for Variant {
    #[inline]
    fn from(value: super::string_name::StringName) -> Self {
        Variant::StringName(value)
    }
}

impl From<crate::core::scene::NodePath> for Variant {
    #[inline]
    fn from(value: crate::core::scene::NodePath) -> Self {
        Variant::NodePath(value)
    }
}

impl From<RandomNumberGenerator> for Variant {
    #[inline]
    fn from(value: RandomNumberGenerator) -> Self {
        Variant::RandomNumberGenerator(value)
    }
}

impl From<crate::core::scene::Node> for Variant {
    #[inline]
    fn from(value: crate::core::scene::Node) -> Self {
        Variant::Node(value)
    }
}

impl From<crate::core::scene::nodes::Sprite2D> for Variant {
    #[inline]
    fn from(value: crate::core::scene::nodes::Sprite2D) -> Self {
        Variant::Sprite2D(value)
    }
}

impl From<crate::core::scene::nodes::ui::Label> for Variant {
    #[inline]
    fn from(value: crate::core::scene::nodes::ui::Label) -> Self {
        Variant::Label(value)
    }
}

impl From<crate::core::scene::nodes::ui::Button> for Variant {
    #[inline]
    fn from(value: crate::core::scene::nodes::ui::Button) -> Self {
        Variant::Button(value)
    }
}

impl From<crate::core::scene::nodes::physics::CollisionShape2D> for Variant {
    #[inline]
    fn from(value: crate::core::scene::nodes::physics::CollisionShape2D) -> Self {
        Variant::CollisionShape2D(value)
    }
}

impl From<crate::core::scene::nodes::physics::Area2D> for Variant {
    #[inline]
    fn from(value: crate::core::scene::nodes::physics::Area2D) -> Self {
        Variant::Area2D(value)
    }
}

impl From<crate::core::scene::nodes::physics::RigidBody2D> for Variant {
    #[inline]
    fn from(value: crate::core::scene::nodes::physics::RigidBody2D) -> Self {
        Variant::RigidBody2D(value)
    }
}

impl From<Vec<Variant>> for Variant {
    #[inline]
    fn from(value: Vec<Variant>) -> Self {
        Variant::Array(value)
    }
}

impl From<HashMap<Variant, Variant>> for Variant {
    #[inline]
    fn from(value: HashMap<Variant, Variant>) -> Self {
        Variant::Dictionary(value)
    }
}

impl From<super::Array> for Variant {
    #[inline]
    fn from(value: super::Array) -> Self {
        Variant::Array(value.into())
    }
}

impl From<super::Dictionary> for Variant {
    #[inline]
    fn from(value: super::Dictionary) -> Self {
        Variant::Dictionary(value.into())
    }
}

impl std::hash::Hash for Variant {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        std::mem::discriminant(self).hash(state);
        match self {
            Variant::Nil => {}
            Variant::Bool(value) => value.hash(state),
            Variant::Int(value) => value.hash(state),
            Variant::Float(value) => value.to_bits().hash(state),
            Variant::String(value) => value.hash(state),
            Variant::Vector2(value) => {
                value.x.to_bits().hash(state);
                value.y.to_bits().hash(state);
            }
            Variant::Vector2i(value) => {
                value.x.hash(state);
                value.y.hash(state);
            }
            Variant::Vector3(value) => {
                value.x.to_bits().hash(state);
                value.y.to_bits().hash(state);
                value.z.to_bits().hash(state);
            }
            Variant::Vector3i(value) => {
                value.x.hash(state);
                value.y.hash(state);
                value.z.hash(state);
            }
            Variant::Vector4(value) => {
                value.x.to_bits().hash(state);
                value.y.to_bits().hash(state);
                value.z.to_bits().hash(state);
                value.w.to_bits().hash(state);
            }
            Variant::Vector4i(value) => {
                value.x.hash(state);
                value.y.hash(state);
                value.z.hash(state);
                value.w.hash(state);
            }
            Variant::Color(value) => {
                value.r.to_bits().hash(state);
                value.g.to_bits().hash(state);
                value.b.to_bits().hash(state);
                value.a.to_bits().hash(state);
            }
            Variant::Rect2(value) => {
                value.position.x.to_bits().hash(state);
                value.position.y.to_bits().hash(state);
                value.size.x.to_bits().hash(state);
                value.size.y.to_bits().hash(state);
            }
            Variant::Rect2i(value) => {
                value.position.x.hash(state);
                value.position.y.hash(state);
                value.size.x.hash(state);
                value.size.y.hash(state);
            }
            Variant::Transform2D(value) => {
                value.x.x.to_bits().hash(state);
                value.x.y.to_bits().hash(state);
                value.y.x.to_bits().hash(state);
                value.y.y.to_bits().hash(state);
                value.origin.x.to_bits().hash(state);
                value.origin.y.to_bits().hash(state);
            }
            Variant::AABB(value) => {
                value.position.x.to_bits().hash(state);
                value.position.y.to_bits().hash(state);
                value.position.z.to_bits().hash(state);
                value.size.x.to_bits().hash(state);
                value.size.y.to_bits().hash(state);
                value.size.z.to_bits().hash(state);
            }
            Variant::Projection(value) => {
                value.x.x.to_bits().hash(state);
                value.x.y.to_bits().hash(state);
                value.x.z.to_bits().hash(state);
                value.x.w.to_bits().hash(state);
                value.y.x.to_bits().hash(state);
                value.y.y.to_bits().hash(state);
                value.y.z.to_bits().hash(state);
                value.y.w.to_bits().hash(state);
                value.z.x.to_bits().hash(state);
                value.z.y.to_bits().hash(state);
                value.z.z.to_bits().hash(state);
                value.z.w.to_bits().hash(state);
                value.w.x.to_bits().hash(state);
                value.w.y.to_bits().hash(state);
                value.w.z.to_bits().hash(state);
                value.w.w.to_bits().hash(state);
            }
            Variant::Quaternion(value) => {
                value.x.to_bits().hash(state);
                value.y.to_bits().hash(state);
                value.z.to_bits().hash(state);
                value.w.to_bits().hash(state);
            }
            Variant::Basis(value) => {
                value.x.x.to_bits().hash(state);
                value.x.y.to_bits().hash(state);
                value.x.z.to_bits().hash(state);
                value.y.x.to_bits().hash(state);
                value.y.y.to_bits().hash(state);
                value.y.z.to_bits().hash(state);
                value.z.x.to_bits().hash(state);
                value.z.y.to_bits().hash(state);
                value.z.z.to_bits().hash(state);
            }
            Variant::Plane(value) => {
                value.normal.x.to_bits().hash(state);
                value.normal.y.to_bits().hash(state);
                value.normal.z.to_bits().hash(state);
                value.d.to_bits().hash(state);
            }
            Variant::Array(value) => {
                value.len().hash(state);
                for item in value {
                    item.hash(state);
                }
            }
            Variant::Dictionary(value) => {
                value.len().hash(state);
                // Note: HashMap iteration order is not guaranteed, but for hashing
                // we need a consistent order. We'll sort by hash of keys.
                let mut items: Vec<_> = value.iter().collect();
                items.sort_by_key(|(k, _)| {
                    let mut hasher = std::collections::hash_map::DefaultHasher::new();
                    k.hash(&mut hasher);
                    hasher.finish()
                });
                for (key, val) in items {
                    key.hash(state);
                    val.hash(state);
                }
            }
            Variant::BoolWrapper(value) => value.hash(state),
            Variant::IntWrapper(value) => value.hash(state),
            Variant::FloatWrapper(value) => value.hash(state),
            Variant::Short(value) => value.hash(state),
            Variant::Int8(value) => value.hash(state),
            Variant::Error(value) => value.hash(state),
            Variant::InputEvent(value) => {
                // Hash based on event type and key properties for deterministic hashing
                match value {
                    InputEvent::Key { keycode, pressed, .. } => {
                        keycode.hash(state);
                        pressed.hash(state);
                    }
                    InputEvent::MouseButton { button, pressed, .. } => {
                        button.hash(state);
                        pressed.hash(state);
                    }
                    InputEvent::MouseMotion { motion, .. } => {
                        // Hash position for motion events
                        motion.position().x.to_bits().hash(state);
                        motion.position().y.to_bits().hash(state);
                    }
                    InputEvent::JoypadButton { button, pressed, device, .. } => {
                        button.hash(state);
                        pressed.hash(state);
                        device.hash(state);
                    }
                    InputEvent::JoypadMotion { motion, .. } => {
                        motion.axis().hash(state);
                        motion.value().to_bits().hash(state);
                        motion.device().hash(state);
                    }
                    InputEvent::Action { action, pressed, .. } => {
                        action.hash(state);
                        pressed.hash(state);
                    }
                }
            }
            Variant::GodotString(value) => value.hash(state),
            Variant::StringName(value) => value.hash(state),
            Variant::NodePath(value) => value.hash(state),
            Variant::RandomNumberGenerator(value) => {
                // Hash based on the RNG state for consistency
                let rng_state = value.get_state();
                rng_state.hash(state);
            }
            Variant::Node(value) => {
                // Hash based on the unique node ID
                value.get_id().hash(state);
            }
            Variant::Sprite2D(value) => {
                // Hash based on the underlying node ID
                value.base().base().get_id().hash(state);
            }
            Variant::Label(value) => {
                // Hash based on the underlying node ID
                value.base().base().get_id().hash(state);
            }
            Variant::Button(value) => {
                // Hash based on the underlying node ID
                value.base().base().get_id().hash(state);
            }
            Variant::CollisionShape2D(value) => {
                // Hash based on the underlying node ID
                value.base().base().get_id().hash(state);
            }
            Variant::Area2D(value) => {
                // Hash based on the underlying node ID
                value.base().base().get_id().hash(state);
            }
            Variant::RigidBody2D(value) => {
                // Hash based on the underlying node ID
                value.base().base().get_id().hash(state);
            }
        }
    }
}

impl Eq for Variant {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::math::{Vector2, Vector2i, Vector3, Vector3i, Vector4i, Rect2, Rect2i, Transform2D, AABB, Projection, Quaternion, Basis, Plane};
    use crate::core::error::Error;
    use super::{Int, Float, Bool, Short, Int8};
    use crate::core::variant::{Array, Dictionary};
    use crate::core::variant::{String as GodotString, StringName};
    use crate::core::scene::NodePath;

    #[test]
    fn test_variant_new_mathematical_types() {
        // Test Vector2i
        let v2i = Vector2i::new(10, 20);
        let var_v2i = Variant::from(v2i);
        assert!(var_v2i.is_vector2i());
        assert!(!var_v2i.is_vector2());
        assert_eq!(var_v2i.as_vector2i(), Some(v2i));
        assert_eq!(var_v2i.as_vector2(), None);

        // Test Vector3i
        let v3i = Vector3i::new(1, 2, 3);
        let var_v3i = Variant::from(v3i);
        assert!(var_v3i.is_vector3i());
        assert!(!var_v3i.is_vector3());
        assert_eq!(var_v3i.as_vector3i(), Some(v3i));
        assert_eq!(var_v3i.as_vector3(), None);

        // Test Vector4i
        let v4i = Vector4i::new(5, 6, 7, 8);
        let var_v4i = Variant::from(v4i);
        assert!(var_v4i.is_vector4i());
        assert!(!var_v4i.is_vector4());
        assert_eq!(var_v4i.as_vector4i(), Some(v4i));
        assert_eq!(var_v4i.as_vector4(), None);

        // Test Rect2
        let rect2 = Rect2::new(1.0, 2.0, 10.0, 20.0);
        let var_rect2 = Variant::from(rect2);
        assert!(var_rect2.is_rect2());
        assert!(!var_rect2.is_rect2i());
        assert_eq!(var_rect2.as_rect2(), Some(rect2));
        assert_eq!(var_rect2.as_rect2i(), None);

        // Test Rect2i
        let rect2i = Rect2i::new(5, 10, 15, 25);
        let var_rect2i = Variant::from(rect2i);
        assert!(var_rect2i.is_rect2i());
        assert!(!var_rect2i.is_rect2());
        assert_eq!(var_rect2i.as_rect2i(), Some(rect2i));
        assert_eq!(var_rect2i.as_rect2(), None);

        // Test Transform2D
        let transform = Transform2D::from_translation(Vector2::new(5.0, 10.0));
        let var_transform = Variant::from(transform);
        assert!(var_transform.is_transform2d());
        assert!(!var_transform.is_vector2());
        assert_eq!(var_transform.as_transform2d(), Some(transform));
        assert_eq!(var_transform.as_vector2(), None);
    }

    #[test]
    fn test_variant_type_checking_comprehensive() {
        // Test all type checking methods return false for wrong types
        let v2i = Variant::from(Vector2i::new(1, 2));
        assert!(!v2i.is_nil());
        assert!(!v2i.is_bool());
        assert!(!v2i.is_int());
        assert!(!v2i.is_float());
        assert!(!v2i.is_string());
        assert!(!v2i.is_vector2());
        assert!(v2i.is_vector2i());
        assert!(!v2i.is_vector3());
        assert!(!v2i.is_vector3i());
        assert!(!v2i.is_vector4());
        assert!(!v2i.is_vector4i());
        assert!(!v2i.is_color());
        assert!(!v2i.is_rect2());
        assert!(!v2i.is_rect2i());
        assert!(!v2i.is_transform2d());
        assert!(!v2i.is_array());
        assert!(!v2i.is_dictionary());

        // Test Rect2
        let rect2 = Variant::from(Rect2::new(0.0, 0.0, 1.0, 1.0));
        assert!(!rect2.is_nil());
        assert!(!rect2.is_vector2());
        assert!(!rect2.is_vector2i());
        assert!(rect2.is_rect2());
        assert!(!rect2.is_rect2i());
        assert!(!rect2.is_transform2d());

        // Test Transform2D
        let transform = Variant::from(Transform2D::IDENTITY);
        assert!(!transform.is_nil());
        assert!(!transform.is_rect2());
        assert!(!transform.is_rect2i());
        assert!(transform.is_transform2d());
        assert!(!transform.is_vector2());
    }

    #[test]
    fn test_variant_display_new_types() {
        // Test Vector2i display
        let v2i = Variant::from(Vector2i::new(42, -17));
        let display_str = format!("{}", v2i);
        assert!(display_str.contains("42"));
        assert!(display_str.contains("-17"));

        // Test Vector3i display
        let v3i = Variant::from(Vector3i::new(1, 2, 3));
        let display_str = format!("{}", v3i);
        assert!(display_str.contains("1"));
        assert!(display_str.contains("2"));
        assert!(display_str.contains("3"));

        // Test Vector4i display
        let v4i = Variant::from(Vector4i::new(10, 20, 30, 40));
        let display_str = format!("{}", v4i);
        assert!(display_str.contains("10"));
        assert!(display_str.contains("20"));
        assert!(display_str.contains("30"));
        assert!(display_str.contains("40"));

        // Test Rect2 display
        let rect2 = Variant::from(Rect2::new(1.5, 2.5, 10.0, 20.0));
        let display_str = format!("{}", rect2);
        assert!(display_str.contains("1.5"));
        assert!(display_str.contains("2.5"));
        assert!(display_str.contains("10"));
        assert!(display_str.contains("20"));

        // Test Rect2i display
        let rect2i = Variant::from(Rect2i::new(5, 10, 15, 25));
        let display_str = format!("{}", rect2i);
        assert!(display_str.contains("5"));
        assert!(display_str.contains("10"));
        assert!(display_str.contains("15"));
        assert!(display_str.contains("25"));

        // Test Transform2D display
        let transform = Variant::from(Transform2D::IDENTITY);
        let display_str = format!("{}", transform);
        // Should contain identity matrix elements
        assert!(display_str.contains("1"));
        assert!(display_str.contains("0"));
    }

    #[test]
    fn test_variant_hash_new_types() {
        use std::collections::HashMap;

        let mut map = HashMap::new();

        // Test Vector2i as key
        let v2i_key = Variant::from(Vector2i::new(10, 20));
        map.insert(v2i_key.clone(), "vector2i_value".into());
        assert_eq!(map.get(&v2i_key), Some(&Variant::from("vector2i_value")));

        // Test Vector3i as key
        let v3i_key = Variant::from(Vector3i::new(1, 2, 3));
        map.insert(v3i_key.clone(), "vector3i_value".into());
        assert_eq!(map.get(&v3i_key), Some(&Variant::from("vector3i_value")));

        // Test Vector4i as key
        let v4i_key = Variant::from(Vector4i::new(5, 6, 7, 8));
        map.insert(v4i_key.clone(), "vector4i_value".into());
        assert_eq!(map.get(&v4i_key), Some(&Variant::from("vector4i_value")));

        // Test Rect2 as key
        let rect2_key = Variant::from(Rect2::new(1.0, 2.0, 3.0, 4.0));
        map.insert(rect2_key.clone(), "rect2_value".into());
        assert_eq!(map.get(&rect2_key), Some(&Variant::from("rect2_value")));

        // Test Rect2i as key
        let rect2i_key = Variant::from(Rect2i::new(10, 20, 30, 40));
        map.insert(rect2i_key.clone(), "rect2i_value".into());
        assert_eq!(map.get(&rect2i_key), Some(&Variant::from("rect2i_value")));

        // Test Transform2D as key
        let transform_key = Variant::from(Transform2D::IDENTITY);
        map.insert(transform_key.clone(), "transform_value".into());
        assert_eq!(map.get(&transform_key), Some(&Variant::from("transform_value")));

        // Verify all keys are present
        assert_eq!(map.len(), 6);
    }

    #[test]
    fn test_variant_equality_new_types() {
        // Test Vector2i equality
        let v2i1 = Variant::from(Vector2i::new(10, 20));
        let v2i2 = Variant::from(Vector2i::new(10, 20));
        let v2i3 = Variant::from(Vector2i::new(10, 21));
        assert_eq!(v2i1, v2i2);
        assert_ne!(v2i1, v2i3);

        // Test Vector3i equality
        let v3i1 = Variant::from(Vector3i::new(1, 2, 3));
        let v3i2 = Variant::from(Vector3i::new(1, 2, 3));
        let v3i3 = Variant::from(Vector3i::new(1, 2, 4));
        assert_eq!(v3i1, v3i2);
        assert_ne!(v3i1, v3i3);

        // Test Vector4i equality
        let v4i1 = Variant::from(Vector4i::new(5, 6, 7, 8));
        let v4i2 = Variant::from(Vector4i::new(5, 6, 7, 8));
        let v4i3 = Variant::from(Vector4i::new(5, 6, 7, 9));
        assert_eq!(v4i1, v4i2);
        assert_ne!(v4i1, v4i3);

        // Test Rect2 equality
        let rect2_1 = Variant::from(Rect2::new(1.0, 2.0, 3.0, 4.0));
        let rect2_2 = Variant::from(Rect2::new(1.0, 2.0, 3.0, 4.0));
        let rect2_3 = Variant::from(Rect2::new(1.0, 2.0, 3.0, 4.1));
        assert_eq!(rect2_1, rect2_2);
        assert_ne!(rect2_1, rect2_3);

        // Test Rect2i equality
        let rect2i_1 = Variant::from(Rect2i::new(10, 20, 30, 40));
        let rect2i_2 = Variant::from(Rect2i::new(10, 20, 30, 40));
        let rect2i_3 = Variant::from(Rect2i::new(10, 20, 30, 41));
        assert_eq!(rect2i_1, rect2i_2);
        assert_ne!(rect2i_1, rect2i_3);

        // Test Transform2D equality
        let transform1 = Variant::from(Transform2D::IDENTITY);
        let transform2 = Variant::from(Transform2D::IDENTITY);
        let transform3 = Variant::from(Transform2D::from_translation(Vector2::new(1.0, 0.0)));
        assert_eq!(transform1, transform2);
        assert_ne!(transform1, transform3);
    }

    #[test]
    fn test_variant_cross_type_inequality() {
        // Test that different mathematical types are not equal
        let v2 = Variant::from(Vector2::new(1.0, 2.0));
        let v2i = Variant::from(Vector2i::new(1, 2));
        let v3 = Variant::from(Vector3::new(1.0, 2.0, 0.0));
        let v3i = Variant::from(Vector3i::new(1, 2, 0));
        let rect2 = Variant::from(Rect2::new(1.0, 2.0, 0.0, 0.0));
        let rect2i = Variant::from(Rect2i::new(1, 2, 0, 0));
        let transform = Variant::from(Transform2D::IDENTITY);

        // All should be different from each other
        assert_ne!(v2, v2i);
        assert_ne!(v2, v3);
        assert_ne!(v2, v3i);
        assert_ne!(v2, rect2);
        assert_ne!(v2, rect2i);
        assert_ne!(v2, transform);

        assert_ne!(v2i, v3);
        assert_ne!(v2i, v3i);
        assert_ne!(v2i, rect2);
        assert_ne!(v2i, rect2i);
        assert_ne!(v2i, transform);

        assert_ne!(v3, v3i);
        assert_ne!(v3, rect2);
        assert_ne!(v3, rect2i);
        assert_ne!(v3, transform);

        assert_ne!(v3i, rect2);
        assert_ne!(v3i, rect2i);
        assert_ne!(v3i, transform);

        assert_ne!(rect2, rect2i);
        assert_ne!(rect2, transform);

        assert_ne!(rect2i, transform);
    }

    #[test]
    fn test_variant_clone_new_types() {
        // Test that all new types can be cloned properly
        let v2i = Variant::from(Vector2i::new(10, 20));
        let v2i_clone = v2i.clone();
        assert_eq!(v2i, v2i_clone);
        assert_eq!(v2i.as_vector2i(), v2i_clone.as_vector2i());

        let v3i = Variant::from(Vector3i::new(1, 2, 3));
        let v3i_clone = v3i.clone();
        assert_eq!(v3i, v3i_clone);
        assert_eq!(v3i.as_vector3i(), v3i_clone.as_vector3i());

        let v4i = Variant::from(Vector4i::new(5, 6, 7, 8));
        let v4i_clone = v4i.clone();
        assert_eq!(v4i, v4i_clone);
        assert_eq!(v4i.as_vector4i(), v4i_clone.as_vector4i());

        let rect2 = Variant::from(Rect2::new(1.0, 2.0, 3.0, 4.0));
        let rect2_clone = rect2.clone();
        assert_eq!(rect2, rect2_clone);
        assert_eq!(rect2.as_rect2(), rect2_clone.as_rect2());

        let rect2i = Variant::from(Rect2i::new(10, 20, 30, 40));
        let rect2i_clone = rect2i.clone();
        assert_eq!(rect2i, rect2i_clone);
        assert_eq!(rect2i.as_rect2i(), rect2i_clone.as_rect2i());

        let transform = Variant::from(Transform2D::IDENTITY);
        let transform_clone = transform.clone();
        assert_eq!(transform, transform_clone);
        assert_eq!(transform.as_transform2d(), transform_clone.as_transform2d());
    }

    #[test]
    fn test_variant_debug_new_types() {
        // Test that all new types have proper Debug formatting
        let v2i = Variant::from(Vector2i::new(10, 20));
        let debug_str = format!("{:?}", v2i);
        assert!(debug_str.contains("Vector2i"));
        assert!(debug_str.contains("10"));
        assert!(debug_str.contains("20"));

        let rect2 = Variant::from(Rect2::new(1.0, 2.0, 3.0, 4.0));
        let debug_str = format!("{:?}", rect2);
        assert!(debug_str.contains("Rect2"));

        let transform = Variant::from(Transform2D::IDENTITY);
        let debug_str = format!("{:?}", transform);
        assert!(debug_str.contains("Transform2D"));
    }

    #[test]
    fn test_variant_primitive_wrapper_types() {
        // Test Bool wrapper
        let bool_wrapper = Variant::from(Bool::new(true));
        assert!(bool_wrapper.is_bool_wrapper());
        assert!(!bool_wrapper.is_bool());
        assert_eq!(bool_wrapper.as_bool_wrapper(), Some(Bool::new(true)));
        assert_eq!(bool_wrapper.as_bool(), None);

        // Test Int wrapper
        let int_wrapper = Variant::from(Int::new(42));
        assert!(int_wrapper.is_int_wrapper());
        assert!(!int_wrapper.is_int());
        assert_eq!(int_wrapper.as_int_wrapper(), Some(Int::new(42)));
        assert_eq!(int_wrapper.as_int(), None);

        // Test Float wrapper
        let float_wrapper = Variant::from(Float::new(3.14));
        assert!(float_wrapper.is_float_wrapper());
        assert!(!float_wrapper.is_float());
        assert_eq!(float_wrapper.as_float_wrapper(), Some(Float::new(3.14)));
        assert_eq!(float_wrapper.as_float(), None);

        // Test Short
        let short_val = Variant::from(Short::new(1000));
        assert!(short_val.is_short());
        assert!(!short_val.is_int());
        assert_eq!(short_val.as_short(), Some(Short::new(1000)));
        assert_eq!(short_val.as_int(), None);

        // Test Int8
        let int8_val = Variant::from(Int8::new(100));
        assert!(int8_val.is_int8());
        assert!(!int8_val.is_int());
        assert_eq!(int8_val.as_int8(), Some(Int8::new(100)));
        assert_eq!(int8_val.as_int(), None);
    }

    #[test]
    fn test_variant_primitive_wrapper_display() {
        // Test wrapper type display
        let bool_wrapper = Variant::from(Bool::new(true));
        assert_eq!(format!("{}", bool_wrapper), "true");

        let int_wrapper = Variant::from(Int::new(42));
        assert_eq!(format!("{}", int_wrapper), "42");

        let float_wrapper = Variant::from(Float::new(3.14));
        assert_eq!(format!("{}", float_wrapper), "3.14");

        let short_val = Variant::from(Short::new(1000));
        assert_eq!(format!("{}", short_val), "1000");

        let int8_val = Variant::from(Int8::new(100));
        assert_eq!(format!("{}", int8_val), "100");
    }

    #[test]
    fn test_variant_primitive_wrapper_hash() {
        use std::collections::HashMap;

        let mut map = HashMap::new();

        // Test wrapper types as keys
        let bool_key = Variant::from(Bool::new(true));
        let int_key = Variant::from(Int::new(42));
        let float_key = Variant::from(Float::new(3.14));
        let short_key = Variant::from(Short::new(1000));
        let int8_key = Variant::from(Int8::new(100));

        map.insert(bool_key.clone(), "bool_value".into());
        map.insert(int_key.clone(), "int_value".into());
        map.insert(float_key.clone(), "float_value".into());
        map.insert(short_key.clone(), "short_value".into());
        map.insert(int8_key.clone(), "int8_value".into());

        assert_eq!(map.get(&bool_key), Some(&Variant::from("bool_value")));
        assert_eq!(map.get(&int_key), Some(&Variant::from("int_value")));
        assert_eq!(map.get(&float_key), Some(&Variant::from("float_value")));
        assert_eq!(map.get(&short_key), Some(&Variant::from("short_value")));
        assert_eq!(map.get(&int8_key), Some(&Variant::from("int8_value")));
    }

    #[test]
    fn test_variant_primitive_wrapper_equality() {
        // Test wrapper type equality
        let bool1 = Variant::from(Bool::new(true));
        let bool2 = Variant::from(Bool::new(true));
        let bool3 = Variant::from(Bool::new(false));
        assert_eq!(bool1, bool2);
        assert_ne!(bool1, bool3);

        let int1 = Variant::from(Int::new(42));
        let int2 = Variant::from(Int::new(42));
        let int3 = Variant::from(Int::new(17));
        assert_eq!(int1, int2);
        assert_ne!(int1, int3);

        let float1 = Variant::from(Float::new(3.14));
        let float2 = Variant::from(Float::new(3.14));
        let float3 = Variant::from(Float::new(2.71));
        assert_eq!(float1, float2);
        assert_ne!(float1, float3);

        // Test that wrapper types are different from primitive types
        let primitive_bool = Variant::from(true);
        let wrapper_bool = Variant::from(Bool::new(true));
        assert_ne!(primitive_bool, wrapper_bool);

        let primitive_int = Variant::from(42i64);
        let wrapper_int = Variant::from(Int::new(42));
        assert_ne!(primitive_int, wrapper_int);

        let primitive_float = Variant::from(3.14f64);
        let wrapper_float = Variant::from(Float::new(3.14));
        assert_ne!(primitive_float, wrapper_float);
    }

    #[test]
    fn test_variant_primitive_wrapper_clone() {
        // Test wrapper type cloning
        let bool_wrapper = Variant::from(Bool::new(true));
        let bool_clone = bool_wrapper.clone();
        assert_eq!(bool_wrapper, bool_clone);
        assert_eq!(bool_wrapper.as_bool_wrapper(), bool_clone.as_bool_wrapper());

        let int_wrapper = Variant::from(Int::new(42));
        let int_clone = int_wrapper.clone();
        assert_eq!(int_wrapper, int_clone);
        assert_eq!(int_wrapper.as_int_wrapper(), int_clone.as_int_wrapper());

        let float_wrapper = Variant::from(Float::new(3.14));
        let float_clone = float_wrapper.clone();
        assert_eq!(float_wrapper, float_clone);
        assert_eq!(float_wrapper.as_float_wrapper(), float_clone.as_float_wrapper());

        let short_val = Variant::from(Short::new(1000));
        let short_clone = short_val.clone();
        assert_eq!(short_val, short_clone);
        assert_eq!(short_val.as_short(), short_clone.as_short());

        let int8_val = Variant::from(Int8::new(100));
        let int8_clone = int8_val.clone();
        assert_eq!(int8_val, int8_clone);
        assert_eq!(int8_val.as_int8(), int8_clone.as_int8());
    }

    #[test]
    fn test_variant_error_type() {
        // Test Error variant creation and type checking
        let ok_error = Variant::from(Error::OK);
        assert!(ok_error.is_error());
        assert!(!ok_error.is_bool());
        assert!(!ok_error.is_int());
        assert!(!ok_error.is_string());
        assert_eq!(ok_error.as_error(), Some(Error::OK));
        assert_eq!(ok_error.as_bool(), None);

        let file_error = Variant::from(Error::ERR_FILE_NOT_FOUND);
        assert!(file_error.is_error());
        assert_eq!(file_error.as_error(), Some(Error::ERR_FILE_NOT_FOUND));

        let timeout_error = Variant::from(Error::ERR_TIMEOUT);
        assert!(timeout_error.is_error());
        assert_eq!(timeout_error.as_error(), Some(Error::ERR_TIMEOUT));

        let printer_error = Variant::from(Error::ERR_PRINTER_ON_FIRE);
        assert!(printer_error.is_error());
        assert_eq!(printer_error.as_error(), Some(Error::ERR_PRINTER_ON_FIRE));
    }

    #[test]
    fn test_variant_error_display() {
        // Test Error variant display
        let ok_error = Variant::from(Error::OK);
        assert_eq!(format!("{}", ok_error), "Success");

        let failed_error = Variant::from(Error::FAILED);
        assert_eq!(format!("{}", failed_error), "Operation failed");

        let file_error = Variant::from(Error::ERR_FILE_NOT_FOUND);
        assert_eq!(format!("{}", file_error), "File not found");

        let memory_error = Variant::from(Error::ERR_OUT_OF_MEMORY);
        assert_eq!(format!("{}", memory_error), "Out of memory");

        let timeout_error = Variant::from(Error::ERR_TIMEOUT);
        assert_eq!(format!("{}", timeout_error), "Operation timed out");

        let printer_error = Variant::from(Error::ERR_PRINTER_ON_FIRE);
        assert_eq!(format!("{}", printer_error), "Printer on fire");
    }

    #[test]
    fn test_variant_error_hash() {
        use std::collections::HashMap;

        let mut map = HashMap::new();

        // Test Error variants as keys
        let ok_key = Variant::from(Error::OK);
        let failed_key = Variant::from(Error::FAILED);
        let file_key = Variant::from(Error::ERR_FILE_NOT_FOUND);
        let timeout_key = Variant::from(Error::ERR_TIMEOUT);

        map.insert(ok_key.clone(), "success".into());
        map.insert(failed_key.clone(), "failure".into());
        map.insert(file_key.clone(), "file_not_found".into());
        map.insert(timeout_key.clone(), "timeout".into());

        assert_eq!(map.get(&ok_key), Some(&Variant::from("success")));
        assert_eq!(map.get(&failed_key), Some(&Variant::from("failure")));
        assert_eq!(map.get(&file_key), Some(&Variant::from("file_not_found")));
        assert_eq!(map.get(&timeout_key), Some(&Variant::from("timeout")));
    }

    #[test]
    fn test_variant_error_equality() {
        // Test Error variant equality
        let ok1 = Variant::from(Error::OK);
        let ok2 = Variant::from(Error::OK);
        let failed = Variant::from(Error::FAILED);
        let file_error = Variant::from(Error::ERR_FILE_NOT_FOUND);

        assert_eq!(ok1, ok2);
        assert_ne!(ok1, failed);
        assert_ne!(failed, file_error);
        assert_ne!(ok1, file_error);

        // Test that Error variants are different from other types
        let bool_var = Variant::from(true);
        let int_var = Variant::from(42i64);
        let string_var = Variant::from("error");

        assert_ne!(ok1, bool_var);
        assert_ne!(failed, int_var);
        assert_ne!(file_error, string_var);
    }

    #[test]
    fn test_variant_error_clone() {
        // Test Error variant cloning
        let original = Variant::from(Error::ERR_FILE_NOT_FOUND);
        let cloned = original.clone();

        assert_eq!(original, cloned);
        assert_eq!(original.as_error(), cloned.as_error());
        assert!(original.is_error());
        assert!(cloned.is_error());
    }

    #[test]
    fn test_variant_error_in_collections() {
        // Test Error variants in Array
        let mut error_array = Array::new();
        error_array.push_back(Error::OK.into());
        error_array.push_back(Error::FAILED.into());
        error_array.push_back(Error::ERR_FILE_NOT_FOUND.into());
        error_array.push_back(Error::ERR_TIMEOUT.into());

        assert_eq!(error_array.size(), 4);

        // Check that all items are Error variants
        for item in error_array.iter() {
            assert!(item.is_error());
        }

        // Test specific error extraction
        assert_eq!(error_array.get(0).unwrap().as_error(), Some(Error::OK));
        assert_eq!(error_array.get(1).unwrap().as_error(), Some(Error::FAILED));
        assert_eq!(error_array.get(2).unwrap().as_error(), Some(Error::ERR_FILE_NOT_FOUND));
        assert_eq!(error_array.get(3).unwrap().as_error(), Some(Error::ERR_TIMEOUT));

        // Test Error variants in Dictionary
        let mut error_dict = Dictionary::new();
        error_dict.set("result".into(), Error::OK.into());
        error_dict.set("file_op".into(), Error::ERR_FILE_NOT_FOUND.into());
        error_dict.set("network_op".into(), Error::ERR_TIMEOUT.into());
        error_dict.set("memory_op".into(), Error::ERR_OUT_OF_MEMORY.into());

        assert_eq!(error_dict.size(), 4);

        // Test key lookup
        assert_eq!(error_dict.get(&"result".into()).unwrap().as_error(), Some(Error::OK));
        assert_eq!(error_dict.get(&"file_op".into()).unwrap().as_error(), Some(Error::ERR_FILE_NOT_FOUND));
        assert_eq!(error_dict.get(&"network_op".into()).unwrap().as_error(), Some(Error::ERR_TIMEOUT));
        assert_eq!(error_dict.get(&"memory_op".into()).unwrap().as_error(), Some(Error::ERR_OUT_OF_MEMORY));
    }

    #[test]
    fn test_variant_error_comprehensive_coverage() {
        // Test a comprehensive set of error codes
        let error_codes = [
            Error::OK,
            Error::FAILED,
            Error::ERR_UNAVAILABLE,
            Error::ERR_UNCONFIGURED,
            Error::ERR_UNAUTHORIZED,
            Error::ERR_INVALID_PARAMETER,
            Error::ERR_OUT_OF_MEMORY,
            Error::ERR_FILE_NOT_FOUND,
            Error::ERR_FILE_BAD_DRIVE,
            Error::ERR_FILE_BAD_PATH,
            Error::ERR_FILE_NO_PERMISSION,
            Error::ERR_FILE_ALREADY_IN_USE,
            Error::ERR_FILE_CANT_OPEN,
            Error::ERR_FILE_CANT_WRITE,
            Error::ERR_FILE_CANT_READ,
            Error::ERR_FILE_UNRECOGNIZED,
            Error::ERR_FILE_CORRUPT,
            Error::ERR_FILE_EOF,
            Error::ERR_CANT_RESOLVE,
            Error::ERR_CANT_CONNECT,
            Error::ERR_CANT_CREATE,
            Error::ERR_QUERY_FAILED,
            Error::ERR_ALREADY_IN_USE,
            Error::ERR_LOCKED,
            Error::ERR_TIMEOUT,
            Error::ERR_CANT_ACQUIRE_RESOURCE,
            Error::ERR_INVALID_DATA,
            Error::ERR_INVALID_DECLARATION,
            Error::ERR_DUPLICATE_SYMBOL,
            Error::ERR_PARSE_ERROR,
            Error::ERR_BUSY,
            Error::ERR_SKIP,
            Error::ERR_HELP,
            Error::ERR_BUG,
            Error::ERR_PRINTER_ON_FIRE,
        ];

        // Test that all error codes can be converted to Variant and back
        for &error_code in &error_codes {
            let variant = Variant::from(error_code);
            assert!(variant.is_error());
            assert_eq!(variant.as_error(), Some(error_code));

            // Test display formatting
            let display_str = format!("{}", variant);
            assert!(!display_str.is_empty());

            // Test debug formatting
            let debug_str = format!("{:?}", variant);
            assert!(debug_str.contains("Error"));
        }
    }

    #[test]
    fn test_variant_error_debug() {
        let error_var = Variant::from(Error::ERR_FILE_NOT_FOUND);
        let debug_str = format!("{:?}", error_var);
        assert!(debug_str.contains("Error"));
        assert!(debug_str.contains("ERR_FILE_NOT_FOUND"));
    }

    #[test]
    fn test_variant_input_event_type() {
        use crate::core::input::{InputEvent, KeyCode, MouseButton, JoypadButton};

        let key_event = Variant::from(InputEvent::key(KeyCode::Space, true, false));
        let mouse_event = Variant::from(InputEvent::mouse_button(MouseButton::Left, true, 100.0, 200.0));
        let gamepad_event = Variant::from(InputEvent::joypad_button(JoypadButton::A, true, 0));

        assert!(key_event.is_input_event());
        assert!(mouse_event.is_input_event());
        assert!(gamepad_event.is_input_event());

        assert!(!key_event.is_string());
        assert!(!mouse_event.is_int());
        assert!(!gamepad_event.is_bool());
    }

    #[test]
    fn test_variant_input_event_extraction() {
        use crate::core::input::{InputEvent, KeyCode};

        let key_event = InputEvent::key(KeyCode::Enter, true, false);
        let variant = Variant::from(key_event.clone());

        if let Some(extracted) = variant.as_input_event() {
            match extracted {
                InputEvent::Key { keycode, pressed, .. } => {
                    assert_eq!(*keycode, KeyCode::Enter);
                    assert!(*pressed);
                }
                _ => panic!("Expected Key event"),
            }
        } else {
            panic!("Failed to extract InputEvent");
        }

        let string_var = Variant::from("hello");
        assert_eq!(string_var.as_input_event(), None);
    }

    #[test]
    fn test_variant_input_event_display() {
        use crate::core::input::{InputEvent, KeyCode, MouseButton};

        let key_event = Variant::from(InputEvent::key(KeyCode::Space, true, false));
        let mouse_event = Variant::from(InputEvent::mouse_button(MouseButton::Right, false, 50.0, 75.0));

        let key_display = format!("{}", key_event);
        let mouse_display = format!("{}", mouse_event);

        assert!(key_display.contains("Key"));
        assert!(key_display.contains("pressed"));

        assert!(mouse_display.contains("MouseButton"));
        assert!(mouse_display.contains("released"));
    }

    #[test]
    fn test_variant_input_event_equality() {
        use crate::core::input::{InputEvent, KeyCode};

        let event1 = Variant::from(InputEvent::key(KeyCode::Space, true, false));
        let event2 = Variant::from(InputEvent::key(KeyCode::Space, true, false));
        let event3 = Variant::from(InputEvent::key(KeyCode::Enter, true, false));

        assert_eq!(event1, event2);
        assert_ne!(event1, event3);

        let string_var = Variant::from("Space");
        assert_ne!(event1, string_var);
    }

    #[test]
    fn test_variant_input_event_hash() {
        use crate::core::input::{InputEvent, KeyCode, MouseButton};
        use std::collections::HashMap;

        let mut input_map = HashMap::new();

        let key_event = Variant::from(InputEvent::key(KeyCode::Space, true, false));
        let mouse_event = Variant::from(InputEvent::mouse_button(MouseButton::Left, true, 100.0, 200.0));

        input_map.insert(key_event.clone(), "Space key pressed".to_string());
        input_map.insert(mouse_event.clone(), "Left mouse button pressed".to_string());

        assert_eq!(input_map.get(&key_event), Some(&"Space key pressed".to_string()));
        assert_eq!(input_map.get(&mouse_event), Some(&"Left mouse button pressed".to_string()));

        let different_key = Variant::from(InputEvent::key(KeyCode::Enter, true, false));
        assert_eq!(input_map.get(&different_key), None);
    }

    #[test]
    fn test_variant_input_event_in_collections() {
        use crate::core::input::{InputEvent, KeyCode, MouseButton, JoypadButton};
        use crate::core::variant::{Array, Dictionary};

        let mut input_array = Array::new();
        input_array.push_back(InputEvent::key(KeyCode::W, true, false).into());
        input_array.push_back(InputEvent::key(KeyCode::A, true, false).into());
        input_array.push_back(InputEvent::key(KeyCode::S, true, false).into());
        input_array.push_back(InputEvent::key(KeyCode::D, true, false).into());
        input_array.push_back(InputEvent::mouse_button(MouseButton::Left, true, 0.0, 0.0).into());
        input_array.push_back(InputEvent::joypad_button(JoypadButton::A, true, 0).into());

        assert_eq!(input_array.size(), 6);

        // Check that all items are input events
        for item in input_array.iter() {
            assert!(item.is_input_event());
        }

        // Test input events as dictionary keys
        let mut input_dict = Dictionary::new();
        input_dict.set(
            InputEvent::key(KeyCode::Space, true, false).into(),
            "Jump action".into()
        );
        input_dict.set(
            InputEvent::mouse_button(MouseButton::Right, true, 0.0, 0.0).into(),
            "Secondary action".into()
        );
        input_dict.set(
            InputEvent::joypad_button(JoypadButton::B, true, 0).into(),
            "Cancel action".into()
        );

        assert_eq!(input_dict.size(), 3);

        let space_key = InputEvent::key(KeyCode::Space, true, false).into();
        assert_eq!(input_dict.get(&space_key), Some(&"Jump action".into()));
    }

    #[test]
    fn test_variant_input_event_comprehensive_coverage() {
        use crate::core::input::{InputEvent, KeyCode, MouseButton, JoypadButton, JoypadAxis, JoypadMotion, MouseMotion};
        use crate::core::math::Vector2;

        // Test all input event types in variants
        let key_event = Variant::from(InputEvent::key(KeyCode::Escape, true, false));
        let mouse_button_event = Variant::from(InputEvent::mouse_button(MouseButton::Middle, false, 320.0, 240.0));

        let motion = MouseMotion::new(
            Vector2::new(100.0, 200.0),
            Vector2::new(5.0, -3.0),
            Vector2::new(1.2, 0.8)
        );
        let mouse_motion_event = Variant::from(InputEvent::mouse_motion(motion));

        let gamepad_button_event = Variant::from(InputEvent::joypad_button(JoypadButton::Start, true, 1));

        let joypad_motion = JoypadMotion::new(JoypadAxis::RightStickY, -0.8, 1);
        let gamepad_motion_event = Variant::from(InputEvent::joypad_motion(joypad_motion));

        let action_event = Variant::from(InputEvent::action("fire".to_string(), true, 0.9));

        // Verify all are recognized as input events
        assert!(key_event.is_input_event());
        assert!(mouse_button_event.is_input_event());
        assert!(mouse_motion_event.is_input_event());
        assert!(gamepad_button_event.is_input_event());
        assert!(gamepad_motion_event.is_input_event());
        assert!(action_event.is_input_event());

        // Verify extraction works for all types
        assert!(key_event.as_input_event().is_some());
        assert!(mouse_button_event.as_input_event().is_some());
        assert!(mouse_motion_event.as_input_event().is_some());
        assert!(gamepad_button_event.as_input_event().is_some());
        assert!(gamepad_motion_event.as_input_event().is_some());
        assert!(action_event.as_input_event().is_some());

        // Verify display formatting works
        let key_display = format!("{}", key_event);
        let action_display = format!("{}", action_event);

        assert!(key_display.contains("Key"));
        assert!(action_display.contains("Action"));
        assert!(action_display.contains("fire"));
    }

    #[test]
    fn test_variant_advanced_mathematical_types() {
        // Test AABB
        let aabb = AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 30.0);
        let var_aabb = Variant::from(aabb);
        assert!(var_aabb.is_aabb());
        assert!(!var_aabb.is_vector3());
        assert_eq!(var_aabb.as_aabb(), Some(aabb));
        assert_eq!(var_aabb.as_vector3(), None);

        // Test Projection
        let projection = Projection::identity();
        let var_projection = Variant::from(projection);
        assert!(var_projection.is_projection());
        assert!(!var_projection.is_transform2d());
        assert_eq!(var_projection.as_projection(), Some(projection));
        assert_eq!(var_projection.as_transform2d(), None);

        // Test Quaternion
        let quaternion = Quaternion::IDENTITY;
        let var_quaternion = Variant::from(quaternion);
        assert!(var_quaternion.is_quaternion());
        assert!(!var_quaternion.is_vector4());
        assert_eq!(var_quaternion.as_quaternion(), Some(quaternion));
        assert_eq!(var_quaternion.as_vector4(), None);

        // Test Basis
        let basis = Basis::IDENTITY;
        let var_basis = Variant::from(basis);
        assert!(var_basis.is_basis());
        assert!(!var_basis.is_transform2d());
        assert_eq!(var_basis.as_basis(), Some(basis));
        assert_eq!(var_basis.as_transform2d(), None);

        // Test Plane
        let plane = Plane::new(Vector3::UP, 5.0);
        let var_plane = Variant::from(plane);
        assert!(var_plane.is_plane());
        assert!(!var_plane.is_vector3());
        assert_eq!(var_plane.as_plane(), Some(plane));
        assert_eq!(var_plane.as_vector3(), None);
    }

    #[test]
    fn test_variant_advanced_mathematical_display() {
        // Test AABB display
        let aabb = Variant::from(AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 30.0));
        let display_str = format!("{}", aabb);
        assert!(display_str.contains("1"));
        assert!(display_str.contains("2"));
        assert!(display_str.contains("3"));

        // Test Projection display
        let projection = Variant::from(Projection::identity());
        let display_str = format!("{}", projection);
        assert!(display_str.contains("1"));
        assert!(display_str.contains("0"));

        // Test Quaternion display
        let quaternion = Variant::from(Quaternion::IDENTITY);
        let display_str = format!("{}", quaternion);
        assert!(display_str.contains("0"));
        assert!(display_str.contains("1"));

        // Test Basis display
        let basis = Variant::from(Basis::IDENTITY);
        let display_str = format!("{}", basis);
        assert!(display_str.contains("1"));
        assert!(display_str.contains("0"));

        // Test Plane display
        let plane = Variant::from(Plane::new(Vector3::UP, 5.0));
        let display_str = format!("{}", plane);
        assert!(display_str.contains("5"));
    }

    #[test]
    fn test_variant_advanced_mathematical_hash() {
        use std::collections::HashMap;

        let mut map = HashMap::new();

        // Test AABB as key
        let aabb_key = Variant::from(AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 30.0));
        map.insert(aabb_key.clone(), "aabb_value".into());
        assert_eq!(map.get(&aabb_key), Some(&Variant::from("aabb_value")));

        // Test Projection as key
        let projection_key = Variant::from(Projection::identity());
        map.insert(projection_key.clone(), "projection_value".into());
        assert_eq!(map.get(&projection_key), Some(&Variant::from("projection_value")));

        // Test Quaternion as key
        let quaternion_key = Variant::from(Quaternion::IDENTITY);
        map.insert(quaternion_key.clone(), "quaternion_value".into());
        assert_eq!(map.get(&quaternion_key), Some(&Variant::from("quaternion_value")));

        // Test Basis as key
        let basis_key = Variant::from(Basis::IDENTITY);
        map.insert(basis_key.clone(), "basis_value".into());
        assert_eq!(map.get(&basis_key), Some(&Variant::from("basis_value")));

        // Test Plane as key
        let plane_key = Variant::from(Plane::new(Vector3::UP, 5.0));
        map.insert(plane_key.clone(), "plane_value".into());
        assert_eq!(map.get(&plane_key), Some(&Variant::from("plane_value")));

        // Verify all keys are present
        assert_eq!(map.len(), 5);
    }

    #[test]
    fn test_variant_advanced_mathematical_equality() {
        // Test AABB equality
        let aabb1 = Variant::from(AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 30.0));
        let aabb2 = Variant::from(AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 30.0));
        let aabb3 = Variant::from(AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 31.0));
        assert_eq!(aabb1, aabb2);
        assert_ne!(aabb1, aabb3);

        // Test Projection equality
        let proj1 = Variant::from(Projection::identity());
        let proj2 = Variant::from(Projection::identity());
        let proj3 = Variant::from(Projection::perspective(1.0, 1.0, 0.1, 100.0));
        assert_eq!(proj1, proj2);
        assert_ne!(proj1, proj3);

        // Test Quaternion equality
        let quat1 = Variant::from(Quaternion::IDENTITY);
        let quat2 = Variant::from(Quaternion::IDENTITY);
        let quat3 = Variant::from(Quaternion::from_axis_angle(Vector3::UP, 1.0));
        assert_eq!(quat1, quat2);
        assert_ne!(quat1, quat3);

        // Test Basis equality
        let basis1 = Variant::from(Basis::IDENTITY);
        let basis2 = Variant::from(Basis::IDENTITY);
        let basis3 = Variant::from(Basis::from_scale(Vector3::new(2.0, 2.0, 2.0)));
        assert_eq!(basis1, basis2);
        assert_ne!(basis1, basis3);

        // Test Plane equality
        let plane1 = Variant::from(Plane::new(Vector3::UP, 5.0));
        let plane2 = Variant::from(Plane::new(Vector3::UP, 5.0));
        let plane3 = Variant::from(Plane::new(Vector3::UP, 6.0));
        assert_eq!(plane1, plane2);
        assert_ne!(plane1, plane3);
    }

    #[test]
    fn test_variant_advanced_mathematical_clone() {
        // Test that all advanced mathematical types can be cloned properly
        let aabb = Variant::from(AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 30.0));
        let aabb_clone = aabb.clone();
        assert_eq!(aabb, aabb_clone);
        assert_eq!(aabb.as_aabb(), aabb_clone.as_aabb());

        let projection = Variant::from(Projection::identity());
        let projection_clone = projection.clone();
        assert_eq!(projection, projection_clone);
        assert_eq!(projection.as_projection(), projection_clone.as_projection());

        let quaternion = Variant::from(Quaternion::IDENTITY);
        let quaternion_clone = quaternion.clone();
        assert_eq!(quaternion, quaternion_clone);
        assert_eq!(quaternion.as_quaternion(), quaternion_clone.as_quaternion());

        let basis = Variant::from(Basis::IDENTITY);
        let basis_clone = basis.clone();
        assert_eq!(basis, basis_clone);
        assert_eq!(basis.as_basis(), basis_clone.as_basis());

        let plane = Variant::from(Plane::new(Vector3::UP, 5.0));
        let plane_clone = plane.clone();
        assert_eq!(plane, plane_clone);
        assert_eq!(plane.as_plane(), plane_clone.as_plane());
    }

    #[test]
    fn test_variant_advanced_mathematical_debug() {
        // Test that all advanced mathematical types have proper Debug formatting
        let aabb = Variant::from(AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 30.0));
        let debug_str = format!("{:?}", aabb);
        assert!(debug_str.contains("AABB"));

        let projection = Variant::from(Projection::identity());
        let debug_str = format!("{:?}", projection);
        assert!(debug_str.contains("Projection"));

        let quaternion = Variant::from(Quaternion::IDENTITY);
        let debug_str = format!("{:?}", quaternion);
        assert!(debug_str.contains("Quaternion"));

        let basis = Variant::from(Basis::IDENTITY);
        let debug_str = format!("{:?}", basis);
        assert!(debug_str.contains("Basis"));

        let plane = Variant::from(Plane::new(Vector3::UP, 5.0));
        let debug_str = format!("{:?}", plane);
        assert!(debug_str.contains("Plane"));
    }

    #[test]
    fn test_variant_text_types() {
        // Test GodotString
        let godot_string = GodotString::from("Hello, World!");
        let var_string = Variant::from(godot_string.clone());
        assert!(var_string.is_godot_string());
        assert!(!var_string.is_string_name());
        assert!(!var_string.is_node_path());
        assert_eq!(var_string.as_godot_string(), Some(&godot_string));
        assert_eq!(var_string.as_string_name(), None);
        assert_eq!(var_string.as_node_path(), None);

        // Test StringName
        let string_name = StringName::from("player_node");
        let var_string_name = Variant::from(string_name.clone());
        assert!(var_string_name.is_string_name());
        assert!(!var_string_name.is_godot_string());
        assert!(!var_string_name.is_node_path());
        assert_eq!(var_string_name.as_string_name(), Some(&string_name));
        assert_eq!(var_string_name.as_godot_string(), None);
        assert_eq!(var_string_name.as_node_path(), None);

        // Test NodePath
        let node_path = NodePath::from("/root/Player/Weapon");
        let var_node_path = Variant::from(node_path.clone());
        assert!(var_node_path.is_node_path());
        assert!(!var_node_path.is_godot_string());
        assert!(!var_node_path.is_string_name());
        assert_eq!(var_node_path.as_node_path(), Some(&node_path));
        assert_eq!(var_node_path.as_godot_string(), None);
        assert_eq!(var_node_path.as_string_name(), None);
    }

    #[test]
    fn test_variant_text_display() {
        // Test GodotString display
        let godot_string = Variant::from(GodotString::from("Hello, World!"));
        let display_str = format!("{}", godot_string);
        assert!(display_str.contains("Hello, World!"));

        // Test StringName display
        let string_name = Variant::from(StringName::from("player_node"));
        let display_str = format!("{}", string_name);
        assert!(display_str.contains("player_node"));

        // Test NodePath display
        let node_path = Variant::from(NodePath::from("/root/Player"));
        let display_str = format!("{}", node_path);
        assert!(display_str.contains("/root/Player"));
    }

    #[test]
    fn test_variant_text_hash() {
        use std::collections::HashMap;

        let mut map = HashMap::new();

        // Test GodotString as key
        let string_key = Variant::from(GodotString::from("hello"));
        map.insert(string_key.clone(), "string_value".into());
        assert_eq!(map.get(&string_key), Some(&Variant::from("string_value")));

        // Test StringName as key
        let string_name_key = Variant::from(StringName::from("player"));
        map.insert(string_name_key.clone(), "string_name_value".into());
        assert_eq!(map.get(&string_name_key), Some(&Variant::from("string_name_value")));

        // Test NodePath as key
        let node_path_key = Variant::from(NodePath::from("/root/Player"));
        map.insert(node_path_key.clone(), "node_path_value".into());
        assert_eq!(map.get(&node_path_key), Some(&Variant::from("node_path_value")));

        // Verify all keys are present
        assert_eq!(map.len(), 3);
    }

    #[test]
    fn test_variant_text_equality() {
        // Test GodotString equality
        let string1 = Variant::from(GodotString::from("hello"));
        let string2 = Variant::from(GodotString::from("hello"));
        let string3 = Variant::from(GodotString::from("world"));
        assert_eq!(string1, string2);
        assert_ne!(string1, string3);

        // Test StringName equality
        let name1 = Variant::from(StringName::from("player"));
        let name2 = Variant::from(StringName::from("player"));
        let name3 = Variant::from(StringName::from("enemy"));
        assert_eq!(name1, name2);
        assert_ne!(name1, name3);

        // Test NodePath equality
        let path1 = Variant::from(NodePath::from("/root/Player"));
        let path2 = Variant::from(NodePath::from("/root/Player"));
        let path3 = Variant::from(NodePath::from("/root/Enemy"));
        assert_eq!(path1, path2);
        assert_ne!(path1, path3);
    }

    #[test]
    fn test_variant_text_clone() {
        // Test that all text types can be cloned properly
        let godot_string = Variant::from(GodotString::from("Hello, World!"));
        let string_clone = godot_string.clone();
        assert_eq!(godot_string, string_clone);
        assert_eq!(godot_string.as_godot_string(), string_clone.as_godot_string());

        let string_name = Variant::from(StringName::from("player_node"));
        let name_clone = string_name.clone();
        assert_eq!(string_name, name_clone);
        assert_eq!(string_name.as_string_name(), name_clone.as_string_name());

        let node_path = Variant::from(NodePath::from("/root/Player/Weapon"));
        let path_clone = node_path.clone();
        assert_eq!(node_path, path_clone);
        assert_eq!(node_path.as_node_path(), path_clone.as_node_path());
    }

    #[test]
    fn test_variant_text_debug() {
        // Test that all text types have proper Debug formatting
        let godot_string = Variant::from(GodotString::from("Hello, World!"));
        let debug_str = format!("{:?}", godot_string);
        assert!(debug_str.contains("GodotString"));

        let string_name = Variant::from(StringName::from("player_node"));
        let debug_str = format!("{:?}", string_name);
        assert!(debug_str.contains("StringName"));

        let node_path = Variant::from(NodePath::from("/root/Player"));
        let debug_str = format!("{:?}", node_path);
        assert!(debug_str.contains("NodePath"));
    }

    #[test]
    fn test_variant_text_cross_type_inequality() {
        // Test that different text types are not equal even with same content
        let godot_string = Variant::from(GodotString::from("player"));
        let string_name = Variant::from(StringName::from("player"));
        let node_path = Variant::from(NodePath::from("player"));

        // All should be different from each other
        assert_ne!(godot_string, string_name);
        assert_ne!(godot_string, node_path);
        assert_ne!(string_name, node_path);

        // Test against other types
        let int_var = Variant::from(42);
        assert_ne!(godot_string, int_var);
        assert_ne!(string_name, int_var);
        assert_ne!(node_path, int_var);
    }

    #[test]
    fn test_variant_text_in_collections() {
        // Test text types in Array
        let mut text_array = Array::new();
        text_array.push_back(Variant::from(GodotString::from("Hello")));
        text_array.push_back(Variant::from(StringName::from("player")));
        text_array.push_back(Variant::from(NodePath::from("/root/Player")));

        assert_eq!(text_array.size(), 3);
        assert!(text_array.get(0).unwrap().is_godot_string());
        assert!(text_array.get(1).unwrap().is_string_name());
        assert!(text_array.get(2).unwrap().is_node_path());

        // Test text types in Dictionary
        let mut text_dict = Dictionary::new();
        text_dict.set(Variant::from(GodotString::from("greeting")), Variant::from("Hello"));
        text_dict.set(Variant::from(StringName::from("player_name")), Variant::from("Hero"));
        text_dict.set(Variant::from(NodePath::from("/root/Player")), Variant::from("Player Node"));

        assert_eq!(text_dict.size(), 3);
        assert!(text_dict.has(&Variant::from(GodotString::from("greeting"))));
        assert!(text_dict.has(&Variant::from(StringName::from("player_name"))));
        assert!(text_dict.has(&Variant::from(NodePath::from("/root/Player"))));
    }

    #[test]
    fn test_variant_node_integration() {
        use crate::core::scene::Node;

        // Test Node creation and variant conversion
        let node = Node::new("TestNode");
        let node_var = Variant::from(node.clone());

        // Test type checking
        assert!(node_var.is_node());
        assert!(!node_var.is_string());
        assert!(!node_var.is_int());

        // Test value extraction
        let extracted_node = node_var.as_node().unwrap();
        assert_eq!(extracted_node.get_name(), "TestNode");
        assert_eq!(extracted_node.get_id(), node.get_id());

        // Test that other type extractions return None
        assert!(node_var.as_string().is_none());
        assert!(node_var.as_int().is_none());
        assert!(node_var.as_vector2().is_none());
    }

    #[test]
    fn test_variant_node_display() {
        use crate::core::scene::Node;

        let node = Node::new("DisplayNode");
        let node_var = Variant::from(node);

        let display_str = format!("{}", node_var);
        assert!(display_str.contains("DisplayNode"));
        assert!(display_str.contains("Node("));
    }

    #[test]
    fn test_variant_node_hash() {
        use crate::core::scene::Node;
        use std::collections::HashMap;

        let node1 = Node::new("Node1");
        let node2 = Node::new("Node2");

        let node1_var = Variant::from(node1.clone());
        let node2_var = Variant::from(node2.clone());

        // Test hash map usage
        let mut map = HashMap::new();
        map.insert(node1_var.clone(), "value1".into());
        map.insert(node2_var.clone(), "value2".into());

        assert_eq!(map.len(), 2);
        assert_eq!(map.get(&node1_var), Some(&Variant::from("value1")));
        assert_eq!(map.get(&node2_var), Some(&Variant::from("value2")));
    }

    #[test]
    fn test_variant_node_equality() {
        use crate::core::scene::Node;

        let node1 = Node::new("Node1");
        let node2 = Node::new("Node2");

        let node1_var1 = Variant::from(node1.clone());
        let node1_var2 = Variant::from(node1.clone());
        let node2_var = Variant::from(node2);

        // Same node should be equal
        assert_eq!(node1_var1, node1_var2);

        // Different nodes should not be equal
        assert_ne!(node1_var1, node2_var);

        // Node should not equal other types
        let string_var = Variant::from("test");
        assert_ne!(node1_var1, string_var);
    }

    #[test]
    fn test_variant_node_clone() {
        use crate::core::scene::Node;

        let node = Node::new("CloneNode");
        let node_var = Variant::from(node.clone());
        let cloned_var = node_var.clone();

        assert_eq!(node_var, cloned_var);
        assert_eq!(node_var.as_node().unwrap().get_name(), cloned_var.as_node().unwrap().get_name());
        assert_eq!(node_var.as_node().unwrap().get_id(), cloned_var.as_node().unwrap().get_id());
    }

    #[test]
    fn test_variant_node_debug() {
        use crate::core::scene::Node;

        let node = Node::new("DebugNode");
        let node_var = Variant::from(node);

        let debug_str = format!("{:?}", node_var);
        assert!(debug_str.contains("Node"));
    }

    #[test]
    fn test_variant_node_in_collections() {
        use crate::core::scene::Node;

        // Test Node in Array
        let mut node_array = Array::new();
        let node1 = Node::new("ArrayNode1");
        let node2 = Node::new("ArrayNode2");

        node_array.push_back(Variant::from(node1.clone()));
        node_array.push_back(Variant::from(node2.clone()));
        node_array.push_back(Variant::from("string"));

        assert_eq!(node_array.size(), 3);
        assert!(node_array.get(0).unwrap().is_node());
        assert!(node_array.get(1).unwrap().is_node());
        assert!(node_array.get(2).unwrap().is_string());

        // Test Node in Dictionary
        let mut node_dict = Dictionary::new();
        let key_node = Node::new("KeyNode");
        let value_node = Node::new("ValueNode");

        node_dict.set(Variant::from(key_node.clone()), Variant::from(value_node.clone()));
        node_dict.set(Variant::from("string_key"), Variant::from(node1.clone()));

        assert_eq!(node_dict.size(), 2);
        assert!(node_dict.has(&Variant::from(key_node)));
        assert!(node_dict.has(&Variant::from("string_key")));
    }

    #[test]
    fn test_variant_node_cross_type_safety() {
        use crate::core::scene::Node;

        let node = Node::new("SafetyNode");
        let node_var = Variant::from(node);

        // Test that Node variant doesn't match other types
        assert!(!node_var.is_string());
        assert!(!node_var.is_int());
        assert!(!node_var.is_float());
        assert!(!node_var.is_bool());
        assert!(!node_var.is_vector2());
        assert!(!node_var.is_vector3());
        assert!(!node_var.is_godot_string());
        assert!(!node_var.is_string_name());
        assert!(!node_var.is_node_path());

        // Test that extraction methods return None for wrong types
        assert!(node_var.as_string().is_none());
        assert!(node_var.as_int().is_none());
        assert!(node_var.as_float().is_none());
        assert!(node_var.as_bool().is_none());
        assert!(node_var.as_vector2().is_none());
        assert!(node_var.as_vector3().is_none());
        assert!(node_var.as_godot_string().is_none());
        assert!(node_var.as_string_name().is_none());
        assert!(node_var.as_node_path().is_none());
    }
}
