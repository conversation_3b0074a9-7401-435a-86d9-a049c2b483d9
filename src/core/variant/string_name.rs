//! Comprehensive StringName implementation with string interning for performance optimization.
//!
//! This module provides a StringName implementation that uses string interning to optimize
//! memory usage and comparison performance for frequently used strings. It maintains
//! compatibility with Godot's StringName class while providing thread-safe string pooling
//! and fast equality comparisons through pointer/hash comparison.

use std::fmt;
use std::hash::{Hash, Hasher};
use std::sync::{Arc, Mutex, OnceLock};
use std::collections::HashMap;
use super::string::String as GodotString;

/// Thread-safe string pool for interning strings
static STRING_POOL: OnceLock<Mutex<HashMap<std::string::String, Arc<std::string::String>>>> = OnceLock::new();

/// ### A Godot-compatible StringName with string interning for performance optimization.
///
/// StringName provides memory-efficient storage and fast comparison for frequently used strings
/// through string interning. Multiple StringName instances with the same content share the same
/// underlying string data, making equality comparisons extremely fast through pointer comparison.
///
/// ## String Interning
///
/// StringName uses a global string pool to intern strings:
/// - **Memory Efficiency**: Identical strings share the same memory allocation
/// - **Fast Equality**: Comparison through pointer equality instead of string comparison
/// - **Thread Safety**: Global string pool is protected by mutex for concurrent access
/// - **Automatic Cleanup**: Strings are removed from pool when no longer referenced
///
/// ## Use Cases
///
/// StringName is ideal for:
/// - **Node Names**: Scene tree node identifiers and paths
/// - **Property Names**: Object property and method identifiers
/// - **Signal Names**: Event and signal identifiers
/// - **Resource Names**: Asset and resource identifiers
/// - **Configuration Keys**: Settings and parameter names
/// - **Frequently Used Strings**: Any strings used repeatedly in the application
///
/// # Examples
/// ```
/// # use verturion::core::variant::StringName;
/// // Create StringNames - identical strings are interned
/// let name1 = StringName::from("player");
/// let name2 = StringName::from("player");
///
/// // Fast equality comparison (pointer comparison)
/// assert_eq!(name1, name2);
///
/// // Convert to/from String
/// let string = name1.to_string();
/// let name3 = StringName::from_string(&string);
/// assert_eq!(name1, name3);
/// ```
#[derive(Debug, Clone)]
pub struct StringName {
    /// The interned string data shared between instances
    data: Arc<std::string::String>,
}

impl StringName {
    /// ### Creates a new StringName from a string slice.
    ///
    /// The string is interned in the global string pool. If the string already exists
    /// in the pool, the existing Arc is reused for memory efficiency.
    ///
    /// # Parameters
    /// - `s`: The string slice to intern
    ///
    /// # Returns
    /// A new StringName with the interned string.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let name = StringName::from("player");
    /// assert_eq!(name.as_str(), "player");
    /// ```
    #[inline]
    pub fn from(s: &str) -> Self {
        Self::intern_string(s.to_string())
    }

    /// ### Creates a new StringName from a Godot String.
    ///
    /// # Parameters
    /// - `string`: The Godot String to convert
    ///
    /// # Returns
    /// A new StringName with the interned string content.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{StringName, String};
    /// let string = String::from("player");
    /// let name = StringName::from_string(&string);
    /// assert_eq!(name.as_str(), "player");
    /// ```
    #[inline]
    pub fn from_string(string: &GodotString) -> Self {
        Self::intern_string(string.as_str().to_string())
    }

    /// ### Creates a new empty StringName.
    ///
    /// # Returns
    /// An empty StringName.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let empty = StringName::new();
    /// assert!(empty.is_empty());
    /// ```
    #[inline]
    pub fn new() -> Self {
        Self::from("")
    }

    /// ### Interns a string in the global string pool.
    ///
    /// This is the core interning mechanism that ensures identical strings
    /// share the same Arc<String> instance.
    ///
    /// # Parameters
    /// - `s`: The string to intern
    ///
    /// # Returns
    /// A StringName with the interned string.
    fn intern_string(s: std::string::String) -> Self {
        let pool = STRING_POOL.get_or_init(|| Mutex::new(HashMap::new()));
        let mut pool_guard = pool.lock().unwrap();

        // Check if string already exists in pool
        if let Some(existing) = pool_guard.get(&s) {
            // Reuse existing Arc
            Self {
                data: Arc::clone(existing),
            }
        } else {
            // Create new Arc and add to pool
            let arc_string = Arc::new(s.clone());
            pool_guard.insert(s, Arc::clone(&arc_string));
            Self {
                data: arc_string,
            }
        }
    }

    /// ### Returns the string content as a string slice.
    ///
    /// # Returns
    /// A string slice of the interned string content.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let name = StringName::from("player");
    /// assert_eq!(name.as_str(), "player");
    /// ```
    #[inline]
    pub fn as_str(&self) -> &str {
        &self.data
    }

    /// ### Checks if the StringName is empty.
    ///
    /// # Returns
    /// `true` if the string content is empty.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let empty = StringName::new();
    /// let name = StringName::from("player");
    /// assert!(empty.is_empty());
    /// assert!(!name.is_empty());
    /// ```
    #[inline]
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    /// ### Returns the length of the string in bytes.
    ///
    /// # Returns
    /// The byte length of the string content.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let name = StringName::from("player");
    /// assert_eq!(name.length(), 6);
    /// ```
    #[inline]
    pub fn length(&self) -> usize {
        self.data.len()
    }

    /// ### Converts the StringName to a Godot String.
    ///
    /// # Returns
    /// A new Godot String with the same content.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let name = StringName::from("player");
    /// let string = name.to_string();
    /// assert_eq!(string.as_str(), "player");
    /// ```
    #[inline]
    pub fn to_string(&self) -> GodotString {
        GodotString::from(self.data.as_str())
    }

    /// ### Checks if this StringName contains the specified substring.
    ///
    /// # Parameters
    /// - `substring`: The substring to search for
    ///
    /// # Returns
    /// `true` if the substring is found.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let name = StringName::from("player_controller");
    /// assert!(name.contains("player"));
    /// assert!(!name.contains("enemy"));
    /// ```
    #[inline]
    pub fn contains(&self, substring: &str) -> bool {
        self.data.contains(substring)
    }

    /// ### Checks if this StringName begins with the specified prefix.
    ///
    /// # Parameters
    /// - `prefix`: The prefix to check for
    ///
    /// # Returns
    /// `true` if the string starts with the prefix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let name = StringName::from("player_controller");
    /// assert!(name.begins_with("player"));
    /// assert!(!name.begins_with("enemy"));
    /// ```
    #[inline]
    pub fn begins_with(&self, prefix: &str) -> bool {
        self.data.starts_with(prefix)
    }

    /// ### Checks if this StringName ends with the specified suffix.
    ///
    /// # Parameters
    /// - `suffix`: The suffix to check for
    ///
    /// # Returns
    /// `true` if the string ends with the suffix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let name = StringName::from("player_controller");
    /// assert!(name.ends_with("controller"));
    /// assert!(!name.ends_with("player"));
    /// ```
    #[inline]
    pub fn ends_with(&self, suffix: &str) -> bool {
        self.data.ends_with(suffix)
    }

    /// ### Returns the number of StringName instances currently in the string pool.
    ///
    /// This is primarily useful for debugging and monitoring memory usage.
    ///
    /// # Returns
    /// The number of unique strings currently interned.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::StringName;
    /// let _name1 = StringName::from("test1");
    /// let _name2 = StringName::from("test2");
    /// let count = StringName::pool_size();
    /// assert!(count >= 2);
    /// ```
    pub fn pool_size() -> usize {
        if let Some(pool) = STRING_POOL.get() {
            pool.lock().unwrap().len()
        } else {
            0
        }
    }

    /// ### Clears unused strings from the string pool.
    ///
    /// Removes strings that are no longer referenced by any StringName instances.
    /// This is automatically handled by Arc's reference counting, but this method
    /// can be called explicitly for cleanup.
    ///
    /// # Returns
    /// The number of strings removed from the pool.
    pub fn cleanup_pool() -> usize {
        if let Some(pool) = STRING_POOL.get() {
            let mut pool_guard = pool.lock().unwrap();
            let initial_size = pool_guard.len();

            // Remove entries where the Arc has only one reference (the pool itself)
            pool_guard.retain(|_, arc| Arc::strong_count(arc) > 1);

            initial_size - pool_guard.len()
        } else {
            0
        }
    }
}

impl Default for StringName {
    /// Returns an empty StringName.
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for StringName {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.data)
    }
}

impl PartialEq for StringName {
    /// Fast equality comparison using pointer equality when possible.
    /// Falls back to string comparison if Arc pointers differ.
    #[inline]
    fn eq(&self, other: &Self) -> bool {
        // Fast path: pointer equality (same Arc)
        Arc::ptr_eq(&self.data, &other.data) ||
        // Slow path: string content equality
        self.data == other.data
    }
}

impl Eq for StringName {}

impl Hash for StringName {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.data.hash(state);
    }
}

impl From<&str> for StringName {
    #[inline]
    fn from(s: &str) -> Self {
        Self::from(s)
    }
}

impl From<std::string::String> for StringName {
    #[inline]
    fn from(s: std::string::String) -> Self {
        Self::intern_string(s)
    }
}

impl From<GodotString> for StringName {
    #[inline]
    fn from(s: GodotString) -> Self {
        Self::from_string(&s)
    }
}

impl From<StringName> for GodotString {
    #[inline]
    fn from(name: StringName) -> Self {
        name.to_string()
    }
}

impl From<StringName> for std::string::String {
    #[inline]
    fn from(name: StringName) -> Self {
        name.data.as_ref().clone()
    }
}

impl AsRef<str> for StringName {
    #[inline]
    fn as_ref(&self) -> &str {
        &self.data
    }
}

impl PartialEq<str> for StringName {
    #[inline]
    fn eq(&self, other: &str) -> bool {
        self.data.as_str() == other
    }
}

impl PartialEq<&str> for StringName {
    #[inline]
    fn eq(&self, other: &&str) -> bool {
        self.data.as_str() == *other
    }
}

impl PartialEq<std::string::String> for StringName {
    #[inline]
    fn eq(&self, other: &std::string::String) -> bool {
        self.data.as_ref() == other
    }
}

impl PartialEq<GodotString> for StringName {
    #[inline]
    fn eq(&self, other: &GodotString) -> bool {
        self.data.as_str() == other.as_str()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_string_name_creation() {
        let name1 = StringName::from("test");
        assert_eq!(name1.as_str(), "test");
        assert!(!name1.is_empty());
        assert_eq!(name1.length(), 4);

        let name2 = StringName::new();
        assert!(name2.is_empty());
        assert_eq!(name2.length(), 0);

        let default = StringName::default();
        assert!(default.is_empty());
    }

    #[test]
    fn test_string_name_interning() {
        let name1 = StringName::from("player");
        let name2 = StringName::from("player");
        let name3 = StringName::from("enemy");

        // Same content should be interned (same Arc)
        assert_eq!(name1, name2);
        assert!(Arc::ptr_eq(&name1.data, &name2.data));

        // Different content should have different Arcs
        assert_ne!(name1, name3);
        assert!(!Arc::ptr_eq(&name1.data, &name3.data));
    }

    #[test]
    fn test_string_name_from_string() {
        let godot_string = GodotString::from("test_string");
        let name = StringName::from_string(&godot_string);
        assert_eq!(name.as_str(), "test_string");

        let name2 = StringName::from_string(&godot_string);
        assert_eq!(name, name2);
    }

    #[test]
    fn test_string_name_conversions() {
        let name = StringName::from("test");

        // To Godot String
        let godot_string = name.to_string();
        assert_eq!(godot_string.as_str(), "test");

        // To std::string::String
        let std_string: std::string::String = name.clone().into();
        assert_eq!(std_string, "test");

        // From std::string::String
        let from_std = StringName::from(&std::string::String::from("test"));
        assert_eq!(from_std, name);

        // AsRef<str>
        let as_ref: &str = name.as_ref();
        assert_eq!(as_ref, "test");
    }

    #[test]
    fn test_string_name_search_operations() {
        let name = StringName::from("player_controller");

        assert!(name.contains("player"));
        assert!(name.contains("controller"));
        assert!(!name.contains("enemy"));

        assert!(name.begins_with("player"));
        assert!(!name.begins_with("controller"));

        assert!(name.ends_with("controller"));
        assert!(!name.ends_with("player"));
    }

    #[test]
    fn test_string_name_equality_comparisons() {
        let name1 = StringName::from("test");
        let name2 = StringName::from("test");
        let name3 = StringName::from("other");

        // StringName equality
        assert_eq!(name1, name2);
        assert_ne!(name1, name3);

        // String slice equality
        assert_eq!(name1, "test");
        assert_ne!(name1, "other");

        // std::string::String equality
        assert_eq!(name1, std::string::String::from("test"));
        assert_ne!(name1, std::string::String::from("other"));

        // Godot String equality
        let godot_string = GodotString::from("test");
        assert_eq!(name1, godot_string);
    }

    #[test]
    fn test_string_name_display() {
        let name = StringName::from("test_display");
        let display_str = format!("{}", name);
        assert_eq!(display_str, "test_display");
    }

    #[test]
    fn test_string_name_hash() {
        use std::collections::HashMap;

        let name1 = StringName::from("key1");
        let name2 = StringName::from("key2");

        let mut map = HashMap::new();
        map.insert(name1.clone(), 42);
        map.insert(name2.clone(), 84);

        assert_eq!(map.get(&name1), Some(&42));
        assert_eq!(map.get(&name2), Some(&84));

        // Same content should hash the same
        let name1_copy = StringName::from("key1");
        assert_eq!(map.get(&name1_copy), Some(&42));
    }

    #[test]
    fn test_string_name_pool_operations() {
        let initial_size = StringName::pool_size();

        let _name1 = StringName::from("pool_test_1");
        let _name2 = StringName::from("pool_test_2");
        let _name3 = StringName::from("pool_test_1"); // Duplicate

        let new_size = StringName::pool_size();
        assert!(new_size >= initial_size + 2); // At least 2 new unique strings

        // Test cleanup (though it may not remove anything if references still exist)
        let _removed = StringName::cleanup_pool();
        // Can't assert specific behavior since other tests may have references
    }

    #[test]
    fn test_string_name_clone() {
        let name1 = StringName::from("clone_test");
        let name2 = name1.clone();

        assert_eq!(name1, name2);
        assert!(Arc::ptr_eq(&name1.data, &name2.data)); // Same Arc
    }

    #[test]
    fn test_string_name_empty_handling() {
        let empty1 = StringName::new();
        let empty2 = StringName::from("");
        let empty3 = StringName::default();

        assert_eq!(empty1, empty2);
        assert_eq!(empty2, empty3);
        assert!(empty1.is_empty());
        assert_eq!(empty1.length(), 0);

        // Empty strings should also be interned
        assert!(Arc::ptr_eq(&empty1.data, &empty2.data));
    }

    #[test]
    fn test_string_name_unicode() {
        let unicode_name = StringName::from("Hello, 🌍!");
        assert_eq!(unicode_name.as_str(), "Hello, 🌍!");
        assert!(unicode_name.contains("🌍"));

        let unicode_name2 = StringName::from("Hello, 🌍!");
        assert_eq!(unicode_name, unicode_name2);
        assert!(Arc::ptr_eq(&unicode_name.data, &unicode_name2.data));
    }

    #[test]
    fn test_string_name_thread_safety() {
        use std::thread;
        use std::sync::Arc as StdArc;
        use std::sync::Barrier;

        let barrier = StdArc::new(Barrier::new(4));
        let mut handles = vec![];

        for i in 0..4 {
            let barrier_clone = StdArc::clone(&barrier);
            let handle = thread::spawn(move || {
                barrier_clone.wait();

                // All threads create StringNames with the same content
                let name = StringName::from("thread_test");
                assert_eq!(name.as_str(), "thread_test");

                // Create unique names per thread
                let unique_name = StringName::from(&format!("thread_{}", i));
                assert!(unique_name.contains(&i.to_string()));

                (name, unique_name)
            });
            handles.push(handle);
        }

        let results: Vec<_> = handles.into_iter().map(|h| h.join().unwrap()).collect();

        // All "thread_test" names should be the same Arc
        for i in 1..results.len() {
            assert!(Arc::ptr_eq(&results[0].0.data, &results[i].0.data));
        }

        // Unique names should be different
        for i in 0..results.len() {
            for j in i+1..results.len() {
                assert_ne!(results[i].1, results[j].1);
            }
        }
    }
}
