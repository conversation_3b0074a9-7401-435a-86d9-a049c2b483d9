/// ### 16-bit Signed Integer Wrapper
///
/// A Godot-compatible wrapper around Rust's i16 primitive type that provides
/// type safety, utility methods, and seamless integration with the Variant system.
/// This type is designed for memory-efficient storage of small integer values
/// while maintaining Godot's API compatibility.
///
/// ## Features
///
/// - **Memory Efficient**: Uses only 16 bits of storage
/// - **Type Safety**: Prevents accidental mixing of different numeric types
/// - **Godot Compatibility**: Matches Godot's short integer behavior patterns
/// - **Performance**: Zero-cost abstraction with Copy semantics
/// - **Integration**: Works seamlessly with Dictionary, Array, and Variant systems
/// - **Range**: Supports values from -32,768 to 32,767
///
/// ## Examples
///
/// ```
/// # use verturion::core::variant::Short;
/// // Create shorts
/// let a = Short::new(1000);
/// let b = Short::from_primitive(-500);
/// let c = Short::default(); // 0
///
/// // Mathematical operations
/// let abs_val = b.abs();
/// let sign = b.sign();
/// let clamped = Short::new(40000).clamp(Short::new(-1000), Short::new(1000));
///
/// // Conversions
/// let as_int = a.to_int();
/// let as_float = a.to_float();
/// ```

use std::fmt;
use std::hash::Hash;

/// ### 16-bit signed integer wrapper type.
///
/// Provides a type-safe wrapper around i16 with Godot-compatible methods
/// and seamless integration with the Variant system.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub struct Short {
    /// The wrapped short integer value
    value: i16,
}

impl Short {
    /// ### Maximum value constant (32,767).
    pub const MAX: Self = Self { value: i16::MAX };

    /// ### Minimum value constant (-32,768).
    pub const MIN: Self = Self { value: i16::MIN };

    /// ### Zero constant.
    pub const ZERO: Self = Self { value: 0 };

    /// ### One constant.
    pub const ONE: Self = Self { value: 1 };

    /// ### Creates a new Short with the specified value.
    ///
    /// This is the primary constructor for creating Short instances.
    /// The value must be within the i16 range (-32,768 to 32,767).
    ///
    /// # Arguments
    /// * `value` - The short integer value to wrap
    ///
    /// # Returns
    /// A new Short instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let positive = Short::new(1000);
    /// let negative = Short::new(-500);
    /// let zero = Short::new(0);
    /// let max_val = Short::new(32767);
    /// ```
    #[inline]
    pub const fn new(value: i16) -> Self {
        Self { value }
    }

    /// ### Creates a new Short from a primitive i16 value.
    ///
    /// This method provides an alternative constructor that makes the
    /// conversion from primitive types explicit in the code.
    ///
    /// # Arguments
    /// * `value` - The primitive i16 value to wrap
    ///
    /// # Returns
    /// A new Short instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let from_literal = Short::from_primitive(100);
    /// let from_variable = Short::from_primitive(some_i16_value);
    /// ```
    #[inline]
    pub const fn from_primitive(value: i16) -> Self {
        Self::new(value)
    }

    /// ### Gets the wrapped short integer value.
    ///
    /// Returns the underlying i16 value contained in this Short wrapper.
    /// This is the primary method for extracting the primitive value.
    ///
    /// # Returns
    /// The wrapped i16 value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let short_val = Short::new(42);
    /// let primitive: i16 = short_val.get();
    /// assert_eq!(primitive, 42);
    /// ```
    #[inline]
    pub const fn get(self) -> i16 {
        self.value
    }

    /// ### Sets the wrapped short integer value.
    ///
    /// Updates the value contained in this Short wrapper.
    /// This method provides mutable access to the wrapped value.
    ///
    /// # Arguments
    /// * `value` - The new value to store
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let mut short_val = Short::new(10);
    /// short_val.set(20);
    /// assert_eq!(short_val.get(), 20);
    /// ```
    #[inline]
    pub fn set(&mut self, value: i16) {
        self.value = value;
    }

    /// ### Returns the wrapped value as a primitive i16.
    ///
    /// This method provides an alternative to `get()` with a more explicit name
    /// indicating the conversion to primitive type.
    ///
    /// # Returns
    /// The wrapped i16 value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let short_val = Short::new(-42);
    /// let primitive = short_val.as_primitive();
    /// assert_eq!(primitive, -42);
    /// ```
    #[inline]
    pub const fn as_primitive(self) -> i16 {
        self.value
    }
}

impl Short {
    /// ### Returns the absolute value of the short integer.
    ///
    /// Computes the absolute value, returning a positive Short.
    /// For the minimum value (i16::MIN), this will wrap around due to
    /// two's complement representation limitations.
    ///
    /// # Returns
    /// A Short containing the absolute value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let negative = Short::new(-42);
    /// let positive = Short::new(17);
    ///
    /// assert_eq!(negative.abs(), Short::new(42));
    /// assert_eq!(positive.abs(), Short::new(17));
    /// assert_eq!(Short::new(0).abs(), Short::new(0));
    /// ```
    #[inline]
    pub fn abs(self) -> Self {
        Self::new(self.value.abs())
    }

    /// ### Returns the sign of the short integer.
    ///
    /// Returns -1 for negative numbers, 0 for zero, and 1 for positive numbers.
    /// This is useful for determining the direction or polarity of a value.
    ///
    /// # Returns
    /// A Short containing -1, 0, or 1 representing the sign.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// assert_eq!(Short::new(-42).sign(), Short::new(-1));
    /// assert_eq!(Short::new(0).sign(), Short::new(0));
    /// assert_eq!(Short::new(42).sign(), Short::new(1));
    /// ```
    #[inline]
    pub fn sign(self) -> Self {
        Self::new(self.value.signum())
    }

    /// ### Clamps the short integer to the specified range.
    ///
    /// Returns a value that is constrained to be between min and max (inclusive).
    /// If the value is less than min, returns min. If greater than max, returns max.
    /// Otherwise returns the original value.
    ///
    /// # Arguments
    /// * `min` - The minimum allowed value
    /// * `max` - The maximum allowed value
    ///
    /// # Returns
    /// A Short clamped to the specified range.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let value = Short::new(150);
    /// let min = Short::new(0);
    /// let max = Short::new(100);
    ///
    /// assert_eq!(value.clamp(min, max), Short::new(100));
    /// assert_eq!(Short::new(-10).clamp(min, max), Short::new(0));
    /// assert_eq!(Short::new(50).clamp(min, max), Short::new(50));
    /// ```
    #[inline]
    pub fn clamp(self, min: Self, max: Self) -> Self {
        Self::new(self.value.clamp(min.value, max.value))
    }

    /// ### Returns the minimum of two short integers.
    ///
    /// Compares two Short values and returns the smaller one.
    ///
    /// # Arguments
    /// * `other` - The other Short to compare with
    ///
    /// # Returns
    /// The smaller of the two Short values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let a = Short::new(10);
    /// let b = Short::new(20);
    ///
    /// assert_eq!(a.min(b), Short::new(10));
    /// assert_eq!(b.min(a), Short::new(10));
    /// ```
    #[inline]
    pub fn min(self, other: Self) -> Self {
        Self::new(self.value.min(other.value))
    }

    /// ### Returns the maximum of two short integers.
    ///
    /// Compares two Short values and returns the larger one.
    ///
    /// # Arguments
    /// * `other` - The other Short to compare with
    ///
    /// # Returns
    /// The larger of the two Short values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let a = Short::new(10);
    /// let b = Short::new(20);
    ///
    /// assert_eq!(a.max(b), Short::new(20));
    /// assert_eq!(b.max(a), Short::new(20));
    /// ```
    #[inline]
    pub fn max(self, other: Self) -> Self {
        Self::new(self.value.max(other.value))
    }

    /// ### Performs saturating addition.
    ///
    /// Adds another Short to this one, saturating at the numeric bounds
    /// instead of overflowing.
    ///
    /// # Arguments
    /// * `other` - The Short to add
    ///
    /// # Returns
    /// The result of saturating addition.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let a = Short::new(32000);
    /// let b = Short::new(1000);
    ///
    /// assert_eq!(a.saturating_add(b), Short::MAX);
    /// ```
    #[inline]
    pub fn saturating_add(self, other: Self) -> Self {
        Self::new(self.value.saturating_add(other.value))
    }

    /// ### Performs saturating subtraction.
    ///
    /// Subtracts another Short from this one, saturating at the numeric bounds
    /// instead of overflowing.
    ///
    /// # Arguments
    /// * `other` - The Short to subtract
    ///
    /// # Returns
    /// The result of saturating subtraction.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let a = Short::new(-32000);
    /// let b = Short::new(1000);
    ///
    /// assert_eq!(a.saturating_sub(b), Short::MIN);
    /// ```
    #[inline]
    pub fn saturating_sub(self, other: Self) -> Self {
        Self::new(self.value.saturating_sub(other.value))
    }
}

impl Short {
    /// ### Converts the short integer to a 64-bit integer.
    ///
    /// Creates a new Int wrapper containing the short value converted
    /// to i64. This conversion is always exact.
    ///
    /// # Returns
    /// An Int containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Short, Int};
    /// let short_val = Short::new(1000);
    /// let int_val = short_val.to_int();
    /// assert_eq!(int_val.get(), 1000);
    /// ```
    #[inline]
    pub fn to_int(self) -> super::Int {
        super::Int::new(self.value as i64)
    }

    /// ### Converts the short integer to a floating-point value.
    ///
    /// Creates a new Float wrapper containing the short value converted
    /// to f64. This conversion is always exact.
    ///
    /// # Returns
    /// A Float containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Short, Float};
    /// let short_val = Short::new(42);
    /// let float_val = short_val.to_float();
    /// assert_eq!(float_val.get(), 42.0);
    /// ```
    #[inline]
    pub fn to_float(self) -> super::Float {
        super::Float::new(self.value as f64)
    }

    /// ### Converts the short integer to an Int8 (8-bit integer).
    ///
    /// Creates a new Int8 wrapper containing the short value converted
    /// to i8. Values outside the i8 range will be truncated.
    ///
    /// # Returns
    /// An Int8 containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Short, Int8};
    /// let short_val = Short::new(100);
    /// let int8_val = short_val.to_int8();
    /// assert_eq!(int8_val.get(), 100);
    /// ```
    #[inline]
    pub fn to_int8(self) -> super::Int8 {
        super::Int8::new(self.value as i8)
    }

    /// ### Converts the short integer to a boolean.
    ///
    /// Creates a new Bool wrapper containing true for non-zero values
    /// and false for zero.
    ///
    /// # Returns
    /// A Bool containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Short, Bool};
    /// let zero = Short::new(0);
    /// let non_zero = Short::new(42);
    ///
    /// assert_eq!(zero.to_bool().get(), false);
    /// assert_eq!(non_zero.to_bool().get(), true);
    /// ```
    #[inline]
    pub fn to_bool(self) -> super::Bool {
        super::Bool::new(self.value != 0)
    }
}

impl Default for Short {
    /// ### Creates a default Short with value 0.
    ///
    /// This implementation allows Short to be used in contexts where
    /// a default value is needed, such as in collections or when
    /// using the `Default::default()` method.
    ///
    /// # Returns
    /// A Short with value 0.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let default_short = Short::default();
    /// assert_eq!(default_short.get(), 0);
    ///
    /// let also_default: Short = Default::default();
    /// assert_eq!(also_default, Short::new(0));
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new(0)
    }
}

impl fmt::Display for Short {
    /// ### Formats the Short for display.
    ///
    /// Provides a human-readable string representation of the short integer value.
    /// The output matches the standard formatting of i16 values.
    ///
    /// # Arguments
    /// * `f` - The formatter to write to
    ///
    /// # Returns
    /// A formatting result.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Short;
    /// let short_val = Short::new(42);
    /// assert_eq!(format!("{}", short_val), "42");
    ///
    /// let negative = Short::new(-17);
    /// assert_eq!(format!("{}", negative), "-17");
    /// ```
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.value)
    }
}

// Arithmetic operations
impl std::ops::Add for Short {
    type Output = Self;

    #[inline]
    fn add(self, rhs: Self) -> Self::Output {
        Self::new(self.value + rhs.value)
    }
}

impl std::ops::Sub for Short {
    type Output = Self;

    #[inline]
    fn sub(self, rhs: Self) -> Self::Output {
        Self::new(self.value - rhs.value)
    }
}

impl std::ops::Mul for Short {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: Self) -> Self::Output {
        Self::new(self.value * rhs.value)
    }
}

impl std::ops::Div for Short {
    type Output = Self;

    #[inline]
    fn div(self, rhs: Self) -> Self::Output {
        Self::new(self.value / rhs.value)
    }
}

impl std::ops::Rem for Short {
    type Output = Self;

    #[inline]
    fn rem(self, rhs: Self) -> Self::Output {
        Self::new(self.value % rhs.value)
    }
}

impl std::ops::Neg for Short {
    type Output = Self;

    #[inline]
    fn neg(self) -> Self::Output {
        Self::new(-self.value)
    }
}

// Assignment operations
impl std::ops::AddAssign for Short {
    #[inline]
    fn add_assign(&mut self, rhs: Self) {
        self.value += rhs.value;
    }
}

impl std::ops::SubAssign for Short {
    #[inline]
    fn sub_assign(&mut self, rhs: Self) {
        self.value -= rhs.value;
    }
}

impl std::ops::MulAssign for Short {
    #[inline]
    fn mul_assign(&mut self, rhs: Self) {
        self.value *= rhs.value;
    }
}

impl std::ops::DivAssign for Short {
    #[inline]
    fn div_assign(&mut self, rhs: Self) {
        self.value /= rhs.value;
    }
}

impl std::ops::RemAssign for Short {
    #[inline]
    fn rem_assign(&mut self, rhs: Self) {
        self.value %= rhs.value;
    }
}

// Conversions from primitive types
impl From<i16> for Short {
    #[inline]
    fn from(value: i16) -> Self {
        Self::new(value)
    }
}

impl From<i8> for Short {
    #[inline]
    fn from(value: i8) -> Self {
        Self::new(value as i16)
    }
}

impl From<u8> for Short {
    #[inline]
    fn from(value: u8) -> Self {
        Self::new(value as i16)
    }
}

// Conversions to primitive types
impl From<Short> for i16 {
    #[inline]
    fn from(short: Short) -> Self {
        short.value
    }
}

impl From<Short> for i32 {
    #[inline]
    fn from(short: Short) -> Self {
        short.value as i32
    }
}

impl From<Short> for i64 {
    #[inline]
    fn from(short: Short) -> Self {
        short.value as i64
    }
}

impl From<Short> for f32 {
    #[inline]
    fn from(short: Short) -> Self {
        short.value as f32
    }
}

impl From<Short> for f64 {
    #[inline]
    fn from(short: Short) -> Self {
        short.value as f64
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_short_creation() {
        let short1 = Short::new(42);
        assert_eq!(short1.get(), 42);

        let short2 = Short::from_primitive(-17);
        assert_eq!(short2.get(), -17);

        let short3 = Short::default();
        assert_eq!(short3.get(), 0);

        let short4: Short = 100i16.into();
        assert_eq!(short4.get(), 100);
    }

    #[test]
    fn test_short_constants() {
        assert_eq!(Short::MAX.get(), i16::MAX);
        assert_eq!(Short::MIN.get(), i16::MIN);
        assert_eq!(Short::ZERO.get(), 0);
        assert_eq!(Short::ONE.get(), 1);
    }

    #[test]
    fn test_short_value_access() {
        let mut short_val = Short::new(10);
        assert_eq!(short_val.get(), 10);
        assert_eq!(short_val.as_primitive(), 10);

        short_val.set(20);
        assert_eq!(short_val.get(), 20);
    }

    #[test]
    fn test_short_mathematical_operations() {
        let positive = Short::new(42);
        let negative = Short::new(-17);
        let zero = Short::new(0);

        // Test abs
        assert_eq!(positive.abs(), Short::new(42));
        assert_eq!(negative.abs(), Short::new(17));
        assert_eq!(zero.abs(), Short::new(0));

        // Test sign
        assert_eq!(positive.sign(), Short::new(1));
        assert_eq!(negative.sign(), Short::new(-1));
        assert_eq!(zero.sign(), Short::new(0));

        // Test min/max
        assert_eq!(positive.min(negative), negative);
        assert_eq!(positive.max(negative), positive);
        assert_eq!(positive.min(positive), positive);
    }

    #[test]
    fn test_short_clamp() {
        let min = Short::new(0);
        let max = Short::new(100);

        assert_eq!(Short::new(50).clamp(min, max), Short::new(50));
        assert_eq!(Short::new(-10).clamp(min, max), Short::new(0));
        assert_eq!(Short::new(150).clamp(min, max), Short::new(100));
        assert_eq!(Short::new(0).clamp(min, max), Short::new(0));
        assert_eq!(Short::new(100).clamp(min, max), Short::new(100));
    }

    #[test]
    fn test_short_saturating_operations() {
        let large = Short::new(32000);
        let small = Short::new(1000);

        assert_eq!(large.saturating_add(small), Short::MAX);
        assert_eq!(Short::MIN.saturating_sub(small), Short::MIN);
    }

    #[test]
    fn test_short_arithmetic_operations() {
        let a = Short::new(10);
        let b = Short::new(3);

        assert_eq!(a + b, Short::new(13));
        assert_eq!(a - b, Short::new(7));
        assert_eq!(a * b, Short::new(30));
        assert_eq!(a / b, Short::new(3));
        assert_eq!(a % b, Short::new(1));
        assert_eq!(-a, Short::new(-10));
    }

    #[test]
    fn test_short_assignment_operations() {
        let mut a = Short::new(10);
        let b = Short::new(3);

        a += b;
        assert_eq!(a, Short::new(13));

        a -= b;
        assert_eq!(a, Short::new(10));

        a *= b;
        assert_eq!(a, Short::new(30));

        a /= b;
        assert_eq!(a, Short::new(10));

        a %= b;
        assert_eq!(a, Short::new(1));
    }

    #[test]
    fn test_short_conversions() {
        let short_val = Short::new(42);

        // Test to other wrapper types
        let int_val = short_val.to_int();
        assert_eq!(int_val.get(), 42);

        let float_val = short_val.to_float();
        assert_eq!(float_val.get(), 42.0);

        let bool_val = short_val.to_bool();
        assert_eq!(bool_val.get(), true);

        let zero_bool = Short::new(0).to_bool();
        assert_eq!(zero_bool.get(), false);

        // Test from primitive types
        let from_i8: Short = (42i8).into();
        assert_eq!(from_i8.get(), 42);

        let from_u8: Short = (42u8).into();
        assert_eq!(from_u8.get(), 42);

        // Test to primitive types
        let to_i16: i16 = short_val.into();
        assert_eq!(to_i16, 42);

        let to_i32: i32 = short_val.into();
        assert_eq!(to_i32, 42);

        let to_i64: i64 = short_val.into();
        assert_eq!(to_i64, 42);

        let to_f32: f32 = short_val.into();
        assert_eq!(to_f32, 42.0);

        let to_f64: f64 = short_val.into();
        assert_eq!(to_f64, 42.0);
    }

    #[test]
    fn test_short_comparison() {
        let a = Short::new(10);
        let b = Short::new(20);
        let c = Short::new(10);

        assert!(a < b);
        assert!(b > a);
        assert_eq!(a, c);
        assert_ne!(a, b);
        assert!(a <= c);
        assert!(a >= c);
    }

    #[test]
    fn test_short_display() {
        assert_eq!(format!("{}", Short::new(42)), "42");
        assert_eq!(format!("{}", Short::new(-17)), "-17");
        assert_eq!(format!("{}", Short::new(0)), "0");
        assert_eq!(format!("{}", Short::MAX), "32767");
        assert_eq!(format!("{}", Short::MIN), "-32768");
    }

    #[test]
    fn test_short_hash() {
        use std::collections::HashMap;

        let mut map = HashMap::new();
        let key1 = Short::new(42);
        let key2 = Short::new(17);

        map.insert(key1, "forty-two");
        map.insert(key2, "seventeen");

        assert_eq!(map.get(&Short::new(42)), Some(&"forty-two"));
        assert_eq!(map.get(&Short::new(17)), Some(&"seventeen"));
        assert_eq!(map.get(&Short::new(100)), None);
    }

    #[test]
    fn test_short_edge_cases() {
        // Test with extreme values
        let max_val = Short::MAX;
        let min_val = Short::MIN;

        assert_eq!(max_val.get(), i16::MAX);
        assert_eq!(min_val.get(), i16::MIN);

        // Test sign of extreme values
        assert_eq!(max_val.sign(), Short::new(1));
        assert_eq!(min_val.sign(), Short::new(-1));

        // Test abs of near-min value (since MIN.abs() panics in debug mode)
        let near_min = Short::new(i16::MIN + 1);
        assert_eq!(near_min.abs(), Short::new(i16::MAX));

        // For MIN value, abs() will wrap around in release mode or panic in debug mode
        // This is expected behavior for two's complement arithmetic
    }

    #[test]
    fn test_short_copy_semantics() {
        let original = Short::new(42);
        let copied = original;

        // Both should have the same value
        assert_eq!(original.get(), 42);
        assert_eq!(copied.get(), 42);

        // Modifying one shouldn't affect the other
        let mut mutable_copy = original;
        mutable_copy.set(100);
        assert_eq!(original.get(), 42);
        assert_eq!(mutable_copy.get(), 100);
    }
}
