//! Comprehensive Dictionary implementation for key-value storage with dynamic typing.
//!
//! This module provides a complete Dictionary implementation using HashMap for efficient
//! key-value storage with Variant types. It supports all common dictionary operations,
//! type-safe access methods, and comprehensive utility functions for data management,
//! configuration storage, and dynamic collections.

use std::fmt;
use std::collections::HashMap;
use std::ops::{Index, IndexMut};
use super::Variant;

/// ### A dictionary for storing key-value pairs with dynamic typing.
///
/// Dictionary provides a hash map-based collection that can store any Variant types
/// as both keys and values. It offers efficient O(1) average-case access time and
/// supports all common dictionary operations with type-safe access methods.
///
/// ## Key Features
///
/// - **Dynamic Typing**: Keys and values can be any Variant type
/// - **Efficient Access**: O(1) average-case lookup, insertion, and deletion
/// - **Type Safety**: Safe type checking and conversion methods
/// - **Iteration Support**: Full iterator support for keys, values, and pairs
/// - **Memory Efficient**: Optimized storage with capacity management
/// - **Godot Compatible**: API matches <PERSON><PERSON>'s Dictionary class
///
/// ## Use Cases
///
/// Dictionary is ideal for:
/// - **Configuration Data**: Settings and parameters with mixed types
/// - **Game State**: Player data, inventory systems, save files
/// - **Scripting Integration**: Dynamic property storage
/// - **Data Serialization**: JSON-like data structures
/// - **Cache Systems**: Flexible key-value caching
/// - **Event Systems**: Parameter passing with mixed types
///
/// ## Performance Features
///
/// - **Optimized Hashing**: Efficient hash implementation for all Variant types
/// - **Capacity Management**: Pre-allocation support for known sizes
/// - **Inline Methods**: Performance-critical operations marked with #[inline]
/// - **Memory Layout**: Compact representation for cache efficiency
#[derive(Debug, Clone, PartialEq)]
pub struct Dictionary {
    /// Internal hash map storage for key-value pairs.
    data: HashMap<Variant, Variant>,
}

impl Dictionary {
    /// ### Creates a new empty Dictionary.
    ///
    /// Initializes an empty dictionary with default capacity. The dictionary
    /// will automatically grow as elements are added.
    ///
    /// # Returns
    /// A new empty Dictionary instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let dict = Dictionary::new();
    /// assert!(dict.is_empty());
    /// assert_eq!(dict.size(), 0);
    /// ```
    #[inline]
    pub fn new() -> Self {
        Self {
            data: HashMap::new(),
        }
    }

    /// ### Creates a new Dictionary with the specified capacity.
    ///
    /// Pre-allocates space for at least the specified number of elements,
    /// which can improve performance when the final size is known in advance.
    ///
    /// # Arguments
    /// * `capacity` - The initial capacity to reserve
    ///
    /// # Returns
    /// A new Dictionary with pre-allocated capacity.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let dict = Dictionary::with_capacity(100);
    /// assert!(dict.is_empty());
    /// // No reallocation needed for first 100 elements
    /// ```
    #[inline]
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            data: HashMap::with_capacity(capacity),
        }
    }

    /// ### Gets the number of key-value pairs in the dictionary.
    ///
    /// Returns the current number of elements stored in the dictionary.
    /// This operation is O(1) and does not iterate through the elements.
    ///
    /// # Returns
    /// The number of key-value pairs in the dictionary.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// assert_eq!(dict.size(), 0);
    ///
    /// dict.set("key".into(), 42.into());
    /// assert_eq!(dict.size(), 1);
    /// ```
    #[inline]
    pub fn size(&self) -> usize {
        self.data.len()
    }

    /// ### Checks if the dictionary is empty.
    ///
    /// Returns true if the dictionary contains no key-value pairs.
    /// This is equivalent to checking if size() == 0 but may be more readable.
    ///
    /// # Returns
    /// True if the dictionary is empty, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// assert!(dict.is_empty());
    ///
    /// dict.set("key".into(), "value".into());
    /// assert!(!dict.is_empty());
    /// ```
    #[inline]
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    /// ### Checks if the dictionary contains the specified key.
    ///
    /// Returns true if the dictionary contains a value for the specified key.
    /// This operation is O(1) average case.
    ///
    /// # Arguments
    /// * `key` - The key to search for
    ///
    /// # Returns
    /// True if the key exists in the dictionary, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// dict.set("name".into(), "Alice".into());
    ///
    /// assert!(dict.has(&"name".into()));
    /// assert!(!dict.has(&"age".into()));
    /// ```
    #[inline]
    pub fn has(&self, key: &Variant) -> bool {
        self.data.contains_key(key)
    }

    /// ### Gets the value associated with the specified key.
    ///
    /// Returns a reference to the value if the key exists, otherwise returns None.
    /// This operation is O(1) average case.
    ///
    /// # Arguments
    /// * `key` - The key to look up
    ///
    /// # Returns
    /// Some(&Variant) if the key exists, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// dict.set("score".into(), 100.into());
    ///
    /// assert_eq!(dict.get(&"score".into()), Some(&Variant::Int(100)));
    /// assert_eq!(dict.get(&"lives".into()), None);
    /// ```
    #[inline]
    pub fn get(&self, key: &Variant) -> Option<&Variant> {
        self.data.get(key)
    }

    /// ### Gets the value associated with the specified key, or returns a default.
    ///
    /// Returns the value if the key exists, otherwise returns the provided default value.
    /// This is useful for providing fallback values when keys might not exist.
    ///
    /// # Arguments
    /// * `key` - The key to look up
    /// * `default` - The default value to return if the key doesn't exist
    ///
    /// # Returns
    /// The value associated with the key, or the default value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// dict.set("level".into(), 5.into());
    ///
    /// assert_eq!(dict.get_or(&"level".into(), &1.into()), &Variant::Int(5));
    /// assert_eq!(dict.get_or(&"lives".into(), &3.into()), &Variant::Int(3));
    /// ```
    #[inline]
    pub fn get_or<'a>(&'a self, key: &Variant, default: &'a Variant) -> &'a Variant {
        self.data.get(key).unwrap_or(default)
    }

    /// ### Sets the value for the specified key.
    ///
    /// Inserts or updates the key-value pair in the dictionary. If the key already
    /// exists, the old value is replaced and returned. This operation is O(1) average case.
    ///
    /// # Arguments
    /// * `key` - The key to set
    /// * `value` - The value to associate with the key
    ///
    /// # Returns
    /// The previous value if the key existed, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    ///
    /// assert_eq!(dict.set("name".into(), "Alice".into()), None);
    /// assert_eq!(dict.set("name".into(), "Bob".into()), Some(Variant::String("Alice".to_string())));
    /// ```
    #[inline]
    pub fn set(&mut self, key: Variant, value: Variant) -> Option<Variant> {
        self.data.insert(key, value)
    }

    /// ### Removes the key-value pair for the specified key.
    ///
    /// Removes and returns the value associated with the key if it exists.
    /// This operation is O(1) average case.
    ///
    /// # Arguments
    /// * `key` - The key to remove
    ///
    /// # Returns
    /// The removed value if the key existed, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// dict.set("temp".into(), 42.into());
    ///
    /// assert_eq!(dict.erase(&"temp".into()), Some(Variant::Int(42)));
    /// assert_eq!(dict.erase(&"temp".into()), None);
    /// ```
    #[inline]
    pub fn erase(&mut self, key: &Variant) -> Option<Variant> {
        self.data.remove(key)
    }

    /// ### Removes all key-value pairs from the dictionary.
    ///
    /// Clears the dictionary, making it empty. The allocated capacity is retained
    /// for potential reuse, which can improve performance for repeated use.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// dict.set("a".into(), 1.into());
    /// dict.set("b".into(), 2.into());
    ///
    /// dict.clear();
    /// assert!(dict.is_empty());
    /// assert_eq!(dict.size(), 0);
    /// ```
    #[inline]
    pub fn clear(&mut self) {
        self.data.clear();
    }

    /// ### Returns an iterator over the keys in the dictionary.
    ///
    /// Creates an iterator that yields references to all keys in the dictionary.
    /// The iteration order is arbitrary but consistent for the same dictionary state.
    ///
    /// # Returns
    /// An iterator over the dictionary keys.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// dict.set("x".into(), 1.into());
    /// dict.set("y".into(), 2.into());
    ///
    /// let keys: Vec<_> = dict.keys().collect();
    /// assert_eq!(keys.len(), 2);
    /// ```
    #[inline]
    pub fn keys(&self) -> impl Iterator<Item = &Variant> {
        self.data.keys()
    }

    /// ### Returns an iterator over the values in the dictionary.
    ///
    /// Creates an iterator that yields references to all values in the dictionary.
    /// The iteration order matches the order of keys() for the same dictionary state.
    ///
    /// # Returns
    /// An iterator over the dictionary values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// dict.set("a".into(), 10.into());
    /// dict.set("b".into(), 20.into());
    ///
    /// let values: Vec<_> = dict.values().collect();
    /// assert_eq!(values.len(), 2);
    /// ```
    #[inline]
    pub fn values(&self) -> impl Iterator<Item = &Variant> {
        self.data.values()
    }

    /// ### Returns an iterator over key-value pairs in the dictionary.
    ///
    /// Creates an iterator that yields references to all key-value pairs.
    /// This is the most efficient way to iterate over both keys and values.
    ///
    /// # Returns
    /// An iterator over (key, value) pairs.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict = Dictionary::new();
    /// dict.set("name".into(), "Alice".into());
    /// dict.set("age".into(), 30.into());
    ///
    /// for (key, value) in dict.iter() {
    ///     println!("{}: {}", key, value);
    /// }
    /// ```
    #[inline]
    pub fn iter(&self) -> impl Iterator<Item = (&Variant, &Variant)> {
        self.data.iter()
    }

    /// ### Creates a shallow copy of the dictionary.
    ///
    /// Returns a new dictionary containing the same key-value pairs.
    /// The keys and values themselves are cloned, creating independent copies.
    ///
    /// # Returns
    /// A new Dictionary with the same contents.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut original = Dictionary::new();
    /// original.set("data".into(), vec![1, 2, 3].into());
    ///
    /// let copy = original.duplicate();
    /// assert_eq!(original, copy);
    /// ```
    #[inline]
    pub fn duplicate(&self) -> Self {
        Self {
            data: self.data.clone(),
        }
    }

    /// ### Merges another dictionary into this one.
    ///
    /// Adds all key-value pairs from the other dictionary to this one.
    /// If keys exist in both dictionaries, the values from the other dictionary
    /// will overwrite the values in this dictionary.
    ///
    /// # Arguments
    /// * `other` - The dictionary to merge into this one
    /// * `overwrite` - Whether to overwrite existing keys (true) or skip them (false)
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Dictionary, Variant};
    /// let mut dict1 = Dictionary::new();
    /// dict1.set("a".into(), 1.into());
    /// dict1.set("b".into(), 2.into());
    ///
    /// let mut dict2 = Dictionary::new();
    /// dict2.set("b".into(), 20.into());
    /// dict2.set("c".into(), 3.into());
    ///
    /// dict1.merge(&dict2, true);
    /// assert_eq!(dict1.get(&"b".into()), Some(&Variant::Int(20))); // Overwritten
    /// assert_eq!(dict1.get(&"c".into()), Some(&Variant::Int(3)));  // Added
    /// ```
    #[inline]
    pub fn merge(&mut self, other: &Dictionary, overwrite: bool) {
        for (key, value) in other.iter() {
            if overwrite || !self.has(key) {
                self.set(key.clone(), value.clone());
            }
        }
    }
}

impl Default for Dictionary {
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for Dictionary {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{{")?;
        for (i, (key, value)) in self.data.iter().enumerate() {
            if i > 0 {
                write!(f, ", ")?;
            }
            write!(f, "{}: {}", key, value)?;
        }
        write!(f, "}}")
    }
}

impl Index<&Variant> for Dictionary {
    type Output = Variant;

    #[inline]
    fn index(&self, key: &Variant) -> &Self::Output {
        &self.data[key]
    }
}

impl IndexMut<&Variant> for Dictionary {
    #[inline]
    fn index_mut(&mut self, key: &Variant) -> &mut Self::Output {
        self.data.get_mut(key).expect("Key not found in dictionary")
    }
}

impl From<HashMap<Variant, Variant>> for Dictionary {
    #[inline]
    fn from(data: HashMap<Variant, Variant>) -> Self {
        Self { data }
    }
}

impl From<Dictionary> for HashMap<Variant, Variant> {
    #[inline]
    fn from(dict: Dictionary) -> Self {
        dict.data
    }
}

impl IntoIterator for Dictionary {
    type Item = (Variant, Variant);
    type IntoIter = std::collections::hash_map::IntoIter<Variant, Variant>;

    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.data.into_iter()
    }
}

impl<'a> IntoIterator for &'a Dictionary {
    type Item = (&'a Variant, &'a Variant);
    type IntoIter = std::collections::hash_map::Iter<'a, Variant, Variant>;

    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.data.iter()
    }
}

impl FromIterator<(Variant, Variant)> for Dictionary {
    #[inline]
    fn from_iter<T: IntoIterator<Item = (Variant, Variant)>>(iter: T) -> Self {
        Self {
            data: HashMap::from_iter(iter),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dictionary_creation() {
        let dict = Dictionary::new();
        assert!(dict.is_empty());
        assert_eq!(dict.size(), 0);

        let dict_with_capacity = Dictionary::with_capacity(10);
        assert!(dict_with_capacity.is_empty());
        assert_eq!(dict_with_capacity.size(), 0);
    }

    #[test]
    fn test_dictionary_basic_operations() {
        let mut dict = Dictionary::new();

        // Test set and get
        assert_eq!(dict.set("name".into(), "Alice".into()), None);
        assert_eq!(dict.get(&"name".into()), Some(&Variant::String("Alice".to_string())));
        assert_eq!(dict.size(), 1);
        assert!(!dict.is_empty());

        // Test has
        assert!(dict.has(&"name".into()));
        assert!(!dict.has(&"age".into()));

        // Test overwrite
        assert_eq!(dict.set("name".into(), "Bob".into()), Some(Variant::String("Alice".to_string())));
        assert_eq!(dict.get(&"name".into()), Some(&Variant::String("Bob".to_string())));
        assert_eq!(dict.size(), 1);
    }

    #[test]
    fn test_dictionary_get_or() {
        let mut dict = Dictionary::new();
        dict.set("level".into(), 5.into());

        let default_level = 1.into();
        let default_lives = 3.into();

        assert_eq!(dict.get_or(&"level".into(), &default_level), &Variant::Int(5));
        assert_eq!(dict.get_or(&"lives".into(), &default_lives), &Variant::Int(3));
    }

    #[test]
    fn test_dictionary_erase() {
        let mut dict = Dictionary::new();
        dict.set("temp".into(), 42.into());
        dict.set("keep".into(), "value".into());

        assert_eq!(dict.erase(&"temp".into()), Some(Variant::Int(42)));
        assert_eq!(dict.erase(&"temp".into()), None);
        assert!(!dict.has(&"temp".into()));
        assert!(dict.has(&"keep".into()));
        assert_eq!(dict.size(), 1);
    }

    #[test]
    fn test_dictionary_clear() {
        let mut dict = Dictionary::new();
        dict.set("a".into(), 1.into());
        dict.set("b".into(), 2.into());
        dict.set("c".into(), 3.into());

        assert_eq!(dict.size(), 3);
        dict.clear();
        assert!(dict.is_empty());
        assert_eq!(dict.size(), 0);
    }

    #[test]
    fn test_dictionary_iteration() {
        let mut dict = Dictionary::new();
        dict.set("x".into(), 1.into());
        dict.set("y".into(), 2.into());
        dict.set("z".into(), 3.into());

        // Test keys iterator
        let keys: Vec<_> = dict.keys().collect();
        assert_eq!(keys.len(), 3);

        // Test values iterator
        let values: Vec<_> = dict.values().collect();
        assert_eq!(values.len(), 3);

        // Test iter
        let pairs: Vec<_> = dict.iter().collect();
        assert_eq!(pairs.len(), 3);

        // Test into_iter
        let dict_copy = dict.duplicate();
        let owned_pairs: Vec<_> = dict_copy.into_iter().collect();
        assert_eq!(owned_pairs.len(), 3);
    }

    #[test]
    fn test_dictionary_duplicate() {
        let mut original = Dictionary::new();
        original.set("name".into(), "Alice".into());
        original.set("age".into(), 30.into());

        let copy = original.duplicate();
        assert_eq!(original, copy);
        assert_eq!(copy.size(), 2);
        assert_eq!(copy.get(&"name".into()), Some(&Variant::String("Alice".to_string())));
    }

    #[test]
    fn test_dictionary_merge() {
        let mut dict1 = Dictionary::new();
        dict1.set("a".into(), 1.into());
        dict1.set("b".into(), 2.into());

        let mut dict2 = Dictionary::new();
        dict2.set("b".into(), 20.into());
        dict2.set("c".into(), 3.into());

        // Test merge with overwrite
        dict1.merge(&dict2, true);
        assert_eq!(dict1.size(), 3);
        assert_eq!(dict1.get(&"a".into()), Some(&Variant::Int(1)));
        assert_eq!(dict1.get(&"b".into()), Some(&Variant::Int(20))); // Overwritten
        assert_eq!(dict1.get(&"c".into()), Some(&Variant::Int(3)));

        // Test merge without overwrite
        let mut dict3 = Dictionary::new();
        dict3.set("a".into(), 1.into());
        dict3.set("b".into(), 2.into());

        dict3.merge(&dict2, false);
        assert_eq!(dict3.size(), 3);
        assert_eq!(dict3.get(&"b".into()), Some(&Variant::Int(2))); // Not overwritten
        assert_eq!(dict3.get(&"c".into()), Some(&Variant::Int(3))); // Added
    }

    #[test]
    fn test_dictionary_indexing() {
        let mut dict = Dictionary::new();
        dict.set("test".into(), "value".into());

        // Test index access
        assert_eq!(dict[&"test".into()], Variant::String("value".to_string()));

        // Test index mutation
        dict[&"test".into()] = "new_value".into();
        assert_eq!(dict.get(&"test".into()), Some(&Variant::String("new_value".to_string())));
    }

    #[test]
    fn test_dictionary_mixed_types() {
        let mut dict = Dictionary::new();

        // Test various Variant types as keys and values
        dict.set("string_key".into(), 42.into());
        dict.set(123.into(), "int_key".into());
        dict.set(true.into(), false.into());
        dict.set(3.14.into(), "pi".into());

        assert_eq!(dict.size(), 4);
        assert_eq!(dict.get(&"string_key".into()), Some(&Variant::Int(42)));
        assert_eq!(dict.get(&123.into()), Some(&Variant::String("int_key".to_string())));
        assert_eq!(dict.get(&true.into()), Some(&Variant::Bool(false)));
        assert_eq!(dict.get(&3.14.into()), Some(&Variant::String("pi".to_string())));
    }

    #[test]
    fn test_dictionary_display() {
        let mut dict = Dictionary::new();
        dict.set("name".into(), "Alice".into());
        dict.set("age".into(), 30.into());

        let display_string = format!("{}", dict);
        // Note: HashMap iteration order is not guaranteed, so we just check it contains the right elements
        assert!(display_string.contains("\"name\": \"Alice\"") || display_string.contains("\"Alice\""));
        assert!(display_string.contains("30") && (display_string.contains("age") || display_string.contains("\"age\"")));
        assert!(display_string.starts_with('{'));
        assert!(display_string.ends_with('}'));
        assert!(display_string.contains(":"));
    }

    #[test]
    fn test_dictionary_from_iterator() {
        let pairs = vec![
            ("a".into(), 1.into()),
            ("b".into(), 2.into()),
            ("c".into(), 3.into()),
        ];

        let dict: Dictionary = pairs.into_iter().collect();
        assert_eq!(dict.size(), 3);
        assert_eq!(dict.get(&"a".into()), Some(&Variant::Int(1)));
        assert_eq!(dict.get(&"b".into()), Some(&Variant::Int(2)));
        assert_eq!(dict.get(&"c".into()), Some(&Variant::Int(3)));
    }

    #[test]
    fn test_dictionary_edge_cases() {
        let mut dict = Dictionary::new();

        // Test empty dictionary operations
        assert_eq!(dict.get(&"nonexistent".into()), None);
        assert_eq!(dict.erase(&"nonexistent".into()), None);
        assert!(!dict.has(&"anything".into()));

        // Test nil values
        dict.set("nil_key".into(), Variant::nil());
        assert!(dict.has(&"nil_key".into()));
        assert_eq!(dict.get(&"nil_key".into()), Some(&Variant::Nil));

        // Test overwriting with different types
        dict.set("dynamic".into(), 42.into());
        assert_eq!(dict.get(&"dynamic".into()), Some(&Variant::Int(42)));

        dict.set("dynamic".into(), "string".into());
        assert_eq!(dict.get(&"dynamic".into()), Some(&Variant::String("string".to_string())));
    }
}
