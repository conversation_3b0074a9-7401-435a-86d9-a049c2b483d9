//! Comprehensive Array implementation for dynamic collections with heterogeneous typing.
//!
//! This module provides a complete Array implementation using Vec for efficient
//! dynamic storage with Variant types. It supports all common array operations,
//! type-safe access methods, and comprehensive utility functions for data management,
//! collections processing, and dynamic arrays.

use std::fmt;
use std::ops::{Index, IndexMut};
use super::Variant;

/// ### A dynamic array for storing elements with heterogeneous typing.
///
/// Array provides a vector-based collection that can store any Variant types
/// as elements. It offers efficient O(1) access time for indexed operations and
/// supports all common array operations with type-safe access methods.
/// 
/// ## Use Cases
///
/// Array is ideal for:
/// - **Dynamic Collections**: Lists with mixed data types
/// - **Game State**: Inventory items, player stats, level data
/// - **Scripting Integration**: Function parameters and return values
/// - **Data Processing**: Flexible data pipelines
/// - **Configuration Lists**: Settings arrays with various types
/// - **Event Systems**: Parameter lists for events
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct Array {
    /// Internal vector storage for elements.
    data: Vec<Variant>,
}

impl Array {
    /// ### Creates a new empty Array.
    ///
    /// Initializes an empty array with default capacity. The array
    /// will automatically grow as elements are added.
    ///
    /// # Returns
    /// A new empty Array instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let arr = Array::new();
    /// assert!(arr.is_empty());
    /// assert_eq!(arr.size(), 0);
    /// ```
    #[inline]
    pub fn new() -> Self {
        Self {
            data: Vec::new(),
        }
    }

    /// ### Creates a new Array with the specified capacity.
    ///
    /// Pre-allocates space for at least the specified number of elements,
    /// which can improve performance when the final size is known in advance.
    ///
    /// # Arguments
    /// * `capacity` - The initial capacity to reserve
    ///
    /// # Returns
    /// A new Array with pre-allocated capacity.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let arr = Array::with_capacity(100);
    /// assert!(arr.is_empty());
    /// // No reallocation needed for first 100 elements
    /// ```
    #[inline]
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            data: Vec::with_capacity(capacity),
        }
    }

    /// ### Creates a new Array from a slice of Variants.
    ///
    /// Copies all elements from the provided slice into a new array.
    /// This is useful for creating arrays from existing data.
    ///
    /// # Arguments
    /// * `slice` - The slice of Variants to copy
    ///
    /// # Returns
    /// A new Array containing copies of the slice elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let data = [1.into(), 2.into(), 3.into()];
    /// let arr = Array::from_slice(&data);
    /// assert_eq!(arr.size(), 3);
    /// ```
    #[inline]
    pub fn from_slice(slice: &[Variant]) -> Self {
        Self {
            data: slice.to_vec(),
        }
    }

    /// ### Gets the number of elements in the array.
    ///
    /// Returns the current number of elements stored in the array.
    /// This operation is O(1) and does not iterate through the elements.
    ///
    /// # Returns
    /// The number of elements in the array.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// assert_eq!(arr.size(), 0);
    ///
    /// arr.push_back(42.into());
    /// assert_eq!(arr.size(), 1);
    /// ```
    #[inline]
    pub fn size(&self) -> usize {
        self.data.len()
    }

    /// ### Checks if the array is empty.
    ///
    /// Returns true if the array contains no elements.
    /// This is equivalent to checking if size() == 0 but may be more readable.
    ///
    /// # Returns
    /// True if the array is empty, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// assert!(arr.is_empty());
    ///
    /// arr.push_back("element".into());
    /// assert!(!arr.is_empty());
    /// ```
    #[inline]
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    /// ### Gets the element at the specified index.
    ///
    /// Returns a reference to the element if the index is valid, otherwise returns None.
    /// This operation is O(1).
    ///
    /// # Arguments
    /// * `index` - The index to access (0-based)
    ///
    /// # Returns
    /// Some(&Variant) if the index is valid, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("hello".into());
    /// arr.push_back(42.into());
    ///
    /// assert_eq!(arr.get(0), Some(&Variant::String("hello".to_string())));
    /// assert_eq!(arr.get(1), Some(&Variant::Int(42)));
    /// assert_eq!(arr.get(2), None);
    /// ```
    #[inline]
    pub fn get(&self, index: usize) -> Option<&Variant> {
        self.data.get(index)
    }

    /// ### Sets the element at the specified index.
    ///
    /// Updates the element at the given index if it exists. Returns the previous
    /// value if the index was valid, otherwise returns None.
    ///
    /// # Arguments
    /// * `index` - The index to update (0-based)
    /// * `value` - The new value to set
    ///
    /// # Returns
    /// Some(Variant) with the previous value if index was valid, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("old".into());
    ///
    /// assert_eq!(arr.set(0, "new".into()), Some(Variant::String("old".to_string())));
    /// assert_eq!(arr.get(0), Some(&Variant::String("new".to_string())));
    /// assert_eq!(arr.set(1, "invalid".into()), None);
    /// ```
    #[inline]
    pub fn set(&mut self, index: usize, value: Variant) -> Option<Variant> {
        if index < self.data.len() {
            Some(std::mem::replace(&mut self.data[index], value))
        } else {
            None
        }
    }

    /// ### Gets the first element in the array.
    ///
    /// Returns a reference to the first element if the array is not empty.
    /// This operation is O(1).
    ///
    /// # Returns
    /// Some(&Variant) if the array is not empty, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// assert_eq!(arr.front(), None);
    ///
    /// arr.push_back("first".into());
    /// arr.push_back("second".into());
    /// assert_eq!(arr.front(), Some(&Variant::String("first".to_string())));
    /// ```
    #[inline]
    pub fn front(&self) -> Option<&Variant> {
        self.data.first()
    }

    /// ### Gets the last element in the array.
    ///
    /// Returns a reference to the last element if the array is not empty.
    /// This operation is O(1).
    ///
    /// # Returns
    /// Some(&Variant) if the array is not empty, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// assert_eq!(arr.back(), None);
    ///
    /// arr.push_back("first".into());
    /// arr.push_back("last".into());
    /// assert_eq!(arr.back(), Some(&Variant::String("last".to_string())));
    /// ```
    #[inline]
    pub fn back(&self) -> Option<&Variant> {
        self.data.last()
    }

    /// ### Adds an element to the end of the array.
    ///
    /// Appends the element to the back of the array. This operation is O(1) amortized,
    /// though it may occasionally require reallocation.
    ///
    /// # Arguments
    /// * `value` - The element to add
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back(1.into());
    /// arr.push_back("hello".into());
    /// assert_eq!(arr.size(), 2);
    /// ```
    #[inline]
    pub fn push_back(&mut self, value: Variant) {
        self.data.push(value);
    }

    /// ### Adds an element to the beginning of the array.
    ///
    /// Inserts the element at the front of the array, shifting all existing elements
    /// to the right. This operation is O(n) due to the need to shift elements.
    ///
    /// # Arguments
    /// * `value` - The element to add
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("second".into());
    /// arr.push_front("first".into());
    /// assert_eq!(arr.get(0), Some(&Variant::String("first".to_string())));
    /// ```
    #[inline]
    pub fn push_front(&mut self, value: Variant) {
        self.data.insert(0, value);
    }

    /// ### Removes and returns the last element from the array.
    ///
    /// Removes the element from the back of the array and returns it.
    /// This operation is O(1).
    ///
    /// # Returns
    /// Some(Variant) if the array was not empty, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("last".into());
    ///
    /// assert_eq!(arr.pop_back(), Some(Variant::String("last".to_string())));
    /// assert_eq!(arr.pop_back(), None);
    /// ```
    #[inline]
    pub fn pop_back(&mut self) -> Option<Variant> {
        self.data.pop()
    }

    /// ### Removes and returns the first element from the array.
    ///
    /// Removes the element from the front of the array and returns it,
    /// shifting all remaining elements to the left. This operation is O(n).
    ///
    /// # Returns
    /// Some(Variant) if the array was not empty, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("first".into());
    /// arr.push_back("second".into());
    ///
    /// assert_eq!(arr.pop_front(), Some(Variant::String("first".to_string())));
    /// assert_eq!(arr.get(0), Some(&Variant::String("second".to_string())));
    /// ```
    #[inline]
    pub fn pop_front(&mut self) -> Option<Variant> {
        if self.data.is_empty() {
            None
        } else {
            Some(self.data.remove(0))
        }
    }

    /// ### Inserts an element at the specified index.
    ///
    /// Inserts the element at the given index, shifting all elements at and after
    /// that index to the right. This operation is O(n) for the shifting.
    ///
    /// # Arguments
    /// * `index` - The index where to insert (0-based)
    /// * `value` - The element to insert
    ///
    /// # Panics
    /// Panics if index > size().
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("first".into());
    /// arr.push_back("third".into());
    /// arr.insert(1, "second".into());
    ///
    /// assert_eq!(arr.size(), 3);
    /// assert_eq!(arr.get(1), Some(&Variant::String("second".to_string())));
    /// ```
    #[inline]
    pub fn insert(&mut self, index: usize, value: Variant) {
        self.data.insert(index, value);
    }

    /// ### Removes the element at the specified index.
    ///
    /// Removes and returns the element at the given index, shifting all elements
    /// after that index to the left. This operation is O(n) for the shifting.
    ///
    /// # Arguments
    /// * `index` - The index of the element to remove (0-based)
    ///
    /// # Returns
    /// Some(Variant) if the index was valid, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("keep".into());
    /// arr.push_back("remove".into());
    /// arr.push_back("keep".into());
    ///
    /// assert_eq!(arr.remove(1), Some(Variant::String("remove".to_string())));
    /// assert_eq!(arr.size(), 2);
    /// ```
    #[inline]
    pub fn remove(&mut self, index: usize) -> Option<Variant> {
        if index < self.data.len() {
            Some(self.data.remove(index))
        } else {
            None
        }
    }

    /// ### Finds the first occurrence of the specified value.
    ///
    /// Searches for the first element that equals the given value and returns its index.
    /// This operation is O(n) as it may need to check all elements.
    ///
    /// # Arguments
    /// * `value` - The value to search for
    ///
    /// # Returns
    /// Some(usize) with the index if found, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("apple".into());
    /// arr.push_back("banana".into());
    /// arr.push_back("apple".into());
    ///
    /// assert_eq!(arr.find(&"banana".into()), Some(1));
    /// assert_eq!(arr.find(&"apple".into()), Some(0)); // First occurrence
    /// assert_eq!(arr.find(&"orange".into()), None);
    /// ```
    #[inline]
    pub fn find(&self, value: &Variant) -> Option<usize> {
        self.data.iter().position(|x| x == value)
    }

    /// ### Checks if the array contains the specified value.
    ///
    /// Returns true if any element in the array equals the given value.
    /// This operation is O(n) as it may need to check all elements.
    ///
    /// # Arguments
    /// * `value` - The value to search for
    ///
    /// # Returns
    /// True if the value is found, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("item".into());
    ///
    /// assert!(arr.has(&"item".into()));
    /// assert!(!arr.has(&"missing".into()));
    /// ```
    #[inline]
    pub fn has(&self, value: &Variant) -> bool {
        self.data.contains(value)
    }

    /// ### Removes all elements from the array.
    ///
    /// Clears the array, making it empty. The allocated capacity is retained
    /// for potential reuse, which can improve performance for repeated use.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back(1.into());
    /// arr.push_back(2.into());
    ///
    /// arr.clear();
    /// assert!(arr.is_empty());
    /// assert_eq!(arr.size(), 0);
    /// ```
    #[inline]
    pub fn clear(&mut self) {
        self.data.clear();
    }

    /// ### Resizes the array to the specified length.
    ///
    /// If the new size is larger than the current size, the array is extended
    /// with copies of the provided value. If smaller, the array is truncated.
    ///
    /// # Arguments
    /// * `new_size` - The new size for the array
    /// * `value` - The value to use for new elements if growing
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back(1.into());
    ///
    /// arr.resize(3, 0.into());
    /// assert_eq!(arr.size(), 3);
    /// assert_eq!(arr.get(2), Some(&Variant::Int(0)));
    ///
    /// arr.resize(1, 0.into());
    /// assert_eq!(arr.size(), 1);
    /// ```
    #[inline]
    pub fn resize(&mut self, new_size: usize, value: Variant) {
        self.data.resize(new_size, value);
    }

    /// ### Creates a shallow copy of the array.
    ///
    /// Returns a new array containing the same elements.
    /// The elements themselves are cloned, creating independent copies.
    ///
    /// # Returns
    /// A new Array with the same contents.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut original = Array::new();
    /// original.push_back("data".into());
    /// original.push_back(42.into());
    ///
    /// let copy = original.duplicate();
    /// assert_eq!(original, copy);
    /// ```
    #[inline]
    pub fn duplicate(&self) -> Self {
        Self {
            data: self.data.clone(),
        }
    }

    /// ### Reverses the order of elements in the array.
    ///
    /// Reverses the array in-place, so the first element becomes the last
    /// and vice versa. This operation is O(n/2).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back(1.into());
    /// arr.push_back(2.into());
    /// arr.push_back(3.into());
    ///
    /// arr.reverse();
    /// assert_eq!(arr.get(0), Some(&Variant::Int(3)));
    /// assert_eq!(arr.get(2), Some(&Variant::Int(1)));
    /// ```
    #[inline]
    pub fn reverse(&mut self) {
        self.data.reverse();
    }

    /// ### Sorts the elements in the array.
    ///
    /// Sorts the array in-place using the natural ordering of Variants.
    /// The sorting is stable and uses an efficient algorithm.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back(3.into());
    /// arr.push_back(1.into());
    /// arr.push_back(2.into());
    ///
    /// arr.sort();
    /// assert_eq!(arr.get(0), Some(&Variant::Int(1)));
    /// assert_eq!(arr.get(1), Some(&Variant::Int(2)));
    /// assert_eq!(arr.get(2), Some(&Variant::Int(3)));
    /// ```
    #[inline]
    pub fn sort(&mut self) {
        self.data.sort_by(|a, b| {
            // Custom comparison for Variants
            use std::cmp::Ordering;
            match (a, b) {
                (Variant::Nil, Variant::Nil) => Ordering::Equal,
                (Variant::Nil, _) => Ordering::Less,
                (_, Variant::Nil) => Ordering::Greater,
                (Variant::Bool(a), Variant::Bool(b)) => a.cmp(b),
                (Variant::Int(a), Variant::Int(b)) => a.cmp(b),
                (Variant::Float(a), Variant::Float(b)) => a.partial_cmp(b).unwrap_or(Ordering::Equal),
                (Variant::String(a), Variant::String(b)) => a.cmp(b),
                // For different types, use type ordering
                (Variant::Bool(_), _) => Ordering::Less,
                (_, Variant::Bool(_)) => Ordering::Greater,
                (Variant::Int(_), _) => Ordering::Less,
                (_, Variant::Int(_)) => Ordering::Greater,
                (Variant::Float(_), _) => Ordering::Less,
                (_, Variant::Float(_)) => Ordering::Greater,
                (Variant::String(_), _) => Ordering::Less,
                (_, Variant::String(_)) => Ordering::Greater,
                // For remaining types, consider them equal for sorting purposes
                _ => Ordering::Equal,
            }
        });
    }

    /// ### Returns an iterator over the elements in the array.
    ///
    /// Creates an iterator that yields references to all elements in order.
    /// This is the most efficient way to iterate over array elements.
    ///
    /// # Returns
    /// An iterator over the array elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back("a".into());
    /// arr.push_back("b".into());
    ///
    /// for element in arr.iter() {
    ///     println!("{}", element);
    /// }
    /// ```
    #[inline]
    pub fn iter(&self) -> impl Iterator<Item = &Variant> {
        self.data.iter()
    }

    /// ### Returns a mutable iterator over the elements in the array.
    ///
    /// Creates an iterator that yields mutable references to all elements in order.
    /// This allows modification of elements during iteration.
    ///
    /// # Returns
    /// A mutable iterator over the array elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Array, Variant};
    /// let mut arr = Array::new();
    /// arr.push_back(1.into());
    /// arr.push_back(2.into());
    ///
    /// for element in arr.iter_mut() {
    ///     if let Variant::Int(ref mut n) = element {
    ///         *n *= 2;
    ///     }
    /// }
    /// ```
    #[inline]
    pub fn iter_mut(&mut self) -> impl Iterator<Item = &mut Variant> {
        self.data.iter_mut()
    }
}

impl Default for Array {
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for Array {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[")?;
        for (i, element) in self.data.iter().enumerate() {
            if i > 0 {
                write!(f, ", ")?;
            }
            write!(f, "{}", element)?;
        }
        write!(f, "]")
    }
}

impl Index<usize> for Array {
    type Output = Variant;

    #[inline]
    fn index(&self, index: usize) -> &Self::Output {
        &self.data[index]
    }
}

impl IndexMut<usize> for Array {
    #[inline]
    fn index_mut(&mut self, index: usize) -> &mut Self::Output {
        &mut self.data[index]
    }
}

impl From<Vec<Variant>> for Array {
    #[inline]
    fn from(data: Vec<Variant>) -> Self {
        Self { data }
    }
}

impl From<Array> for Vec<Variant> {
    #[inline]
    fn from(arr: Array) -> Self {
        arr.data
    }
}

impl IntoIterator for Array {
    type Item = Variant;
    type IntoIter = std::vec::IntoIter<Variant>;

    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.data.into_iter()
    }
}

impl<'a> IntoIterator for &'a Array {
    type Item = &'a Variant;
    type IntoIter = std::slice::Iter<'a, Variant>;

    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.data.iter()
    }
}

impl<'a> IntoIterator for &'a mut Array {
    type Item = &'a mut Variant;
    type IntoIter = std::slice::IterMut<'a, Variant>;

    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.data.iter_mut()
    }
}

impl FromIterator<Variant> for Array {
    #[inline]
    fn from_iter<T: IntoIterator<Item = Variant>>(iter: T) -> Self {
        Self {
            data: Vec::from_iter(iter),
        }
    }
}

impl Extend<Variant> for Array {
    #[inline]
    fn extend<T: IntoIterator<Item = Variant>>(&mut self, iter: T) {
        self.data.extend(iter);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_array_creation() {
        let arr = Array::new();
        assert!(arr.is_empty());
        assert_eq!(arr.size(), 0);

        let arr_with_capacity = Array::with_capacity(10);
        assert!(arr_with_capacity.is_empty());
        assert_eq!(arr_with_capacity.size(), 0);

        let data = [1.into(), 2.into(), 3.into()];
        let arr_from_slice = Array::from_slice(&data);
        assert_eq!(arr_from_slice.size(), 3);
        assert_eq!(arr_from_slice.get(1), Some(&Variant::Int(2)));
    }

    #[test]
    fn test_array_basic_operations() {
        let mut arr = Array::new();

        // Test push_back and get
        arr.push_back("first".into());
        arr.push_back(42.into());
        assert_eq!(arr.size(), 2);
        assert!(!arr.is_empty());
        assert_eq!(arr.get(0), Some(&Variant::String("first".to_string())));
        assert_eq!(arr.get(1), Some(&Variant::Int(42)));
        assert_eq!(arr.get(2), None);

        // Test front and back
        assert_eq!(arr.front(), Some(&Variant::String("first".to_string())));
        assert_eq!(arr.back(), Some(&Variant::Int(42)));

        // Test set
        assert_eq!(arr.set(0, "updated".into()), Some(Variant::String("first".to_string())));
        assert_eq!(arr.get(0), Some(&Variant::String("updated".to_string())));
        assert_eq!(arr.set(5, "invalid".into()), None);
    }

    #[test]
    fn test_array_push_pop_operations() {
        let mut arr = Array::new();

        // Test push_back and pop_back
        arr.push_back("last".into());
        assert_eq!(arr.pop_back(), Some(Variant::String("last".to_string())));
        assert_eq!(arr.pop_back(), None);

        // Test push_front and pop_front
        arr.push_back("second".into());
        arr.push_front("first".into());
        assert_eq!(arr.get(0), Some(&Variant::String("first".to_string())));
        assert_eq!(arr.get(1), Some(&Variant::String("second".to_string())));

        assert_eq!(arr.pop_front(), Some(Variant::String("first".to_string())));
        assert_eq!(arr.get(0), Some(&Variant::String("second".to_string())));
    }

    #[test]
    fn test_array_insert_remove() {
        let mut arr = Array::new();
        arr.push_back("first".into());
        arr.push_back("third".into());

        // Test insert
        arr.insert(1, "second".into());
        assert_eq!(arr.size(), 3);
        assert_eq!(arr.get(1), Some(&Variant::String("second".to_string())));

        // Test remove
        assert_eq!(arr.remove(1), Some(Variant::String("second".to_string())));
        assert_eq!(arr.size(), 2);
        assert_eq!(arr.get(1), Some(&Variant::String("third".to_string())));
        assert_eq!(arr.remove(5), None);
    }

    #[test]
    fn test_array_find_has() {
        let mut arr = Array::new();
        arr.push_back("apple".into());
        arr.push_back("banana".into());
        arr.push_back("apple".into());

        // Test find
        assert_eq!(arr.find(&"banana".into()), Some(1));
        assert_eq!(arr.find(&"apple".into()), Some(0)); // First occurrence
        assert_eq!(arr.find(&"orange".into()), None);

        // Test has
        assert!(arr.has(&"banana".into()));
        assert!(arr.has(&"apple".into()));
        assert!(!arr.has(&"orange".into()));
    }

    #[test]
    fn test_array_clear_resize() {
        let mut arr = Array::new();
        arr.push_back(1.into());
        arr.push_back(2.into());
        arr.push_back(3.into());

        // Test clear
        assert_eq!(arr.size(), 3);
        arr.clear();
        assert!(arr.is_empty());
        assert_eq!(arr.size(), 0);

        // Test resize
        arr.push_back(1.into());
        arr.resize(3, 0.into());
        assert_eq!(arr.size(), 3);
        assert_eq!(arr.get(2), Some(&Variant::Int(0)));

        arr.resize(1, 0.into());
        assert_eq!(arr.size(), 1);
    }

    #[test]
    fn test_array_duplicate() {
        let mut original = Array::new();
        original.push_back("data".into());
        original.push_back(42.into());

        let copy = original.duplicate();
        assert_eq!(original, copy);
        assert_eq!(copy.size(), 2);
        assert_eq!(copy.get(0), Some(&Variant::String("data".to_string())));
    }

    #[test]
    fn test_array_reverse() {
        let mut arr = Array::new();
        arr.push_back(1.into());
        arr.push_back(2.into());
        arr.push_back(3.into());

        arr.reverse();
        assert_eq!(arr.get(0), Some(&Variant::Int(3)));
        assert_eq!(arr.get(1), Some(&Variant::Int(2)));
        assert_eq!(arr.get(2), Some(&Variant::Int(1)));
    }

    #[test]
    fn test_array_sort() {
        let mut arr = Array::new();
        arr.push_back(3.into());
        arr.push_back(1.into());
        arr.push_back(2.into());

        arr.sort();
        assert_eq!(arr.get(0), Some(&Variant::Int(1)));
        assert_eq!(arr.get(1), Some(&Variant::Int(2)));
        assert_eq!(arr.get(2), Some(&Variant::Int(3)));

        // Test mixed types sorting
        let mut mixed_arr = Array::new();
        mixed_arr.push_back(Variant::nil());
        mixed_arr.push_back(true.into());
        mixed_arr.push_back(false.into());
        mixed_arr.push_back(1.into());

        mixed_arr.sort();
        assert_eq!(mixed_arr.get(0), Some(&Variant::Nil));
        assert_eq!(mixed_arr.get(1), Some(&Variant::Bool(false)));
        assert_eq!(mixed_arr.get(2), Some(&Variant::Bool(true)));
        assert_eq!(mixed_arr.get(3), Some(&Variant::Int(1)));
    }

    #[test]
    fn test_array_iteration() {
        let mut arr = Array::new();
        arr.push_back(1.into());
        arr.push_back(2.into());
        arr.push_back(3.into());

        // Test iter
        let values: Vec<_> = arr.iter().collect();
        assert_eq!(values.len(), 3);

        // Test into_iter
        let arr_copy = arr.duplicate();
        let owned_values: Vec<_> = arr_copy.into_iter().collect();
        assert_eq!(owned_values.len(), 3);

        // Test iter_mut
        for element in arr.iter_mut() {
            if let Variant::Int(n) = element {
                *n *= 2;
            }
        }
        assert_eq!(arr.get(0), Some(&Variant::Int(2)));
        assert_eq!(arr.get(1), Some(&Variant::Int(4)));
        assert_eq!(arr.get(2), Some(&Variant::Int(6)));
    }

    #[test]
    fn test_array_indexing() {
        let mut arr = Array::new();
        arr.push_back("test".into());
        arr.push_back(42.into());

        // Test index access
        assert_eq!(arr[0], Variant::String("test".to_string()));
        assert_eq!(arr[1], Variant::Int(42));

        // Test index mutation
        arr[0] = "updated".into();
        assert_eq!(arr.get(0), Some(&Variant::String("updated".to_string())));
    }

    #[test]
    fn test_array_from_iterator() {
        let values = vec![1.into(), 2.into(), 3.into()];
        let arr: Array = values.into_iter().collect();

        assert_eq!(arr.size(), 3);
        assert_eq!(arr.get(0), Some(&Variant::Int(1)));
        assert_eq!(arr.get(1), Some(&Variant::Int(2)));
        assert_eq!(arr.get(2), Some(&Variant::Int(3)));
    }

    #[test]
    fn test_array_extend() {
        let mut arr = Array::new();
        arr.push_back(1.into());

        let additional = vec![2.into(), 3.into(), 4.into()];
        arr.extend(additional);

        assert_eq!(arr.size(), 4);
        assert_eq!(arr.get(3), Some(&Variant::Int(4)));
    }

    #[test]
    fn test_array_display() {
        let mut arr = Array::new();
        arr.push_back("hello".into());
        arr.push_back(42.into());
        arr.push_back(true.into());

        let display_string = format!("{}", arr);
        assert_eq!(display_string, "[\"hello\", 42, true]");

        let empty_arr = Array::new();
        assert_eq!(format!("{}", empty_arr), "[]");
    }

    #[test]
    fn test_array_edge_cases() {
        let mut arr = Array::new();

        // Test empty array operations
        assert_eq!(arr.front(), None);
        assert_eq!(arr.back(), None);
        assert_eq!(arr.pop_back(), None);
        assert_eq!(arr.pop_front(), None);
        assert!(!arr.has(&"anything".into()));
        assert_eq!(arr.find(&"anything".into()), None);

        // Test single element operations
        arr.push_back("only".into());
        assert_eq!(arr.front(), Some(&Variant::String("only".to_string())));
        assert_eq!(arr.back(), Some(&Variant::String("only".to_string())));
        assert_eq!(arr.pop_front(), Some(Variant::String("only".to_string())));
        assert!(arr.is_empty());

        // Test mixed types
        arr.push_back(Variant::nil());
        arr.push_back(42.into());
        arr.push_back("string".into());
        arr.push_back(true.into());
        assert_eq!(arr.size(), 4);
    }
}
