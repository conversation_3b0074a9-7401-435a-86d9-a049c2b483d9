//! Comprehensive Godot-compatible String wrapper implementation for UTF-8 text handling.
//!
//! This module provides a complete String wrapper that maintains compatibility with <PERSON><PERSON>'s
//! String class while leveraging Rust's efficient string handling. It includes comprehensive
//! string manipulation, pattern matching, numeric conversion, and path handling operations
//! commonly used in game development and scripting environments.

use std::fmt;
use std::hash::{Hash, Hasher};
use std::path::Path;

/// ### A Godot-compatible String wrapper for UTF-8 text handling and manipulation.
///
/// String provides comprehensive text processing capabilities with full UTF-8 support,
/// maintaining API compatibility with <PERSON><PERSON>'s String class while leveraging Rust's
/// efficient string handling and memory safety guarantees.
///
/// ## Features
///
/// String supports:
/// - **UTF-8 Text Processing**: Full Unicode support with proper character handling
/// - **Pattern Matching**: Advanced search, replace, and matching operations
/// - **Case Conversion**: Upper, lower, and capitalization transformations
/// - **Numeric Conversion**: Safe conversion to/from integers and floating-point numbers
/// - **Path Manipulation**: File path operations for game asset management
/// - **Formatting Operations**: Padding, trimming, and text formatting
/// - **Godot Compatibility**: API matches <PERSON><PERSON>'s String class behavior
///
/// ## Use Cases
///
/// String is ideal for:
/// - **Game Text**: UI strings, dialogue, localization
/// - **Asset Paths**: File and resource path manipulation
/// - **Configuration**: Settings and parameter parsing
/// - **Scripting**: Dynamic text processing in game scripts
/// - **Data Processing**: Text parsing and formatting operations
///
/// # Examples
/// ```
/// # use verturion::core::variant::String;
/// // Create and manipulate strings
/// let mut text = String::from("Hello, World!");
/// assert!(text.contains("World"));
/// assert!(text.begins_with("Hello"));
///
/// // Case conversion
/// let upper = text.to_upper();
/// let lower = text.to_lower();
///
/// // Numeric conversion
/// let number_str = String::from("42");
/// assert_eq!(number_str.to_int(), Some(42));
///
/// // Path operations
/// let path = String::from("/game/assets/texture.png");
/// assert_eq!(path.get_extension(), "png");
/// assert_eq!(path.get_file(), "texture.png");
/// ```
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct String {
    /// The internal UTF-8 string data
    data: std::string::String,
}

impl String {
    /// ### Creates a new empty String.
    ///
    /// # Returns
    /// An empty String instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let empty = String::new();
    /// assert!(empty.is_empty());
    /// ```
    #[inline]
    pub fn new() -> Self {
        Self {
            data: std::string::String::new(),
        }
    }

    /// ### Creates a String with the specified capacity.
    ///
    /// Pre-allocates memory for the string to avoid reallocations during growth.
    ///
    /// # Parameters
    /// - `capacity`: The initial capacity in bytes
    ///
    /// # Returns
    /// An empty String with the specified capacity.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let string = String::with_capacity(100);
    /// assert!(string.is_empty());
    /// ```
    #[inline]
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            data: std::string::String::with_capacity(capacity),
        }
    }

    /// ### Returns the length of the string in bytes.
    ///
    /// Note: This returns the byte length, not the character count for Unicode strings.
    ///
    /// # Returns
    /// The length of the string in bytes.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello");
    /// assert_eq!(text.length(), 5);
    /// ```
    #[inline]
    pub fn length(&self) -> usize {
        self.data.len()
    }

    /// ### Returns the number of Unicode characters in the string.
    ///
    /// This counts actual Unicode characters, which may differ from byte length.
    ///
    /// # Returns
    /// The number of Unicode characters.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello 🌍");
    /// assert_eq!(text.char_count(), 7);
    /// ```
    #[inline]
    pub fn char_count(&self) -> usize {
        self.data.chars().count()
    }

    /// ### Checks if the string is empty.
    ///
    /// # Returns
    /// `true` if the string contains no characters.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let empty = String::new();
    /// let text = String::from("Hello");
    /// assert!(empty.is_empty());
    /// assert!(!text.is_empty());
    /// ```
    #[inline]
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    /// ### Checks if the string contains the specified substring.
    ///
    /// # Parameters
    /// - `substring`: The substring to search for
    ///
    /// # Returns
    /// `true` if the substring is found.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!");
    /// assert!(text.contains("World"));
    /// assert!(!text.contains("Rust"));
    /// ```
    #[inline]
    pub fn contains(&self, substring: &str) -> bool {
        self.data.contains(substring)
    }

    /// ### Checks if the string begins with the specified prefix.
    ///
    /// # Parameters
    /// - `prefix`: The prefix to check for
    ///
    /// # Returns
    /// `true` if the string starts with the prefix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!");
    /// assert!(text.begins_with("Hello"));
    /// assert!(!text.begins_with("World"));
    /// ```
    #[inline]
    pub fn begins_with(&self, prefix: &str) -> bool {
        self.data.starts_with(prefix)
    }

    /// ### Checks if the string ends with the specified suffix.
    ///
    /// # Parameters
    /// - `suffix`: The suffix to check for
    ///
    /// # Returns
    /// `true` if the string ends with the suffix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!");
    /// assert!(text.ends_with("World!"));
    /// assert!(!text.ends_with("Hello"));
    /// ```
    #[inline]
    pub fn ends_with(&self, suffix: &str) -> bool {
        self.data.ends_with(suffix)
    }

    /// ### Finds the first occurrence of a substring.
    ///
    /// # Parameters
    /// - `substring`: The substring to search for
    ///
    /// # Returns
    /// The byte index of the first occurrence, or None if not found.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!");
    /// assert_eq!(text.find("World"), Some(7));
    /// assert_eq!(text.find("Rust"), None);
    /// ```
    #[inline]
    pub fn find(&self, substring: &str) -> Option<usize> {
        self.data.find(substring)
    }

    /// ### Finds the last occurrence of a substring.
    ///
    /// # Parameters
    /// - `substring`: The substring to search for
    ///
    /// # Returns
    /// The byte index of the last occurrence, or None if not found.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, Hello, World!");
    /// assert_eq!(text.rfind("Hello"), Some(7));
    /// assert_eq!(text.rfind("Rust"), None);
    /// ```
    #[inline]
    pub fn rfind(&self, substring: &str) -> Option<usize> {
        self.data.rfind(substring)
    }

    /// ### Returns a substring from the specified range.
    ///
    /// # Parameters
    /// - `start`: The starting byte index
    /// - `end`: The ending byte index (exclusive)
    ///
    /// # Returns
    /// A new String containing the substring, or empty if indices are invalid.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!");
    /// let sub = text.substr(7, 12);
    /// assert_eq!(sub.as_str(), "World");
    /// ```
    #[inline]
    pub fn substr(&self, start: usize, end: usize) -> String {
        if start >= self.data.len() || start >= end {
            return String::new();
        }

        let end = end.min(self.data.len());
        String::from(&self.data[start..end])
    }

    /// ### Returns a substring from the specified start position with given length.
    ///
    /// # Parameters
    /// - `start`: The starting byte index
    /// - `length`: The number of bytes to include
    ///
    /// # Returns
    /// A new String containing the substring.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!");
    /// let sub = text.substring(7, 5);
    /// assert_eq!(sub.as_str(), "World");
    /// ```
    #[inline]
    pub fn substring(&self, start: usize, length: usize) -> String {
        self.substr(start, start + length)
    }

    /// ### Converts the string to uppercase.
    ///
    /// # Returns
    /// A new String with all characters converted to uppercase.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!");
    /// let upper = text.to_upper();
    /// assert_eq!(upper.as_str(), "HELLO, WORLD!");
    /// ```
    #[inline]
    pub fn to_upper(&self) -> String {
        String::from(self.data.to_uppercase())
    }

    /// ### Converts the string to lowercase.
    ///
    /// # Returns
    /// A new String with all characters converted to lowercase.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!");
    /// let lower = text.to_lower();
    /// assert_eq!(lower.as_str(), "hello, world!");
    /// ```
    #[inline]
    pub fn to_lower(&self) -> String {
        String::from(self.data.to_lowercase())
    }

    /// ### Capitalizes the first character of the string.
    ///
    /// # Returns
    /// A new String with the first character capitalized and the rest lowercase.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("hello world");
    /// let capitalized = text.capitalize();
    /// assert_eq!(capitalized.as_str(), "Hello world");
    /// ```
    #[inline]
    pub fn capitalize(&self) -> String {
        if self.data.is_empty() {
            return String::new();
        }

        let mut chars = self.data.chars();
        match chars.next() {
            None => String::new(),
            Some(first) => {
                let capitalized = first.to_uppercase().collect::<std::string::String>() + &chars.as_str().to_lowercase();
                String::from(capitalized)
            }
        }
    }

    /// ### Replaces all occurrences of a substring with another string.
    ///
    /// # Parameters
    /// - `from`: The substring to replace
    /// - `to`: The replacement string
    ///
    /// # Returns
    /// A new String with all occurrences replaced.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World! Hello!");
    /// let replaced = text.replace("Hello", "Hi");
    /// assert_eq!(replaced.as_str(), "Hi, World! Hi!");
    /// ```
    #[inline]
    pub fn replace(&self, from: &str, to: &str) -> String {
        String::from(self.data.replace(from, to))
    }

    /// ### Replaces the first occurrence of a substring.
    ///
    /// # Parameters
    /// - `from`: The substring to replace
    /// - `to`: The replacement string
    ///
    /// # Returns
    /// A new String with the first occurrence replaced.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World! Hello!");
    /// let replaced = text.replace_first("Hello", "Hi");
    /// assert_eq!(replaced.as_str(), "Hi, World! Hello!");
    /// ```
    #[inline]
    pub fn replace_first(&self, from: &str, to: &str) -> String {
        if let Some(index) = self.data.find(from) {
            let mut result = std::string::String::with_capacity(self.data.len());
            result.push_str(&self.data[..index]);
            result.push_str(to);
            result.push_str(&self.data[index + from.len()..]);
            String::from(result)
        } else {
            self.clone()
        }
    }

    /// ### Splits the string by a delimiter.
    ///
    /// # Parameters
    /// - `delimiter`: The delimiter to split by
    ///
    /// # Returns
    /// A vector of String parts.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("apple,banana,cherry");
    /// let parts = text.split(",");
    /// assert_eq!(parts.len(), 3);
    /// assert_eq!(parts[0].as_str(), "apple");
    /// ```
    #[inline]
    pub fn split(&self, delimiter: &str) -> Vec<String> {
        self.data
            .split(delimiter)
            .map(|s| String::from(s))
            .collect()
    }

    /// ### Joins a vector of strings with this string as delimiter.
    ///
    /// # Parameters
    /// - `parts`: The strings to join
    ///
    /// # Returns
    /// A new String with all parts joined.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let delimiter = String::from(",");
    /// let parts = vec![String::from("apple"), String::from("banana")];
    /// let joined = delimiter.join(&parts);
    /// assert_eq!(joined.as_str(), "apple,banana");
    /// ```
    #[inline]
    pub fn join(&self, parts: &[String]) -> String {
        let string_parts: Vec<&str> = parts.iter().map(|s| s.as_str()).collect();
        String::from(string_parts.join(&self.data))
    }

    /// ### Removes leading and trailing whitespace.
    ///
    /// # Returns
    /// A new String with whitespace trimmed.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("  Hello, World!  ");
    /// let trimmed = text.strip_edges();
    /// assert_eq!(trimmed.as_str(), "Hello, World!");
    /// ```
    #[inline]
    pub fn strip_edges(&self) -> String {
        String::from(self.data.trim())
    }

    /// ### Removes leading whitespace.
    ///
    /// # Returns
    /// A new String with leading whitespace removed.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("  Hello, World!");
    /// let trimmed = text.lstrip();
    /// assert_eq!(trimmed.as_str(), "Hello, World!");
    /// ```
    #[inline]
    pub fn lstrip(&self) -> String {
        String::from(self.data.trim_start())
    }

    /// ### Removes trailing whitespace.
    ///
    /// # Returns
    /// A new String with trailing whitespace removed.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!  ");
    /// let trimmed = text.rstrip();
    /// assert_eq!(trimmed.as_str(), "Hello, World!");
    /// ```
    #[inline]
    pub fn rstrip(&self) -> String {
        String::from(self.data.trim_end())
    }

    /// ### Attempts to convert the string to an integer.
    ///
    /// # Returns
    /// Some(i64) if the string represents a valid integer, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let number = String::from("42");
    /// let invalid = String::from("hello");
    /// assert_eq!(number.to_int(), Some(42));
    /// assert_eq!(invalid.to_int(), None);
    /// ```
    #[inline]
    pub fn to_int(&self) -> Option<i64> {
        self.data.trim().parse().ok()
    }

    /// ### Attempts to convert the string to a floating-point number.
    ///
    /// # Returns
    /// Some(f64) if the string represents a valid float, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let number = String::from("3.14");
    /// let invalid = String::from("hello");
    /// assert_eq!(number.to_float(), Some(3.14));
    /// assert_eq!(invalid.to_float(), None);
    /// ```
    #[inline]
    pub fn to_float(&self) -> Option<f64> {
        self.data.trim().parse().ok()
    }

    /// ### Checks if the string represents a valid integer.
    ///
    /// # Returns
    /// `true` if the string can be parsed as an integer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let number = String::from("42");
    /// let invalid = String::from("hello");
    /// assert!(number.is_valid_int());
    /// assert!(!invalid.is_valid_int());
    /// ```
    #[inline]
    pub fn is_valid_int(&self) -> bool {
        self.to_int().is_some()
    }

    /// ### Checks if the string represents a valid floating-point number.
    ///
    /// # Returns
    /// `true` if the string can be parsed as a float.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let number = String::from("3.14");
    /// let invalid = String::from("hello");
    /// assert!(number.is_valid_float());
    /// assert!(!invalid.is_valid_float());
    /// ```
    #[inline]
    pub fn is_valid_float(&self) -> bool {
        self.to_float().is_some()
    }

    /// ### Extracts the filename from a file path.
    ///
    /// # Returns
    /// The filename portion of the path, or the entire string if no path separator is found.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let path = String::from("/game/assets/texture.png");
    /// assert_eq!(path.get_file(), "texture.png");
    /// ```
    #[inline]
    pub fn get_file(&self) -> &str {
        Path::new(&self.data)
            .file_name()
            .and_then(|name| name.to_str())
            .unwrap_or(&self.data)
    }

    /// ### Extracts the file extension from a file path.
    ///
    /// # Returns
    /// The file extension without the dot, or empty string if no extension.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let path = String::from("/game/assets/texture.png");
    /// assert_eq!(path.get_extension(), "png");
    /// ```
    #[inline]
    pub fn get_extension(&self) -> &str {
        Path::new(&self.data)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
    }

    /// ### Extracts the base directory from a file path.
    ///
    /// # Returns
    /// The directory portion of the path, or empty string if no directory.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let path = String::from("/game/assets/texture.png");
    /// assert_eq!(path.get_base_dir(), "/game/assets");
    /// ```
    #[inline]
    pub fn get_base_dir(&self) -> &str {
        Path::new(&self.data)
            .parent()
            .and_then(|parent| parent.to_str())
            .unwrap_or("")
    }

    /// ### Extracts the filename without extension.
    ///
    /// # Returns
    /// The filename without its extension.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let path = String::from("/game/assets/texture.png");
    /// assert_eq!(path.get_basename(), "texture");
    /// ```
    #[inline]
    pub fn get_basename(&self) -> &str {
        Path::new(&self.data)
            .file_stem()
            .and_then(|stem| stem.to_str())
            .unwrap_or(&self.data)
    }

    /// ### Pads the string to the specified length with spaces on the left.
    ///
    /// # Parameters
    /// - `length`: The target length
    ///
    /// # Returns
    /// A new String padded to the specified length.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("42");
    /// let padded = text.pad_zeros(5);
    /// assert_eq!(padded.as_str(), "00042");
    /// ```
    #[inline]
    pub fn pad_zeros(&self, length: usize) -> String {
        if self.data.len() >= length {
            self.clone()
        } else {
            let padding = length - self.data.len();
            String::from("0".repeat(padding) + &self.data)
        }
    }

    /// ### Pads the string to the specified length with the given character.
    ///
    /// # Parameters
    /// - `length`: The target length
    /// - `character`: The character to pad with
    ///
    /// # Returns
    /// A new String padded to the specified length.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("hello");
    /// let padded = text.lpad(10, ' ');
    /// assert_eq!(padded.as_str(), "     hello");
    /// ```
    #[inline]
    pub fn lpad(&self, length: usize, character: char) -> String {
        if self.data.len() >= length {
            self.clone()
        } else {
            let padding = length - self.data.len();
            String::from(character.to_string().repeat(padding) + &self.data)
        }
    }

    /// ### Pads the string to the specified length with the given character on the right.
    ///
    /// # Parameters
    /// - `length`: The target length
    /// - `character`: The character to pad with
    ///
    /// # Returns
    /// A new String padded to the specified length.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("hello");
    /// let padded = text.rpad(10, '.');
    /// assert_eq!(padded.as_str(), "hello.....");
    /// ```
    #[inline]
    pub fn rpad(&self, length: usize, character: char) -> String {
        if self.data.len() >= length {
            self.clone()
        } else {
            let padding = length - self.data.len();
            String::from(self.data.clone() + &character.to_string().repeat(padding))
        }
    }

    /// ### Returns a reference to the internal string data.
    ///
    /// # Returns
    /// A string slice of the internal data.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let text = String::from("Hello, World!");
    /// assert_eq!(text.as_str(), "Hello, World!");
    /// ```
    #[inline]
    pub fn as_str(&self) -> &str {
        &self.data
    }

    /// ### Appends a string to this string.
    ///
    /// # Parameters
    /// - `other`: The string to append
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let mut text = String::from("Hello");
    /// text.push_str(", World!");
    /// assert_eq!(text.as_str(), "Hello, World!");
    /// ```
    #[inline]
    pub fn push_str(&mut self, other: &str) {
        self.data.push_str(other);
    }

    /// ### Appends a character to this string.
    ///
    /// # Parameters
    /// - `ch`: The character to append
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let mut text = String::from("Hello");
    /// text.push('!');
    /// assert_eq!(text.as_str(), "Hello!");
    /// ```
    #[inline]
    pub fn push(&mut self, ch: char) {
        self.data.push(ch);
    }

    /// ### Clears the string, removing all content.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::String;
    /// let mut text = String::from("Hello, World!");
    /// text.clear();
    /// assert!(text.is_empty());
    /// ```
    #[inline]
    pub fn clear(&mut self) {
        self.data.clear();
    }
}

impl Default for String {
    /// Returns an empty String.
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for String {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.data)
    }
}

impl Hash for String {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.data.hash(state);
    }
}

impl From<&str> for String {
    #[inline]
    fn from(s: &str) -> Self {
        Self {
            data: s.to_string(),
        }
    }
}

impl From<std::string::String> for String {
    #[inline]
    fn from(s: std::string::String) -> Self {
        Self { data: s }
    }
}

impl From<String> for std::string::String {
    #[inline]
    fn from(s: String) -> Self {
        s.data
    }
}

impl AsRef<str> for String {
    #[inline]
    fn as_ref(&self) -> &str {
        &self.data
    }
}

impl std::ops::Add<&str> for String {
    type Output = String;

    #[inline]
    fn add(self, other: &str) -> Self::Output {
        String::from(self.data + other)
    }
}

impl std::ops::Add<String> for String {
    type Output = String;

    #[inline]
    fn add(self, other: String) -> Self::Output {
        String::from(self.data + &other.data)
    }
}

impl std::ops::AddAssign<&str> for String {
    #[inline]
    fn add_assign(&mut self, other: &str) {
        self.data.push_str(other);
    }
}

impl std::ops::AddAssign<String> for String {
    #[inline]
    fn add_assign(&mut self, other: String) {
        self.data.push_str(&other.data);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_string_creation() {
        let empty = String::new();
        assert!(empty.is_empty());
        assert_eq!(empty.length(), 0);

        let with_capacity = String::with_capacity(100);
        assert!(with_capacity.is_empty());

        let from_str = String::from("Hello, World!");
        assert_eq!(from_str.as_str(), "Hello, World!");
        assert_eq!(from_str.length(), 13);

        let default = String::default();
        assert!(default.is_empty());
    }

    #[test]
    fn test_string_properties() {
        let text = String::from("Hello, 🌍!");
        assert_eq!(text.length(), 12); // Byte length (🌍 is 4 bytes)
        assert_eq!(text.char_count(), 9); // Character count
        assert!(!text.is_empty());

        let empty = String::new();
        assert!(empty.is_empty());
        assert_eq!(empty.length(), 0);
        assert_eq!(empty.char_count(), 0);
    }

    #[test]
    fn test_string_search_operations() {
        let text = String::from("Hello, World! Hello!");

        // Contains
        assert!(text.contains("World"));
        assert!(text.contains("Hello"));
        assert!(!text.contains("Rust"));

        // Begins with / ends with
        assert!(text.begins_with("Hello"));
        assert!(!text.begins_with("World"));
        assert!(text.ends_with("Hello!"));
        assert!(!text.ends_with("World"));

        // Find operations
        assert_eq!(text.find("World"), Some(7));
        assert_eq!(text.find("Hello"), Some(0));
        assert_eq!(text.find("Rust"), None);

        assert_eq!(text.rfind("Hello"), Some(14));
        assert_eq!(text.rfind("World"), Some(7));
        assert_eq!(text.rfind("Rust"), None);
    }

    #[test]
    fn test_string_substring_operations() {
        let text = String::from("Hello, World!");

        let sub1 = text.substr(7, 12);
        assert_eq!(sub1.as_str(), "World");

        let sub2 = text.substring(7, 5);
        assert_eq!(sub2.as_str(), "World");

        // Edge cases
        let empty_sub = text.substr(20, 25);
        assert!(empty_sub.is_empty());

        let invalid_sub = text.substr(10, 5);
        assert!(invalid_sub.is_empty());
    }

    #[test]
    fn test_string_case_conversion() {
        let text = String::from("Hello, World!");

        let upper = text.to_upper();
        assert_eq!(upper.as_str(), "HELLO, WORLD!");

        let lower = text.to_lower();
        assert_eq!(lower.as_str(), "hello, world!");

        let capitalized = String::from("hello world").capitalize();
        assert_eq!(capitalized.as_str(), "Hello world");

        let empty_cap = String::new().capitalize();
        assert!(empty_cap.is_empty());
    }

    #[test]
    fn test_string_replacement_operations() {
        let text = String::from("Hello, World! Hello!");

        let replaced_all = text.replace("Hello", "Hi");
        assert_eq!(replaced_all.as_str(), "Hi, World! Hi!");

        let replaced_first = text.replace_first("Hello", "Hi");
        assert_eq!(replaced_first.as_str(), "Hi, World! Hello!");

        let no_replace = text.replace("Rust", "Python");
        assert_eq!(no_replace.as_str(), "Hello, World! Hello!");
    }

    #[test]
    fn test_string_split_join_operations() {
        let text = String::from("apple,banana,cherry");
        let parts = text.split(",");
        assert_eq!(parts.len(), 3);
        assert_eq!(parts[0].as_str(), "apple");
        assert_eq!(parts[1].as_str(), "banana");
        assert_eq!(parts[2].as_str(), "cherry");

        let delimiter = String::from(",");
        let joined = delimiter.join(&parts);
        assert_eq!(joined.as_str(), "apple,banana,cherry");

        let single_split = String::from("no-delimiter").split(",");
        assert_eq!(single_split.len(), 1);
        assert_eq!(single_split[0].as_str(), "no-delimiter");
    }

    #[test]
    fn test_string_trimming_operations() {
        let text = String::from("  Hello, World!  ");

        let trimmed = text.strip_edges();
        assert_eq!(trimmed.as_str(), "Hello, World!");

        let left_trimmed = text.lstrip();
        assert_eq!(left_trimmed.as_str(), "Hello, World!  ");

        let right_trimmed = text.rstrip();
        assert_eq!(right_trimmed.as_str(), "  Hello, World!");

        let no_whitespace = String::from("NoWhitespace");
        assert_eq!(no_whitespace.strip_edges().as_str(), "NoWhitespace");
    }

    #[test]
    fn test_string_numeric_conversion() {
        // Valid integers
        let int_str = String::from("42");
        assert_eq!(int_str.to_int(), Some(42));
        assert!(int_str.is_valid_int());

        let negative_int = String::from("-123");
        assert_eq!(negative_int.to_int(), Some(-123));
        assert!(negative_int.is_valid_int());

        // Valid floats
        let float_str = String::from("3.14");
        assert_eq!(float_str.to_float(), Some(3.14));
        assert!(float_str.is_valid_float());

        let negative_float = String::from("-2.5");
        assert_eq!(negative_float.to_float(), Some(-2.5));
        assert!(negative_float.is_valid_float());

        // Invalid conversions
        let invalid = String::from("hello");
        assert_eq!(invalid.to_int(), None);
        assert_eq!(invalid.to_float(), None);
        assert!(!invalid.is_valid_int());
        assert!(!invalid.is_valid_float());

        // Whitespace handling
        let padded = String::from("  42  ");
        assert_eq!(padded.to_int(), Some(42));
        assert!(padded.is_valid_int());
    }

    #[test]
    fn test_string_path_operations() {
        let path = String::from("/game/assets/texture.png");

        assert_eq!(path.get_file(), "texture.png");
        assert_eq!(path.get_extension(), "png");
        assert_eq!(path.get_base_dir(), "/game/assets");
        assert_eq!(path.get_basename(), "texture");

        // Path without extension
        let no_ext = String::from("/game/assets/readme");
        assert_eq!(no_ext.get_file(), "readme");
        assert_eq!(no_ext.get_extension(), "");
        assert_eq!(no_ext.get_basename(), "readme");

        // Just filename
        let filename = String::from("texture.png");
        assert_eq!(filename.get_file(), "texture.png");
        assert_eq!(filename.get_extension(), "png");
        assert_eq!(filename.get_base_dir(), "");
        assert_eq!(filename.get_basename(), "texture");

        // Windows-style path (note: on Unix systems, backslashes are treated as part of filename)
        let windows_path = String::from("C:\\game\\assets\\texture.png");
        // On Unix, this is treated as a single filename, on Windows it would be parsed correctly
        if cfg!(windows) {
            assert_eq!(windows_path.get_file(), "texture.png");
            assert_eq!(windows_path.get_extension(), "png");
        } else {
            // On Unix, the whole string is treated as a filename
            assert_eq!(windows_path.get_file(), "C:\\game\\assets\\texture.png");
        }
    }

    #[test]
    fn test_string_padding_operations() {
        let text = String::from("42");

        let zero_padded = text.pad_zeros(5);
        assert_eq!(zero_padded.as_str(), "00042");

        let left_padded = text.lpad(5, ' ');
        assert_eq!(left_padded.as_str(), "   42");

        let right_padded = text.rpad(5, '.');
        assert_eq!(right_padded.as_str(), "42...");

        // No padding needed
        let long_text = String::from("hello world");
        let no_pad = long_text.pad_zeros(5);
        assert_eq!(no_pad.as_str(), "hello world");
    }

    #[test]
    fn test_string_mutation_operations() {
        let mut text = String::from("Hello");

        text.push_str(", World");
        assert_eq!(text.as_str(), "Hello, World");

        text.push('!');
        assert_eq!(text.as_str(), "Hello, World!");

        text.clear();
        assert!(text.is_empty());
    }

    #[test]
    fn test_string_operators() {
        let hello = String::from("Hello");
        let world = String::from(", World!");

        // Addition operators
        let combined1 = hello.clone() + ", World!";
        assert_eq!(combined1.as_str(), "Hello, World!");

        let combined2 = hello.clone() + world.clone();
        assert_eq!(combined2.as_str(), "Hello, World!");

        // Assignment operators
        let mut text = hello.clone();
        text += ", World!";
        assert_eq!(text.as_str(), "Hello, World!");

        let mut text2 = hello.clone();
        text2 += world;
        assert_eq!(text2.as_str(), "Hello, World!");
    }

    #[test]
    fn test_string_conversions() {
        // From &str
        let from_str = String::from("Hello");
        assert_eq!(from_str.as_str(), "Hello");

        // From std::string::String
        let std_string = std::string::String::from("World");
        let from_std = String::from(std_string);
        assert_eq!(from_std.as_str(), "World");

        // To std::string::String
        let to_std: std::string::String = from_str.into();
        assert_eq!(to_std, "Hello");

        // AsRef<str>
        let text = String::from("Test");
        let as_ref: &str = text.as_ref();
        assert_eq!(as_ref, "Test");
    }

    #[test]
    fn test_string_display_and_hash() {
        let text = String::from("Hello, World!");
        let display_str = format!("{}", text);
        assert_eq!(display_str, "Hello, World!");

        // Hash consistency
        use std::collections::HashMap;
        let mut map = HashMap::new();
        map.insert(text.clone(), 42);
        assert_eq!(map.get(&text), Some(&42));
    }

    #[test]
    fn test_string_edge_cases() {
        // Empty string operations
        let empty = String::new();
        assert_eq!(empty.find("test"), None);
        assert_eq!(empty.substr(0, 5).as_str(), "");
        assert_eq!(empty.to_upper().as_str(), "");
        assert_eq!(empty.replace("a", "b").as_str(), "");

        // Unicode handling
        let unicode = String::from("Hello, 🌍! 你好");
        assert!(unicode.contains("🌍"));
        assert!(unicode.contains("你好"));
        assert_eq!(unicode.char_count(), 12); // "Hello, 🌍! 你好" = 12 characters

        // Very long strings
        let long_str = "a".repeat(10000);
        let long_string = String::from(long_str);
        assert_eq!(long_string.length(), 10000);
        assert!(long_string.contains("a"));

        // Special characters in paths
        let special_path = String::from("/path/with spaces/file.ext");
        assert_eq!(special_path.get_file(), "file.ext");
        assert_eq!(special_path.get_extension(), "ext");
    }

    #[test]
    fn test_string_equality() {
        let str1 = String::from("Hello");
        let str2 = String::from("Hello");
        let str3 = String::from("World");

        assert_eq!(str1, str2);
        assert_ne!(str1, str3);

        // Clone equality
        let cloned = str1.clone();
        assert_eq!(str1, cloned);
    }
}
