/// ### Godot-compatible typed dictionary implementation.
///
/// This module provides a type-safe dictionary implementation following <PERSON><PERSON>'s
/// TypedDictionary patterns. It ensures compile-time type safety for both keys
/// and values while maintaining runtime compatibility with the existing Variant system.
///
/// ## Type Safety Features
///
/// - **Compile-Time Type Checking**: All operations are type-safe at compile time
/// - **Runtime Type Validation**: Safe conversion from untyped Dictionary with validation
/// - **Generic Implementation**: Works with any key/value types that can be stored in Variant
/// - **Performance Optimized**: Efficient operations with zero-cost abstractions
/// - **Godot Compatible**: API and behavior patterns matching Godot Engine
/// - **Collection Traits**: Full support for iteration, indexing, and standard traits
///
/// ## Examples
///
/// ```rust
/// use verturion::core::variant::{TypedDictionary, Variant};
/// use verturion::core::variant::{Int, Float};
///
/// // Create typed dictionaries with compile-time type safety
/// let mut scores = TypedDictionary::<Int, Float>::new();
/// scores.set(Int::new(1), Float::new(100.5));
/// scores.set(Int::new(2), Float::new(95.0));
///
/// // Type-safe operations
/// assert_eq!(scores.size(), 2);
/// assert_eq!(scores.get(&Int::new(1)), Some(&Float::new(100.5)));
///
/// // Safe conversion from untyped Dictionary
/// let untyped = Dictionary::from([
///     (Int::new(3).into(), Float::new(87.5).into()),
///     (Int::new(4).into(), Float::new(92.0).into())
/// ]);
/// let typed = TypedDictionary::<Int, Float>::try_from(untyped)?;
/// ```
use std::fmt;
use std::ops::{Index, IndexMut};
use std::collections::HashMap;
use std::collections::hash_map::{Iter, IterMut, IntoIter, Keys, Values, ValuesMut};
use std::hash::Hash;
use super::{Dictionary, Variant};

/// ### Type-safe dictionary with compile-time type checking.
///
/// A generic dictionary implementation that provides compile-time type safety
/// for both keys and values while maintaining compatibility with Godot's TypedDictionary
/// behavior and the existing Variant system.
///
/// ## Type Parameters
///
/// - `K`: The key type that must implement Hash, Eq, Clone and conversion to/from Variant
/// - `V`: The value type that must implement Clone and conversion to/from Variant
///
/// ## Type Safety
///
/// All operations are type-safe at compile time, preventing runtime type errors
/// that could occur with untyped Dictionary operations. The TypedDictionary ensures that
/// only keys of type K and values of type V can be stored and retrieved.
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::variant::TypedDictionary;
/// # use verturion::core::variant::{Int, Float};
/// // Create a typed dictionary mapping integers to floats
/// let mut grades = TypedDictionary::<Int, Float>::new();
/// grades.set(Int::new(101), Float::new(95.5));
/// grades.set(Int::new(102), Float::new(87.0));
///
/// // Type-safe access
/// assert_eq!(grades.get(&Int::new(101)), Some(&Float::new(95.5)));
/// assert_eq!(grades.size(), 2);
///
/// // Compile-time type checking prevents errors
/// // grades.set("string", 42); // This would not compile!
/// ```
#[derive(Clone, Debug)]
pub struct TypedDictionary<K, V> {
    /// Internal storage using HashMap for efficient operations
    map: HashMap<K, V>,
}

impl<K, V> TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Creates a new empty TypedDictionary.
    ///
    /// Returns a TypedDictionary with no key-value pairs and default capacity.
    /// The dictionary will allocate memory as pairs are added.
    ///
    /// # Returns
    /// New empty TypedDictionary instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let dict = TypedDictionary::<Int, Float>::new();
    /// assert_eq!(dict.size(), 0);
    /// assert!(dict.is_empty());
    /// ```
    #[inline]
    pub fn new() -> Self {
        Self {
            map: HashMap::new(),
        }
    }

    /// ### Creates a new TypedDictionary with the specified capacity.
    ///
    /// Pre-allocates memory for the specified number of key-value pairs to avoid
    /// reallocations during initial population of the dictionary.
    ///
    /// # Parameters
    /// - `capacity`: Number of key-value pairs to pre-allocate space for
    ///
    /// # Returns
    /// New empty TypedDictionary with pre-allocated capacity.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let dict = TypedDictionary::<Int, Float>::with_capacity(100);
    /// assert_eq!(dict.size(), 0);
    /// ```
    #[inline]
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            map: HashMap::with_capacity(capacity),
        }
    }

    /// ### Sets a key-value pair in the dictionary.
    ///
    /// Inserts or updates the value associated with the given key.
    /// If the key already exists, the old value is replaced and returned.
    /// This operation is type-safe and will only accept keys of type K and values of type V.
    ///
    /// # Parameters
    /// - `key`: Key to associate with the value
    /// - `value`: Value to store
    ///
    /// # Returns
    /// The previous value if the key existed, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// assert_eq!(dict.set(Int::new(1), Float::new(10.5)), None);
    /// assert_eq!(dict.set(Int::new(1), Float::new(20.5)), Some(Float::new(10.5)));
    /// ```
    #[inline]
    pub fn set(&mut self, key: K, value: V) -> Option<V> {
        self.map.insert(key, value)
    }

    /// ### Gets a reference to the value associated with the key.
    ///
    /// Returns a reference to the value if the key exists in the dictionary.
    /// This operation is type-safe and returns a value of type V.
    ///
    /// # Parameters
    /// - `key`: Key to look up
    ///
    /// # Returns
    /// Some(&V) if the key exists, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(42.5));
    ///
    /// assert_eq!(dict.get(&Int::new(1)), Some(&Float::new(42.5)));
    /// assert_eq!(dict.get(&Int::new(2)), None);
    /// ```
    #[inline]
    pub fn get(&self, key: &K) -> Option<&V> {
        self.map.get(key)
    }

    /// ### Gets a mutable reference to the value associated with the key.
    ///
    /// Returns a mutable reference to the value if the key exists in the dictionary.
    /// This allows modification of the value in place.
    ///
    /// # Parameters
    /// - `key`: Key to look up
    ///
    /// # Returns
    /// Some(&mut V) if the key exists, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(42.5));
    ///
    /// if let Some(value) = dict.get_mut(&Int::new(1)) {
    ///     *value = Float::new(100.0);
    /// }
    /// assert_eq!(dict.get(&Int::new(1)), Some(&Float::new(100.0)));
    /// ```
    #[inline]
    pub fn get_mut(&mut self, key: &K) -> Option<&mut V> {
        self.map.get_mut(key)
    }

    /// ### Checks if the dictionary contains the specified key.
    ///
    /// Returns true if the dictionary contains a key equal to the given value.
    /// Uses the key's Hash and Eq implementations for comparison.
    ///
    /// # Parameters
    /// - `key`: Key to search for
    ///
    /// # Returns
    /// True if the key is found, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(42.5));
    ///
    /// assert!(dict.has(&Int::new(1)));
    /// assert!(!dict.has(&Int::new(2)));
    /// ```
    #[inline]
    pub fn has(&self, key: &K) -> bool {
        self.map.contains_key(key)
    }

    /// ### Removes a key-value pair from the dictionary.
    ///
    /// Removes the key and its associated value from the dictionary.
    /// Returns the value if the key existed, None otherwise.
    ///
    /// # Parameters
    /// - `key`: Key to remove
    ///
    /// # Returns
    /// The removed value if the key existed, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(42.5));
    ///
    /// assert_eq!(dict.erase(&Int::new(1)), Some(Float::new(42.5)));
    /// assert_eq!(dict.erase(&Int::new(1)), None);
    /// assert_eq!(dict.size(), 0);
    /// ```
    #[inline]
    pub fn erase(&mut self, key: &K) -> Option<V> {
        self.map.remove(key)
    }

    /// ### Removes all key-value pairs from the dictionary.
    ///
    /// After calling this method, the dictionary will be empty but will retain
    /// its allocated capacity for efficient reuse.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(42.5));
    /// dict.set(Int::new(2), Float::new(100.0));
    ///
    /// dict.clear();
    /// assert_eq!(dict.size(), 0);
    /// assert!(dict.is_empty());
    /// ```
    #[inline]
    pub fn clear(&mut self) {
        self.map.clear();
    }

    /// ### Returns the number of key-value pairs in the dictionary.
    ///
    /// This is the count of pairs currently stored in the dictionary,
    /// which may be less than the allocated capacity.
    ///
    /// # Returns
    /// Number of key-value pairs in the dictionary.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// assert_eq!(dict.size(), 0);
    ///
    /// dict.set(Int::new(1), Float::new(42.5));
    /// assert_eq!(dict.size(), 1);
    /// ```
    #[inline]
    pub fn size(&self) -> usize {
        self.map.len()
    }

    /// ### Checks if the dictionary is empty.
    ///
    /// Returns true if the dictionary contains no key-value pairs, false otherwise.
    /// This is equivalent to checking if size() == 0.
    ///
    /// # Returns
    /// True if the dictionary is empty, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// assert!(dict.is_empty());
    ///
    /// dict.set(Int::new(1), Float::new(42.5));
    /// assert!(!dict.is_empty());
    /// ```
    #[inline]
    pub fn is_empty(&self) -> bool {
        self.map.is_empty()
    }

    /// ### Reserves capacity for at least additional key-value pairs.
    ///
    /// Reserves capacity for at least `additional` more key-value pairs to be inserted.
    /// The collection may reserve more space to avoid frequent reallocations.
    ///
    /// # Parameters
    /// - `additional`: Number of additional pairs to reserve space for
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.reserve(100);
    /// ```
    #[inline]
    pub fn reserve(&mut self, additional: usize) {
        self.map.reserve(additional);
    }

    /// ### Shrinks the capacity to fit the current size.
    ///
    /// Releases any excess capacity to minimize memory usage.
    /// After calling this method, capacity will be close to size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::with_capacity(100);
    /// dict.set(Int::new(1), Float::new(42.5));
    /// dict.set(Int::new(2), Float::new(100.0));
    ///
    /// dict.shrink_to_fit();
    /// ```
    #[inline]
    pub fn shrink_to_fit(&mut self) {
        self.map.shrink_to_fit();
    }

    /// ### Gets a value or inserts a default if the key doesn't exist.
    ///
    /// Returns a mutable reference to the value associated with the key.
    /// If the key doesn't exist, inserts the provided default value first.
    ///
    /// # Parameters
    /// - `key`: Key to look up or insert
    /// - `default`: Default value to insert if key doesn't exist
    ///
    /// # Returns
    /// Mutable reference to the value (existing or newly inserted).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    ///
    /// let value = dict.get_or_insert(Int::new(1), Float::new(0.0));
    /// *value = Float::new(42.5);
    ///
    /// assert_eq!(dict.get(&Int::new(1)), Some(&Float::new(42.5)));
    /// ```
    #[inline]
    pub fn get_or_insert(&mut self, key: K, default: V) -> &mut V {
        self.map.entry(key).or_insert(default)
    }

    /// ### Gets a value or inserts a computed default if the key doesn't exist.
    ///
    /// Returns a mutable reference to the value associated with the key.
    /// If the key doesn't exist, computes and inserts a default value using the provided closure.
    ///
    /// # Parameters
    /// - `key`: Key to look up or insert
    /// - `default_fn`: Function to compute the default value
    ///
    /// # Returns
    /// Mutable reference to the value (existing or newly computed).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    ///
    /// let value = dict.get_or_insert_with(Int::new(1), || Float::new(42.5));
    /// assert_eq!(*value, Float::new(42.5));
    /// ```
    #[inline]
    pub fn get_or_insert_with<F>(&mut self, key: K, default_fn: F) -> &mut V
    where
        F: FnOnce() -> V,
    {
        self.map.entry(key).or_insert_with(default_fn)
    }

    /// ### Creates a duplicate of the dictionary.
    ///
    /// Returns a new TypedDictionary containing clones of all key-value pairs.
    /// This is equivalent to calling clone() but follows Godot naming.
    ///
    /// # Returns
    /// New TypedDictionary with cloned key-value pairs.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(42.5));
    /// dict.set(Int::new(2), Float::new(100.0));
    ///
    /// let duplicate = dict.duplicate();
    /// assert_eq!(dict.size(), duplicate.size());
    /// ```
    #[inline]
    pub fn duplicate(&self) -> Self {
        self.clone()
    }

    /// ### Merges another TypedDictionary into this one.
    ///
    /// Inserts all key-value pairs from the other dictionary into this one.
    /// If a key exists in both dictionaries, the value from the other dictionary overwrites this one.
    ///
    /// # Parameters
    /// - `other`: Other TypedDictionary to merge from
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict1 = TypedDictionary::<Int, Float>::new();
    /// dict1.set(Int::new(1), Float::new(10.0));
    ///
    /// let mut dict2 = TypedDictionary::<Int, Float>::new();
    /// dict2.set(Int::new(2), Float::new(20.0));
    /// dict2.set(Int::new(3), Float::new(30.0));
    ///
    /// dict1.merge(dict2);
    /// assert_eq!(dict1.size(), 3);
    /// ```
    #[inline]
    pub fn merge(&mut self, other: Self) {
        for (key, value) in other.map {
            self.map.insert(key, value);
        }
    }

    /// ### Returns an iterator over the keys.
    ///
    /// Returns an iterator that yields references to each key in the dictionary.
    /// The order of iteration is arbitrary but consistent.
    ///
    /// # Returns
    /// Iterator over references to keys.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(10.0));
    /// dict.set(Int::new(2), Float::new(20.0));
    ///
    /// for key in dict.keys() {
    ///     println!("Key: {}", key);
    /// }
    /// ```
    #[inline]
    pub fn keys(&self) -> Keys<K, V> {
        self.map.keys()
    }

    /// ### Returns an iterator over the values.
    ///
    /// Returns an iterator that yields references to each value in the dictionary.
    /// The order of iteration is arbitrary but consistent with keys().
    ///
    /// # Returns
    /// Iterator over references to values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(10.0));
    /// dict.set(Int::new(2), Float::new(20.0));
    ///
    /// for value in dict.values() {
    ///     println!("Value: {}", value);
    /// }
    /// ```
    #[inline]
    pub fn values(&self) -> Values<K, V> {
        self.map.values()
    }

    /// ### Returns a mutable iterator over the values.
    ///
    /// Returns an iterator that yields mutable references to each value in the dictionary.
    /// Allows modification of values while iterating.
    ///
    /// # Returns
    /// Iterator over mutable references to values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(10.0));
    /// dict.set(Int::new(2), Float::new(20.0));
    ///
    /// for value in dict.values_mut() {
    ///     *value = Float::new(value.value() * 2.0);
    /// }
    /// ```
    #[inline]
    pub fn values_mut(&mut self) -> ValuesMut<K, V> {
        self.map.values_mut()
    }

    /// ### Returns an iterator over key-value pairs.
    ///
    /// Returns an iterator that yields references to each key-value pair.
    /// The order of iteration is arbitrary but consistent.
    ///
    /// # Returns
    /// Iterator over references to key-value pairs.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(10.0));
    /// dict.set(Int::new(2), Float::new(20.0));
    ///
    /// for (key, value) in dict.iter() {
    ///     println!("{}: {}", key, value);
    /// }
    /// ```
    #[inline]
    pub fn iter(&self) -> Iter<K, V> {
        self.map.iter()
    }

    /// ### Returns a mutable iterator over key-value pairs.
    ///
    /// Returns an iterator that yields references to keys and mutable references to values.
    /// Allows modification of values while iterating.
    ///
    /// # Returns
    /// Iterator over key references and mutable value references.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(10.0));
    /// dict.set(Int::new(2), Float::new(20.0));
    ///
    /// for (key, value) in dict.iter_mut() {
    ///     *value = Float::new(value.value() + key.value() as f64);
    /// }
    /// ```
    #[inline]
    pub fn iter_mut(&mut self) -> IterMut<K, V> {
        self.map.iter_mut()
    }

    /// ### Converts the TypedDictionary to an untyped Dictionary.
    ///
    /// Creates a new Dictionary containing Variant representations of all key-value pairs.
    /// This allows interoperability with untyped collection APIs.
    ///
    /// # Returns
    /// New Dictionary containing all pairs as Variants.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut typed_dict = TypedDictionary::<Int, Float>::new();
    /// typed_dict.set(Int::new(1), Float::new(42.5));
    /// typed_dict.set(Int::new(2), Float::new(100.0));
    ///
    /// let untyped_dict = typed_dict.to_dictionary();
    /// assert_eq!(untyped_dict.size(), 2);
    /// ```
    #[inline]
    pub fn to_dictionary(&self) -> Dictionary {
        let mut dict = Dictionary::new();
        for (key, value) in &self.map {
            dict.set(key.clone().into(), value.clone().into());
        }
        dict
    }

    /// ### Creates a TypedDictionary from an untyped Dictionary.
    ///
    /// Attempts to convert all key-value pairs from the Dictionary to types K and V.
    /// Returns an error if any key or value cannot be converted to the target types.
    ///
    /// # Parameters
    /// - `dictionary`: Untyped Dictionary to convert
    ///
    /// # Returns
    /// Result containing TypedDictionary on success, error on failure.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Dictionary, Int, Float};
    /// let untyped = Dictionary::from([
    ///     (Int::new(1).into(), Float::new(42.5).into()),
    ///     (Int::new(2).into(), Float::new(100.0).into())
    /// ]);
    ///
    /// let typed = TypedDictionary::<Int, Float>::from_dictionary(untyped)?;
    /// assert_eq!(typed.size(), 2);
    /// ```
    pub fn from_dictionary(dictionary: Dictionary) -> Result<Self, &'static str> {
        let mut map = HashMap::with_capacity(dictionary.size());

        for (key_variant, value_variant) in dictionary.iter() {
            let key = K::try_from(key_variant.clone())
                .map_err(|_| "Key type conversion failed")?;
            let value = V::try_from(value_variant.clone())
                .map_err(|_| "Value type conversion failed")?;
            map.insert(key, value);
        }

        Ok(Self { map })
    }
}

// Trait implementations for TypedDictionary
impl<K, V> Default for TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Creates the default TypedDictionary value.
    ///
    /// Returns an empty TypedDictionary with no key-value pairs.
    /// This is equivalent to calling TypedDictionary::new().
    ///
    /// # Returns
    /// Empty TypedDictionary instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let dict = TypedDictionary::<Int, Float>::default();
    /// assert!(dict.is_empty());
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}

impl<K, V> PartialEq for TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant> + PartialEq,
{
    /// ### Checks equality between two TypedDictionaries.
    ///
    /// Returns true if both dictionaries have the same size and all corresponding
    /// key-value pairs are equal according to their PartialEq implementations.
    ///
    /// # Parameters
    /// - `other`: Other TypedDictionary to compare with
    ///
    /// # Returns
    /// True if dictionaries are equal, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict1 = TypedDictionary::<Int, Float>::new();
    /// let mut dict2 = TypedDictionary::<Int, Float>::new();
    ///
    /// dict1.set(Int::new(1), Float::new(42.5));
    /// dict2.set(Int::new(1), Float::new(42.5));
    ///
    /// assert_eq!(dict1, dict2);
    /// ```
    #[inline]
    fn eq(&self, other: &Self) -> bool {
        self.map == other.map
    }
}

impl<K, V> Index<&K> for TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Output = V;

    /// ### Gets a reference to the value at the specified key.
    ///
    /// Returns a reference to the value associated with the given key.
    /// Panics if the key is not found in the dictionary.
    ///
    /// # Parameters
    /// - `key`: Key to look up
    ///
    /// # Returns
    /// Reference to the value associated with the key.
    ///
    /// # Panics
    /// Panics if the key is not found.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(42.5));
    ///
    /// assert_eq!(dict[&Int::new(1)], Float::new(42.5));
    /// ```
    #[inline]
    fn index(&self, key: &K) -> &Self::Output {
        &self.map[key]
    }
}

impl<K, V> IndexMut<&K> for TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Gets a mutable reference to the value at the specified key.
    ///
    /// Returns a mutable reference to the value associated with the given key.
    /// Panics if the key is not found in the dictionary.
    ///
    /// # Parameters
    /// - `key`: Key to look up
    ///
    /// # Returns
    /// Mutable reference to the value associated with the key.
    ///
    /// # Panics
    /// Panics if the key is not found.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(42.5));
    ///
    /// dict[&Int::new(1)] = Float::new(100.0);
    /// assert_eq!(dict[&Int::new(1)], Float::new(100.0));
    /// ```
    #[inline]
    fn index_mut(&mut self, key: &K) -> &mut Self::Output {
        self.map.get_mut(key).expect("Key not found")
    }
}

impl<K, V> IntoIterator for TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Item = (K, V);
    type IntoIter = IntoIter<K, V>;

    /// ### Converts the TypedDictionary into an iterator.
    ///
    /// Consumes the TypedDictionary and returns an iterator that yields
    /// owned key-value pairs. This allows for efficient iteration without cloning.
    ///
    /// # Returns
    /// Iterator that yields owned key-value pairs.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(10.0));
    /// dict.set(Int::new(2), Float::new(20.0));
    ///
    /// for (key, value) in dict {
    ///     println!("{}: {}", key, value);
    /// }
    /// ```
    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.map.into_iter()
    }
}

impl<'a, K, V> IntoIterator for &'a TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Item = (&'a K, &'a V);
    type IntoIter = Iter<'a, K, V>;

    /// ### Converts a reference to TypedDictionary into an iterator.
    ///
    /// Returns an iterator that yields references to key-value pairs without
    /// consuming the TypedDictionary.
    ///
    /// # Returns
    /// Iterator that yields references to key-value pairs.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(10.0));
    /// dict.set(Int::new(2), Float::new(20.0));
    ///
    /// for (key, value) in &dict {
    ///     println!("{}: {}", key, value);
    /// }
    /// ```
    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.map.iter()
    }
}

impl<'a, K, V> IntoIterator for &'a mut TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Item = (&'a K, &'a mut V);
    type IntoIter = IterMut<'a, K, V>;

    /// ### Converts a mutable reference to TypedDictionary into an iterator.
    ///
    /// Returns an iterator that yields references to keys and mutable references to values
    /// without consuming the TypedDictionary.
    ///
    /// # Returns
    /// Iterator that yields key references and mutable value references.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(10.0));
    /// dict.set(Int::new(2), Float::new(20.0));
    ///
    /// for (key, value) in &mut dict {
    ///     *value = Float::new(value.value() * 2.0);
    /// }
    /// ```
    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.map.iter_mut()
    }
}

impl<K, V> fmt::Display for TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant> + fmt::Display,
    V: Clone + Into<Variant> + TryFrom<Variant> + fmt::Display,
{
    /// ### Formats the TypedDictionary for display.
    ///
    /// Provides human-readable representation of the typed dictionary
    /// showing all key-value pairs in a map format.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let mut dict = TypedDictionary::<Int, Float>::new();
    /// dict.set(Int::new(1), Float::new(10.5));
    /// dict.set(Int::new(2), Float::new(20.0));
    ///
    /// println!("{}", dict); // Shows: TypedDictionary{1: 10.5, 2: 20}
    /// ```
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "TypedDictionary{{")?;
        let mut first = true;
        for (key, value) in &self.map {
            if !first {
                write!(f, ", ")?;
            }
            write!(f, "{}: {}", key, value)?;
            first = false;
        }
        write!(f, "}}")
    }
}

impl<K, V> TryFrom<Dictionary> for TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Error = &'static str;

    /// ### Attempts to convert a Dictionary to TypedDictionary.
    ///
    /// Tries to convert all key-value pairs from the untyped Dictionary to types K and V.
    /// Returns an error if any key or value cannot be converted.
    ///
    /// # Parameters
    /// - `dictionary`: Untyped Dictionary to convert
    ///
    /// # Returns
    /// Result containing TypedDictionary on success, error message on failure.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Dictionary, Int, Float};
    /// let untyped = Dictionary::from([
    ///     (Int::new(1).into(), Float::new(42.5).into()),
    ///     (Int::new(2).into(), Float::new(100.0).into())
    /// ]);
    ///
    /// let typed: TypedDictionary<Int, Float> = untyped.try_into()?;
    /// assert_eq!(typed.size(), 2);
    /// ```
    fn try_from(dictionary: Dictionary) -> Result<Self, Self::Error> {
        Self::from_dictionary(dictionary)
    }
}

impl<K, V> FromIterator<(K, V)> for TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Creates a TypedDictionary from an iterator of key-value pairs.
    ///
    /// Collects all key-value pairs from an iterator into a new TypedDictionary.
    /// This enables using collect() with iterators to create typed dictionaries.
    ///
    /// # Parameters
    /// - `iter`: Iterator yielding key-value pairs of types (K, V)
    ///
    /// # Returns
    /// New TypedDictionary containing all pairs from the iterator.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let dict: TypedDictionary<Int, Float> = (1..=3)
    ///     .map(|i| (Int::new(i), Float::new(i as f64 * 10.0)))
    ///     .collect();
    /// assert_eq!(dict.size(), 3);
    /// ```
    #[inline]
    fn from_iter<I: IntoIterator<Item = (K, V)>>(iter: I) -> Self {
        Self {
            map: HashMap::from_iter(iter),
        }
    }
}

impl<K, V, const N: usize> From<[(K, V); N]> for TypedDictionary<K, V>
where
    K: Clone + Hash + Eq + Into<Variant> + TryFrom<Variant>,
    V: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Creates a TypedDictionary from an array of key-value pairs.
    ///
    /// Converts a fixed-size array of pairs into a TypedDictionary.
    /// This provides a convenient way to create typed dictionaries from literals.
    ///
    /// # Parameters
    /// - `array`: Fixed-size array of key-value pairs to convert
    ///
    /// # Returns
    /// New TypedDictionary containing all pairs from the array.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedDictionary, Int, Float};
    /// let dict = TypedDictionary::from([
    ///     (Int::new(1), Float::new(10.5)),
    ///     (Int::new(2), Float::new(20.0)),
    ///     (Int::new(3), Float::new(30.5))
    /// ]);
    /// assert_eq!(dict.size(), 3);
    /// ```
    #[inline]
    fn from(array: [(K, V); N]) -> Self {
        Self {
            map: HashMap::from(array),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::variant::{Int, Float, Bool};

    #[test]
    fn test_typed_dictionary_creation() {
        let dict = TypedDictionary::<Int, Float>::new();
        assert_eq!(dict.size(), 0);
        assert!(dict.is_empty());

        let dict_with_capacity = TypedDictionary::<Int, Float>::with_capacity(10);
        assert_eq!(dict_with_capacity.size(), 0);
    }

    #[test]
    fn test_typed_dictionary_set_get() {
        let mut dict = TypedDictionary::<Int, Float>::new();

        assert_eq!(dict.set(Int::new(1), Float::new(10.5)), None);
        assert_eq!(dict.set(Int::new(2), Float::new(20.0)), None);
        assert_eq!(dict.set(Int::new(1), Float::new(15.5)), Some(Float::new(10.5)));

        assert_eq!(dict.size(), 2);
        assert!(!dict.is_empty());

        assert_eq!(dict.get(&Int::new(1)), Some(&Float::new(15.5)));
        assert_eq!(dict.get(&Int::new(2)), Some(&Float::new(20.0)));
        assert_eq!(dict.get(&Int::new(3)), None);
    }

    #[test]
    fn test_typed_dictionary_get_mut() {
        let mut dict = TypedDictionary::<Int, Float>::new();
        dict.set(Int::new(1), Float::new(10.5));

        if let Some(value) = dict.get_mut(&Int::new(1)) {
            *value = Float::new(25.0);
        }

        assert_eq!(dict.get(&Int::new(1)), Some(&Float::new(25.0)));
    }

    #[test]
    fn test_typed_dictionary_has_erase() {
        let mut dict = TypedDictionary::<Int, Float>::new();
        dict.set(Int::new(1), Float::new(10.5));
        dict.set(Int::new(2), Float::new(20.0));

        assert!(dict.has(&Int::new(1)));
        assert!(dict.has(&Int::new(2)));
        assert!(!dict.has(&Int::new(3)));

        assert_eq!(dict.erase(&Int::new(1)), Some(Float::new(10.5)));
        assert_eq!(dict.erase(&Int::new(1)), None);
        assert!(!dict.has(&Int::new(1)));
        assert_eq!(dict.size(), 1);
    }

    #[test]
    fn test_typed_dictionary_clear() {
        let mut dict = TypedDictionary::<Int, Float>::new();
        dict.set(Int::new(1), Float::new(10.5));
        dict.set(Int::new(2), Float::new(20.0));

        dict.clear();
        assert_eq!(dict.size(), 0);
        assert!(dict.is_empty());
    }

    #[test]
    fn test_typed_dictionary_indexing() {
        let mut dict = TypedDictionary::<Int, Float>::new();
        dict.set(Int::new(1), Float::new(42.5));
        dict.set(Int::new(2), Float::new(100.0));

        assert_eq!(dict[&Int::new(1)], Float::new(42.5));
        assert_eq!(dict[&Int::new(2)], Float::new(100.0));

        dict[&Int::new(1)] = Float::new(99.5);
        assert_eq!(dict[&Int::new(1)], Float::new(99.5));
    }

    #[test]
    fn test_typed_dictionary_get_or_insert() {
        let mut dict = TypedDictionary::<Int, Float>::new();

        let value1 = dict.get_or_insert(Int::new(1), Float::new(10.0));
        *value1 = Float::new(15.0);

        let value2 = dict.get_or_insert(Int::new(1), Float::new(20.0));
        assert_eq!(*value2, Float::new(15.0)); // Should not insert default

        assert_eq!(dict.size(), 1);
    }

    #[test]
    fn test_typed_dictionary_get_or_insert_with() {
        let mut dict = TypedDictionary::<Int, Float>::new();

        let value = dict.get_or_insert_with(Int::new(1), || Float::new(42.5));
        assert_eq!(*value, Float::new(42.5));

        // Should not call closure again
        let value2 = dict.get_or_insert_with(Int::new(1), || Float::new(100.0));
        assert_eq!(*value2, Float::new(42.5));
    }

    #[test]
    fn test_typed_dictionary_duplicate() {
        let mut dict = TypedDictionary::<Int, Float>::new();
        dict.set(Int::new(1), Float::new(10.5));
        dict.set(Int::new(2), Float::new(20.0));

        let duplicate = dict.duplicate();
        assert_eq!(dict.size(), duplicate.size());
        assert_eq!(dict.get(&Int::new(1)), duplicate.get(&Int::new(1)));
        assert_eq!(dict.get(&Int::new(2)), duplicate.get(&Int::new(2)));

        // Ensure they are independent
        dict.set(Int::new(3), Float::new(30.0));
        assert_ne!(dict.size(), duplicate.size());
    }

    #[test]
    fn test_typed_dictionary_merge() {
        let mut dict1 = TypedDictionary::<Int, Float>::new();
        dict1.set(Int::new(1), Float::new(10.0));
        dict1.set(Int::new(2), Float::new(20.0));

        let mut dict2 = TypedDictionary::<Int, Float>::new();
        dict2.set(Int::new(2), Float::new(25.0)); // Should overwrite
        dict2.set(Int::new(3), Float::new(30.0));

        dict1.merge(dict2);
        assert_eq!(dict1.size(), 3);
        assert_eq!(dict1.get(&Int::new(1)), Some(&Float::new(10.0)));
        assert_eq!(dict1.get(&Int::new(2)), Some(&Float::new(25.0))); // Overwritten
        assert_eq!(dict1.get(&Int::new(3)), Some(&Float::new(30.0)));
    }

    #[test]
    fn test_typed_dictionary_iteration() {
        let mut dict = TypedDictionary::<Int, Float>::new();
        dict.set(Int::new(1), Float::new(10.0));
        dict.set(Int::new(2), Float::new(20.0));
        dict.set(Int::new(3), Float::new(30.0));

        // Test keys iteration
        let keys: Vec<_> = dict.keys().cloned().collect();
        assert_eq!(keys.len(), 3);
        assert!(keys.contains(&Int::new(1)));
        assert!(keys.contains(&Int::new(2)));
        assert!(keys.contains(&Int::new(3)));

        // Test values iteration
        let values: Vec<_> = dict.values().cloned().collect();
        assert_eq!(values.len(), 3);
        assert!(values.contains(&Float::new(10.0)));
        assert!(values.contains(&Float::new(20.0)));
        assert!(values.contains(&Float::new(30.0)));

        // Test mutable values iteration
        for value in dict.values_mut() {
            *value = Float::new(value.get() * 2.0);
        }
        assert_eq!(dict.get(&Int::new(1)), Some(&Float::new(20.0)));
        assert_eq!(dict.get(&Int::new(2)), Some(&Float::new(40.0)));
        assert_eq!(dict.get(&Int::new(3)), Some(&Float::new(60.0)));

        // Test key-value pairs iteration
        let mut sum = 0.0;
        for (key, value) in &dict {
            sum += key.get() as f64 + value.get();
        }
        assert_eq!(sum, 126.0); // (1+20) + (2+40) + (3+60)

        // Test mutable key-value pairs iteration
        for (key, value) in &mut dict {
            *value = Float::new(value.get() + key.get() as f64);
        }
        assert_eq!(dict.get(&Int::new(1)), Some(&Float::new(21.0)));
        assert_eq!(dict.get(&Int::new(2)), Some(&Float::new(42.0)));
        assert_eq!(dict.get(&Int::new(3)), Some(&Float::new(63.0)));

        // Test consuming iteration
        let pairs: Vec<_> = dict.into_iter().collect();
        assert_eq!(pairs.len(), 3);
    }

    #[test]
    fn test_typed_dictionary_equality() {
        let mut dict1 = TypedDictionary::<Int, Float>::new();
        let mut dict2 = TypedDictionary::<Int, Float>::new();

        dict1.set(Int::new(1), Float::new(10.5));
        dict1.set(Int::new(2), Float::new(20.0));

        dict2.set(Int::new(1), Float::new(10.5));
        dict2.set(Int::new(2), Float::new(20.0));

        assert_eq!(dict1, dict2);

        dict2.set(Int::new(3), Float::new(30.0));
        assert_ne!(dict1, dict2);
    }

    #[test]
    fn test_typed_dictionary_display() {
        let mut dict = TypedDictionary::<Int, Float>::new();
        dict.set(Int::new(1), Float::new(10.5));
        dict.set(Int::new(2), Float::new(20.0));

        let display = format!("{}", dict);
        assert!(display.contains("TypedDictionary"));
        assert!(display.contains("1"));
        assert!(display.contains("10.5"));
        assert!(display.contains("2"));
        assert!(display.contains("20"));
    }

    #[test]
    fn test_typed_dictionary_conversion_to_dictionary() {
        let mut typed_dict = TypedDictionary::<Int, Float>::new();
        typed_dict.set(Int::new(1), Float::new(42.5));
        typed_dict.set(Int::new(2), Float::new(100.0));

        let untyped_dict = typed_dict.to_dictionary();
        assert_eq!(untyped_dict.size(), 2);

        // Verify elements can be extracted as the correct types
        if let Some(value) = untyped_dict.get(&Int::new(1).into()).and_then(|v| v.as_float_wrapper()) {
            assert_eq!(value.get(), 42.5);
        } else {
            panic!("Failed to extract first value");
        }
    }

    #[test]
    fn test_typed_dictionary_conversion_from_dictionary() {
        let mut untyped = Dictionary::new();
        untyped.set(Int::new(1).into(), Float::new(42.5).into());
        untyped.set(Int::new(2).into(), Float::new(100.0).into());
        untyped.set(Int::new(3).into(), Float::new(75.5).into());

        let typed = TypedDictionary::<Int, Float>::from_dictionary(untyped).unwrap();
        assert_eq!(typed.size(), 3);
        assert_eq!(typed.get(&Int::new(1)), Some(&Float::new(42.5)));
        assert_eq!(typed.get(&Int::new(2)), Some(&Float::new(100.0)));
        assert_eq!(typed.get(&Int::new(3)), Some(&Float::new(75.5)));
    }

    #[test]
    fn test_typed_dictionary_conversion_from_dictionary_failure() {
        let mut mixed_untyped = Dictionary::new();
        mixed_untyped.set(Int::new(1).into(), Float::new(42.5).into());
        mixed_untyped.set(Int::new(2).into(), Bool::new(true).into());  // Wrong value type
        mixed_untyped.set(Int::new(3).into(), Float::new(75.5).into());

        let result = TypedDictionary::<Int, Float>::from_dictionary(mixed_untyped);
        assert!(result.is_err());

        let mut mixed_keys = Dictionary::new();
        mixed_keys.set(Int::new(1).into(), Float::new(42.5).into());
        mixed_keys.set(Float::new(2.5).into(), Float::new(100.0).into());  // Wrong key type
        mixed_keys.set(Int::new(3).into(), Float::new(75.5).into());

        let result = TypedDictionary::<Int, Float>::from_dictionary(mixed_keys);
        assert!(result.is_err());
    }

    #[test]
    fn test_typed_dictionary_try_from() {
        let mut untyped = Dictionary::new();
        untyped.set(Bool::new(true).into(), Int::new(1).into());
        untyped.set(Bool::new(false).into(), Int::new(0).into());

        let typed: Result<TypedDictionary<Bool, Int>, _> = untyped.try_into();
        assert!(typed.is_ok());

        let typed = typed.unwrap();
        assert_eq!(typed.size(), 2);
        assert_eq!(typed.get(&Bool::new(true)), Some(&Int::new(1)));
        assert_eq!(typed.get(&Bool::new(false)), Some(&Int::new(0)));
    }

    #[test]
    fn test_typed_dictionary_from_array() {
        let dict = TypedDictionary::from([
            (Int::new(1), Float::new(10.5)),
            (Int::new(2), Float::new(20.0)),
            (Int::new(3), Float::new(30.5))
        ]);

        assert_eq!(dict.size(), 3);
        assert_eq!(dict.get(&Int::new(1)), Some(&Float::new(10.5)));
        assert_eq!(dict.get(&Int::new(2)), Some(&Float::new(20.0)));
        assert_eq!(dict.get(&Int::new(3)), Some(&Float::new(30.5)));
    }

    #[test]
    fn test_typed_dictionary_from_iterator() {
        let dict: TypedDictionary<Int, Float> = (1..=5)
            .map(|i| (Int::new(i), Float::new(i as f64 * 10.0)))
            .collect();

        assert_eq!(dict.size(), 5);
        assert_eq!(dict.get(&Int::new(1)), Some(&Float::new(10.0)));
        assert_eq!(dict.get(&Int::new(5)), Some(&Float::new(50.0)));
    }

    #[test]
    fn test_typed_dictionary_memory_management() {
        let mut dict = TypedDictionary::<Int, Float>::new();

        // Test capacity management
        dict.reserve(100);

        dict.set(Int::new(1), Float::new(10.0));
        dict.set(Int::new(2), Float::new(20.0));

        dict.shrink_to_fit();
    }

    #[test]
    fn test_typed_dictionary_default() {
        let dict = TypedDictionary::<Int, Float>::default();
        assert!(dict.is_empty());
        assert_eq!(dict.size(), 0);
    }

    #[test]
    fn test_typed_dictionary_edge_cases() {
        let mut dict = TypedDictionary::<Int, Float>::new();

        // Test empty dictionary operations
        assert_eq!(dict.get(&Int::new(1)), None);
        assert!(!dict.has(&Int::new(1)));
        assert_eq!(dict.erase(&Int::new(1)), None);

        // Test single element
        dict.set(Int::new(42), Float::new(100.5));
        assert_eq!(dict.size(), 1);
        assert_eq!(dict.get(&Int::new(42)), Some(&Float::new(100.5)));

        // Test overwriting
        let old_value = dict.set(Int::new(42), Float::new(200.5));
        assert_eq!(old_value, Some(Float::new(100.5)));
        assert_eq!(dict.get(&Int::new(42)), Some(&Float::new(200.5)));
    }
}
