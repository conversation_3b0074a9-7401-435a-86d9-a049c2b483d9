/// ### Godot-compatible typed array implementation.
///
/// This module provides a type-safe array implementation following <PERSON><PERSON>'s
/// TypedArray patterns. It ensures compile-time type safety while maintaining
/// runtime compatibility with the existing Variant system.
///
/// ## Type Safety Features
///
/// - **Compile-Time Type Checking**: All operations are type-safe at compile time
/// - **Runtime Type Validation**: Safe conversion from untyped Array with validation
/// - **Generic Implementation**: Works with any type that can be stored in Variant
/// - **Performance Optimized**: Efficient operations with zero-cost abstractions
/// - **Godot Compatible**: API and behavior patterns matching Godot Engine
/// - **Collection Traits**: Full support for iteration, indexing, and standard traits
///
/// ## Examples
///
/// ```rust
/// use verturion::core::variant::{TypedArray, Variant};
/// use verturion::core::math::Vector2;
///
/// // Create typed arrays with compile-time type safety
/// let mut positions = TypedArray::<Vector2>::new();
/// positions.push(Vector2::new(1.0, 2.0));
/// positions.push(Vector2::new(3.0, 4.0));
///
/// // Type-safe operations
/// assert_eq!(positions.size(), 2);
/// assert_eq!(positions[0], Vector2::new(1.0, 2.0));
///
/// // Safe conversion from untyped Array
/// let untyped = Array::from([
///     Vector2::new(5.0, 6.0).into(),
///     Vector2::new(7.0, 8.0).into()
/// ]);
/// let typed = TypedArray::<Vector2>::try_from(untyped)?;
/// ```
use std::fmt;
use std::ops::{Index, IndexMut};
use std::slice::{Iter, IterMut};
use std::vec::IntoIter;
use super::{Array, Variant};

/// ### Type-safe array with compile-time type checking.
///
/// A generic array implementation that provides compile-time type safety
/// for all operations while maintaining compatibility with Godot's TypedArray
/// behavior and the existing Variant system.
///
/// ## Type Parameters
///
/// - `T`: The element type that must implement Clone and conversion to/from Variant
///
/// ## Type Safety
///
/// All operations are type-safe at compile time, preventing runtime type errors
/// that could occur with untyped Array operations. The TypedArray ensures that
/// only elements of type T can be stored and retrieved.
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::variant::TypedArray;
/// # use verturion::core::variant::Int;
/// // Create a typed array of integers
/// let mut numbers = TypedArray::<Int>::new();
/// numbers.push(Int::new(42));
/// numbers.push(Int::new(100));
///
/// // Type-safe access
/// assert_eq!(numbers[0], Int::new(42));
/// assert_eq!(numbers.size(), 2);
///
/// // Compile-time type checking prevents errors
/// // numbers.push("string"); // This would not compile!
/// ```
#[derive(Clone, Debug)]
pub struct TypedArray<T> {
    /// Internal storage using Vec for efficient operations
    elements: Vec<T>,
}

impl<T> TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Creates a new empty TypedArray.
    ///
    /// Returns a TypedArray with no elements and zero capacity.
    /// The array will allocate memory as elements are added.
    ///
    /// # Returns
    /// New empty TypedArray instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let array = TypedArray::<Int>::new();
    /// assert_eq!(array.size(), 0);
    /// assert!(array.is_empty());
    /// ```
    #[inline]
    pub fn new() -> Self {
        Self {
            elements: Vec::new(),
        }
    }

    /// ### Creates a new TypedArray with the specified capacity.
    ///
    /// Pre-allocates memory for the specified number of elements to avoid
    /// reallocations during initial population of the array.
    ///
    /// # Parameters
    /// - `capacity`: Number of elements to pre-allocate space for
    ///
    /// # Returns
    /// New empty TypedArray with pre-allocated capacity.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let array = TypedArray::<Int>::with_capacity(100);
    /// assert_eq!(array.size(), 0);
    /// assert!(array.capacity() >= 100);
    /// ```
    #[inline]
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            elements: Vec::with_capacity(capacity),
        }
    }

    /// ### Adds an element to the end of the array.
    ///
    /// Appends the element to the back of the array, increasing the size by 1.
    /// This operation is type-safe and will only accept elements of type T.
    ///
    /// # Parameters
    /// - `element`: Element to add to the array
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(42));
    /// array.push(Int::new(100));
    /// assert_eq!(array.size(), 2);
    /// ```
    #[inline]
    pub fn push(&mut self, element: T) {
        self.elements.push(element);
    }

    /// ### Removes and returns the last element from the array.
    ///
    /// Returns the last element if the array is not empty, otherwise returns None.
    /// This operation is type-safe and returns an element of type T.
    ///
    /// # Returns
    /// Some(T) if the array is not empty, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(42));
    /// array.push(Int::new(100));
    ///
    /// assert_eq!(array.pop(), Some(Int::new(100)));
    /// assert_eq!(array.pop(), Some(Int::new(42)));
    /// assert_eq!(array.pop(), None);
    /// ```
    #[inline]
    pub fn pop(&mut self) -> Option<T> {
        self.elements.pop()
    }

    /// ### Inserts an element at the specified index.
    ///
    /// Shifts all elements at and after the index to the right.
    /// Panics if the index is greater than the array length.
    ///
    /// # Parameters
    /// - `index`: Position to insert the element at
    /// - `element`: Element to insert
    ///
    /// # Panics
    /// Panics if index > len().
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(3));
    /// array.insert(1, Int::new(2));
    ///
    /// assert_eq!(array[0], Int::new(1));
    /// assert_eq!(array[1], Int::new(2));
    /// assert_eq!(array[2], Int::new(3));
    /// ```
    #[inline]
    pub fn insert(&mut self, index: usize, element: T) {
        self.elements.insert(index, element);
    }

    /// ### Removes and returns the element at the specified index.
    ///
    /// Shifts all elements after the index to the left.
    /// Panics if the index is out of bounds.
    ///
    /// # Parameters
    /// - `index`: Index of the element to remove
    ///
    /// # Returns
    /// The removed element of type T.
    ///
    /// # Panics
    /// Panics if index >= len().
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    /// array.push(Int::new(3));
    ///
    /// let removed = array.remove(1);
    /// assert_eq!(removed, Int::new(2));
    /// assert_eq!(array.size(), 2);
    /// ```
    #[inline]
    pub fn remove(&mut self, index: usize) -> T {
        self.elements.remove(index)
    }

    /// ### Removes all elements from the array.
    ///
    /// After calling this method, the array will be empty but will retain
    /// its allocated capacity for efficient reuse.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// array.clear();
    /// assert_eq!(array.size(), 0);
    /// assert!(array.is_empty());
    /// ```
    #[inline]
    pub fn clear(&mut self) {
        self.elements.clear();
    }

    /// ### Returns the number of elements in the array.
    ///
    /// This is the count of elements currently stored in the array,
    /// which may be less than the allocated capacity.
    ///
    /// # Returns
    /// Number of elements in the array.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// assert_eq!(array.size(), 0);
    ///
    /// array.push(Int::new(42));
    /// assert_eq!(array.size(), 1);
    /// ```
    #[inline]
    pub fn size(&self) -> usize {
        self.elements.len()
    }

    /// ### Checks if the array is empty.
    ///
    /// Returns true if the array contains no elements, false otherwise.
    /// This is equivalent to checking if size() == 0.
    ///
    /// # Returns
    /// True if the array is empty, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// assert!(array.is_empty());
    ///
    /// array.push(Int::new(42));
    /// assert!(!array.is_empty());
    /// ```
    #[inline]
    pub fn is_empty(&self) -> bool {
        self.elements.is_empty()
    }

    /// ### Returns the allocated capacity of the array.
    ///
    /// This is the number of elements the array can hold without reallocating.
    /// The capacity is always greater than or equal to the size.
    ///
    /// # Returns
    /// Allocated capacity of the array.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let array = TypedArray::<Int>::with_capacity(10);
    /// assert!(array.capacity() >= 10);
    /// ```
    #[inline]
    pub fn capacity(&self) -> usize {
        self.elements.capacity()
    }

    /// ### Reserves capacity for at least additional elements.
    ///
    /// Reserves capacity for at least `additional` more elements to be inserted.
    /// The collection may reserve more space to avoid frequent reallocations.
    ///
    /// # Parameters
    /// - `additional`: Number of additional elements to reserve space for
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.reserve(100);
    /// assert!(array.capacity() >= 100);
    /// ```
    #[inline]
    pub fn reserve(&mut self, additional: usize) {
        self.elements.reserve(additional);
    }

    /// ### Shrinks the capacity to fit the current size.
    ///
    /// Releases any excess capacity to minimize memory usage.
    /// After calling this method, capacity will be close to size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::with_capacity(100);
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// array.shrink_to_fit();
    /// assert!(array.capacity() >= 2);
    /// assert!(array.capacity() < 100);
    /// ```
    #[inline]
    pub fn shrink_to_fit(&mut self) {
        self.elements.shrink_to_fit();
    }

    /// ### Checks if the array contains the specified element.
    ///
    /// Returns true if the array contains an element equal to the given value.
    /// Uses the element's PartialEq implementation for comparison.
    ///
    /// # Parameters
    /// - `element`: Element to search for
    ///
    /// # Returns
    /// True if the element is found, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// assert!(array.contains(&Int::new(1)));
    /// assert!(!array.contains(&Int::new(3)));
    /// ```
    #[inline]
    pub fn contains(&self, element: &T) -> bool
    where
        T: PartialEq,
    {
        self.elements.contains(element)
    }

    /// ### Finds the index of the first occurrence of the element.
    ///
    /// Returns the index of the first element equal to the given value,
    /// or None if no such element is found.
    ///
    /// # Parameters
    /// - `element`: Element to search for
    ///
    /// # Returns
    /// Some(index) if found, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    /// array.push(Int::new(1));
    ///
    /// assert_eq!(array.find(&Int::new(1)), Some(0));
    /// assert_eq!(array.find(&Int::new(2)), Some(1));
    /// assert_eq!(array.find(&Int::new(3)), None);
    /// ```
    #[inline]
    pub fn find(&self, element: &T) -> Option<usize>
    where
        T: PartialEq,
    {
        self.elements.iter().position(|x| x == element)
    }

    /// ### Reverses the order of elements in the array.
    ///
    /// Reverses the array in-place, so the first element becomes the last
    /// and the last element becomes the first.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    /// array.push(Int::new(3));
    ///
    /// array.reverse();
    /// assert_eq!(array[0], Int::new(3));
    /// assert_eq!(array[1], Int::new(2));
    /// assert_eq!(array[2], Int::new(1));
    /// ```
    #[inline]
    pub fn reverse(&mut self) {
        self.elements.reverse();
    }

    /// ### Sorts the array in ascending order.
    ///
    /// Sorts the elements using their natural ordering (Ord trait).
    /// The sort is stable, preserving the relative order of equal elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(3));
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// array.sort();
    /// assert_eq!(array[0], Int::new(1));
    /// assert_eq!(array[1], Int::new(2));
    /// assert_eq!(array[2], Int::new(3));
    /// ```
    #[inline]
    pub fn sort(&mut self)
    where
        T: Ord,
    {
        self.elements.sort();
    }

    /// ### Sorts the array using a comparison function.
    ///
    /// Sorts the elements using the provided comparison function.
    /// The sort is stable, preserving the relative order of equal elements.
    ///
    /// # Parameters
    /// - `compare`: Function that compares two elements
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(3));
    /// array.push(Int::new(2));
    ///
    /// // Sort in descending order
    /// array.sort_by(|a, b| b.cmp(a));
    /// assert_eq!(array[0], Int::new(3));
    /// assert_eq!(array[1], Int::new(2));
    /// assert_eq!(array[2], Int::new(1));
    /// ```
    #[inline]
    pub fn sort_by<F>(&mut self, compare: F)
    where
        F: FnMut(&T, &T) -> std::cmp::Ordering,
    {
        self.elements.sort_by(compare);
    }

    /// ### Creates a duplicate of the array.
    ///
    /// Returns a new TypedArray containing clones of all elements.
    /// This is equivalent to calling clone() but follows Godot naming.
    ///
    /// # Returns
    /// New TypedArray with cloned elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// let duplicate = array.duplicate();
    /// assert_eq!(array.size(), duplicate.size());
    /// assert_eq!(array[0], duplicate[0]);
    /// ```
    #[inline]
    pub fn duplicate(&self) -> Self {
        self.clone()
    }

    /// ### Resizes the array to the specified size.
    ///
    /// If the new size is larger, fills with clones of the provided value.
    /// If the new size is smaller, truncates the array.
    ///
    /// # Parameters
    /// - `new_size`: Target size for the array
    /// - `fill_value`: Value to use for new elements if growing
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    ///
    /// array.resize(3, Int::new(0));
    /// assert_eq!(array.size(), 3);
    /// assert_eq!(array[1], Int::new(0));
    /// assert_eq!(array[2], Int::new(0));
    /// ```
    #[inline]
    pub fn resize(&mut self, new_size: usize, fill_value: T) {
        self.elements.resize(new_size, fill_value);
    }

    /// ### Returns an iterator over the elements.
    ///
    /// Returns an iterator that yields references to each element in order.
    /// The iterator implements the standard Iterator trait.
    ///
    /// # Returns
    /// Iterator over references to elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// for element in array.iter() {
    ///     println!("{}", element);
    /// }
    /// ```
    #[inline]
    pub fn iter(&self) -> Iter<T> {
        self.elements.iter()
    }

    /// ### Returns a mutable iterator over the elements.
    ///
    /// Returns an iterator that yields mutable references to each element.
    /// Allows modification of elements while iterating.
    ///
    /// # Returns
    /// Iterator over mutable references to elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// for element in array.iter_mut() {
    ///     *element = Int::new(element.value() * 2);
    /// }
    /// ```
    #[inline]
    pub fn iter_mut(&mut self) -> IterMut<T> {
        self.elements.iter_mut()
    }

    /// ### Converts the TypedArray to an untyped Array.
    ///
    /// Creates a new Array containing Variant representations of all elements.
    /// This allows interoperability with untyped collection APIs.
    ///
    /// # Returns
    /// New Array containing all elements as Variants.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut typed_array = TypedArray::<Int>::new();
    /// typed_array.push(Int::new(1));
    /// typed_array.push(Int::new(2));
    ///
    /// let untyped_array = typed_array.to_array();
    /// assert_eq!(untyped_array.size(), 2);
    /// ```
    #[inline]
    pub fn to_array(&self) -> Array {
        let variants: Vec<Variant> = self.elements.iter()
            .map(|element| element.clone().into())
            .collect();
        Array::from(variants)
    }

    /// ### Creates a TypedArray from an untyped Array.
    ///
    /// Attempts to convert all elements from the Array to type T.
    /// Returns an error if any element cannot be converted to type T.
    ///
    /// # Parameters
    /// - `array`: Untyped Array to convert
    ///
    /// # Returns
    /// Result containing TypedArray on success, error on failure.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Array, Int};
    /// let untyped = Array::from([
    ///     Int::new(1).into(),
    ///     Int::new(2).into()
    /// ]);
    ///
    /// let typed = TypedArray::<Int>::from_array(untyped)?;
    /// assert_eq!(typed.size(), 2);
    /// ```
    pub fn from_array(array: Array) -> Result<Self, &'static str> {
        let mut elements = Vec::with_capacity(array.size());

        for variant in array.iter() {
            match T::try_from(variant.clone()) {
                Ok(element) => elements.push(element),
                Err(_) => return Err("Type conversion failed"),
            }
        }

        Ok(Self { elements })
    }
}

// Trait implementations for TypedArray
impl<T> Default for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Creates the default TypedArray value.
    ///
    /// Returns an empty TypedArray with no elements.
    /// This is equivalent to calling TypedArray::new().
    ///
    /// # Returns
    /// Empty TypedArray instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let array = TypedArray::<Int>::default();
    /// assert!(array.is_empty());
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}

impl<T> PartialEq for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant> + PartialEq,
{
    /// ### Checks equality between two TypedArrays.
    ///
    /// Returns true if both arrays have the same size and all corresponding
    /// elements are equal according to their PartialEq implementation.
    ///
    /// # Parameters
    /// - `other`: Other TypedArray to compare with
    ///
    /// # Returns
    /// True if arrays are equal, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array1 = TypedArray::<Int>::new();
    /// let mut array2 = TypedArray::<Int>::new();
    ///
    /// array1.push(Int::new(1));
    /// array2.push(Int::new(1));
    ///
    /// assert_eq!(array1, array2);
    /// ```
    #[inline]
    fn eq(&self, other: &Self) -> bool {
        self.elements == other.elements
    }
}

impl<T> Index<usize> for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Output = T;

    /// ### Gets a reference to the element at the specified index.
    ///
    /// Returns a reference to the element at the given index.
    /// Panics if the index is out of bounds.
    ///
    /// # Parameters
    /// - `index`: Index of the element to access
    ///
    /// # Returns
    /// Reference to the element at the index.
    ///
    /// # Panics
    /// Panics if index >= len().
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(42));
    ///
    /// assert_eq!(array[0], Int::new(42));
    /// ```
    #[inline]
    fn index(&self, index: usize) -> &Self::Output {
        &self.elements[index]
    }
}

impl<T> IndexMut<usize> for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Gets a mutable reference to the element at the specified index.
    ///
    /// Returns a mutable reference to the element at the given index.
    /// Panics if the index is out of bounds.
    ///
    /// # Parameters
    /// - `index`: Index of the element to access
    ///
    /// # Returns
    /// Mutable reference to the element at the index.
    ///
    /// # Panics
    /// Panics if index >= len().
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(42));
    ///
    /// array[0] = Int::new(100);
    /// assert_eq!(array[0], Int::new(100));
    /// ```
    #[inline]
    fn index_mut(&mut self, index: usize) -> &mut Self::Output {
        &mut self.elements[index]
    }
}

impl<T> IntoIterator for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Item = T;
    type IntoIter = IntoIter<T>;

    /// ### Converts the TypedArray into an iterator.
    ///
    /// Consumes the TypedArray and returns an iterator that yields
    /// owned elements. This allows for efficient iteration without cloning.
    ///
    /// # Returns
    /// Iterator that yields owned elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// for element in array {
    ///     println!("{}", element);
    /// }
    /// ```
    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.elements.into_iter()
    }
}

impl<'a, T> IntoIterator for &'a TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Item = &'a T;
    type IntoIter = Iter<'a, T>;

    /// ### Converts a reference to TypedArray into an iterator.
    ///
    /// Returns an iterator that yields references to elements without
    /// consuming the TypedArray.
    ///
    /// # Returns
    /// Iterator that yields references to elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// for element in &array {
    ///     println!("{}", element);
    /// }
    /// ```
    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.elements.iter()
    }
}

impl<'a, T> IntoIterator for &'a mut TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Item = &'a mut T;
    type IntoIter = IterMut<'a, T>;

    /// ### Converts a mutable reference to TypedArray into an iterator.
    ///
    /// Returns an iterator that yields mutable references to elements
    /// without consuming the TypedArray.
    ///
    /// # Returns
    /// Iterator that yields mutable references to elements.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    ///
    /// for element in &mut array {
    ///     *element = Int::new(element.value() * 2);
    /// }
    /// ```
    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.elements.iter_mut()
    }
}

impl<T> fmt::Display for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant> + fmt::Display,
{
    /// ### Formats the TypedArray for display.
    ///
    /// Provides human-readable representation of the typed array
    /// showing all elements in a list format.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let mut array = TypedArray::<Int>::new();
    /// array.push(Int::new(1));
    /// array.push(Int::new(2));
    /// array.push(Int::new(3));
    ///
    /// println!("{}", array); // Shows: TypedArray[1, 2, 3]
    /// ```
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "TypedArray[")?;
        for (i, element) in self.elements.iter().enumerate() {
            if i > 0 {
                write!(f, ", ")?;
            }
            write!(f, "{}", element)?;
        }
        write!(f, "]")
    }
}

impl<T> TryFrom<Array> for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    type Error = &'static str;

    /// ### Attempts to convert an Array to TypedArray.
    ///
    /// Tries to convert all elements from the untyped Array to type T.
    /// Returns an error if any element cannot be converted.
    ///
    /// # Parameters
    /// - `array`: Untyped Array to convert
    ///
    /// # Returns
    /// Result containing TypedArray on success, error message on failure.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Array, Int};
    /// let untyped = Array::from([
    ///     Int::new(1).into(),
    ///     Int::new(2).into()
    /// ]);
    ///
    /// let typed: TypedArray<Int> = untyped.try_into()?;
    /// assert_eq!(typed.size(), 2);
    /// ```
    fn try_from(array: Array) -> Result<Self, Self::Error> {
        Self::from_array(array)
    }
}

impl<T> From<Vec<T>> for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Creates a TypedArray from a Vec.
    ///
    /// Converts a standard Vec into a TypedArray without copying elements.
    /// This provides an efficient way to create typed arrays from existing data.
    ///
    /// # Parameters
    /// - `vec`: Vec to convert to TypedArray
    ///
    /// # Returns
    /// New TypedArray containing all elements from the Vec.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let vec = vec![Int::new(1), Int::new(2), Int::new(3)];
    /// let array = TypedArray::from(vec);
    /// assert_eq!(array.size(), 3);
    /// ```
    #[inline]
    fn from(vec: Vec<T>) -> Self {
        Self { elements: vec }
    }
}

impl<T, const N: usize> From<[T; N]> for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Creates a TypedArray from an array.
    ///
    /// Converts a fixed-size array into a TypedArray.
    /// This provides a convenient way to create typed arrays from literals.
    ///
    /// # Parameters
    /// - `array`: Fixed-size array to convert
    ///
    /// # Returns
    /// New TypedArray containing all elements from the array.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let array = TypedArray::from([Int::new(1), Int::new(2), Int::new(3)]);
    /// assert_eq!(array.size(), 3);
    /// ```
    #[inline]
    fn from(array: [T; N]) -> Self {
        Self {
            elements: Vec::from(array),
        }
    }
}

impl<T> FromIterator<T> for TypedArray<T>
where
    T: Clone + Into<Variant> + TryFrom<Variant>,
{
    /// ### Creates a TypedArray from an iterator.
    ///
    /// Collects all elements from an iterator into a new TypedArray.
    /// This enables using collect() with iterators to create typed arrays.
    ///
    /// # Parameters
    /// - `iter`: Iterator yielding elements of type T
    ///
    /// # Returns
    /// New TypedArray containing all elements from the iterator.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{TypedArray, Int};
    /// let array: TypedArray<Int> = (1..=5)
    ///     .map(Int::new)
    ///     .collect();
    /// assert_eq!(array.size(), 5);
    /// ```
    #[inline]
    fn from_iter<I: IntoIterator<Item = T>>(iter: I) -> Self {
        Self {
            elements: Vec::from_iter(iter),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::variant::{Int, Float, Bool};

    #[test]
    fn test_typed_array_creation() {
        let array = TypedArray::<Int>::new();
        assert_eq!(array.size(), 0);
        assert!(array.is_empty());

        let array_with_capacity = TypedArray::<Int>::with_capacity(10);
        assert_eq!(array_with_capacity.size(), 0);
        assert!(array_with_capacity.capacity() >= 10);
    }

    #[test]
    fn test_typed_array_push_pop() {
        let mut array = TypedArray::<Int>::new();

        array.push(Int::new(1));
        array.push(Int::new(2));
        array.push(Int::new(3));

        assert_eq!(array.size(), 3);
        assert!(!array.is_empty());

        assert_eq!(array.pop(), Some(Int::new(3)));
        assert_eq!(array.pop(), Some(Int::new(2)));
        assert_eq!(array.pop(), Some(Int::new(1)));
        assert_eq!(array.pop(), None);

        assert!(array.is_empty());
    }

    #[test]
    fn test_typed_array_insert_remove() {
        let mut array = TypedArray::<Int>::new();
        array.push(Int::new(1));
        array.push(Int::new(3));

        array.insert(1, Int::new(2));
        assert_eq!(array.size(), 3);
        assert_eq!(array[0], Int::new(1));
        assert_eq!(array[1], Int::new(2));
        assert_eq!(array[2], Int::new(3));

        let removed = array.remove(1);
        assert_eq!(removed, Int::new(2));
        assert_eq!(array.size(), 2);
        assert_eq!(array[0], Int::new(1));
        assert_eq!(array[1], Int::new(3));
    }

    #[test]
    fn test_typed_array_indexing() {
        let mut array = TypedArray::<Int>::new();
        array.push(Int::new(42));
        array.push(Int::new(100));

        assert_eq!(array[0], Int::new(42));
        assert_eq!(array[1], Int::new(100));

        array[0] = Int::new(99);
        assert_eq!(array[0], Int::new(99));
    }

    #[test]
    fn test_typed_array_clear_resize() {
        let mut array = TypedArray::<Int>::new();
        array.push(Int::new(1));
        array.push(Int::new(2));

        array.clear();
        assert_eq!(array.size(), 0);
        assert!(array.is_empty());

        array.resize(3, Int::new(42));
        assert_eq!(array.size(), 3);
        assert_eq!(array[0], Int::new(42));
        assert_eq!(array[1], Int::new(42));
        assert_eq!(array[2], Int::new(42));
    }

    #[test]
    fn test_typed_array_contains_find() {
        let mut array = TypedArray::<Int>::new();
        array.push(Int::new(1));
        array.push(Int::new(2));
        array.push(Int::new(3));
        array.push(Int::new(2));

        assert!(array.contains(&Int::new(2)));
        assert!(!array.contains(&Int::new(5)));

        assert_eq!(array.find(&Int::new(2)), Some(1));
        assert_eq!(array.find(&Int::new(3)), Some(2));
        assert_eq!(array.find(&Int::new(5)), None);
    }

    #[test]
    fn test_typed_array_reverse_sort() {
        let mut array = TypedArray::<Int>::new();
        array.push(Int::new(3));
        array.push(Int::new(1));
        array.push(Int::new(2));

        array.reverse();
        assert_eq!(array[0], Int::new(2));
        assert_eq!(array[1], Int::new(1));
        assert_eq!(array[2], Int::new(3));

        array.sort();
        assert_eq!(array[0], Int::new(1));
        assert_eq!(array[1], Int::new(2));
        assert_eq!(array[2], Int::new(3));

        array.sort_by(|a, b| b.cmp(a));
        assert_eq!(array[0], Int::new(3));
        assert_eq!(array[1], Int::new(2));
        assert_eq!(array[2], Int::new(1));
    }

    #[test]
    fn test_typed_array_duplicate() {
        let mut array = TypedArray::<Int>::new();
        array.push(Int::new(1));
        array.push(Int::new(2));

        let duplicate = array.duplicate();
        assert_eq!(array.size(), duplicate.size());
        assert_eq!(array[0], duplicate[0]);
        assert_eq!(array[1], duplicate[1]);

        // Ensure they are independent
        array.push(Int::new(3));
        assert_ne!(array.size(), duplicate.size());
    }

    #[test]
    fn test_typed_array_iteration() {
        let mut array = TypedArray::<Int>::new();
        array.push(Int::new(1));
        array.push(Int::new(2));
        array.push(Int::new(3));

        // Test immutable iteration
        let mut sum = 0;
        for element in &array {
            sum += element.get();
        }
        assert_eq!(sum, 6);

        // Test mutable iteration
        for element in &mut array {
            *element = Int::new(element.get() * 2);
        }
        assert_eq!(array[0], Int::new(2));
        assert_eq!(array[1], Int::new(4));
        assert_eq!(array[2], Int::new(6));

        // Test consuming iteration
        let values: Vec<i64> = array.into_iter().map(|x| x.get()).collect();
        assert_eq!(values, vec![2, 4, 6]);
    }

    #[test]
    fn test_typed_array_equality() {
        let mut array1 = TypedArray::<Int>::new();
        let mut array2 = TypedArray::<Int>::new();

        array1.push(Int::new(1));
        array1.push(Int::new(2));

        array2.push(Int::new(1));
        array2.push(Int::new(2));

        assert_eq!(array1, array2);

        array2.push(Int::new(3));
        assert_ne!(array1, array2);
    }

    #[test]
    fn test_typed_array_display() {
        let mut array = TypedArray::<Int>::new();
        array.push(Int::new(1));
        array.push(Int::new(2));
        array.push(Int::new(3));

        let display = format!("{}", array);
        assert!(display.contains("TypedArray"));
        assert!(display.contains("1"));
        assert!(display.contains("2"));
        assert!(display.contains("3"));
    }

    #[test]
    fn test_typed_array_conversion_to_array() {
        let mut typed_array = TypedArray::<Int>::new();
        typed_array.push(Int::new(1));
        typed_array.push(Int::new(2));
        typed_array.push(Int::new(3));

        let untyped_array = typed_array.to_array();
        assert_eq!(untyped_array.size(), 3);

        // Verify elements can be extracted as the correct type
        if let Some(first) = untyped_array.get(0).and_then(|v| v.as_int_wrapper()) {
            assert_eq!(first.get(), 1);
        } else {
            panic!("Failed to extract first element");
        }
    }

    #[test]
    fn test_typed_array_conversion_from_array() {
        let untyped = Array::from(vec![
            Int::new(1).into(),
            Int::new(2).into(),
            Int::new(3).into()
        ]);

        let typed = TypedArray::<Int>::from_array(untyped).unwrap();
        assert_eq!(typed.size(), 3);
        assert_eq!(typed[0], Int::new(1));
        assert_eq!(typed[1], Int::new(2));
        assert_eq!(typed[2], Int::new(3));
    }

    #[test]
    fn test_typed_array_conversion_from_array_failure() {
        let mixed_untyped = Array::from(vec![
            Int::new(1).into(),
            Float::new(2.5).into(),  // Wrong type
            Int::new(3).into()
        ]);

        let result = TypedArray::<Int>::from_array(mixed_untyped);
        assert!(result.is_err());
    }

    #[test]
    fn test_typed_array_try_from() {
        let untyped = Array::from(vec![
            Bool::new(true).into(),
            Bool::new(false).into(),
            Bool::new(true).into()
        ]);

        let typed: Result<TypedArray<Bool>, _> = untyped.try_into();
        assert!(typed.is_ok());

        let typed = typed.unwrap();
        assert_eq!(typed.size(), 3);
        assert_eq!(typed[0], Bool::new(true));
        assert_eq!(typed[1], Bool::new(false));
        assert_eq!(typed[2], Bool::new(true));
    }

    #[test]
    fn test_typed_array_from_vec() {
        let vec = vec![Int::new(1), Int::new(2), Int::new(3)];
        let array = TypedArray::from(vec);

        assert_eq!(array.size(), 3);
        assert_eq!(array[0], Int::new(1));
        assert_eq!(array[1], Int::new(2));
        assert_eq!(array[2], Int::new(3));
    }

    #[test]
    fn test_typed_array_from_fixed_array() {
        let array = TypedArray::from([Int::new(1), Int::new(2), Int::new(3)]);

        assert_eq!(array.size(), 3);
        assert_eq!(array[0], Int::new(1));
        assert_eq!(array[1], Int::new(2));
        assert_eq!(array[2], Int::new(3));
    }

    #[test]
    fn test_typed_array_from_iterator() {
        let array: TypedArray<Int> = (1..=5)
            .map(Int::new)
            .collect();

        assert_eq!(array.size(), 5);
        assert_eq!(array[0], Int::new(1));
        assert_eq!(array[4], Int::new(5));
    }

    #[test]
    fn test_typed_array_memory_management() {
        let mut array = TypedArray::<Int>::new();

        // Test capacity management
        array.reserve(100);
        assert!(array.capacity() >= 100);

        array.push(Int::new(1));
        array.push(Int::new(2));

        array.shrink_to_fit();
        assert!(array.capacity() >= 2);
        assert!(array.capacity() < 100);
    }

    #[test]
    fn test_typed_array_default() {
        let array = TypedArray::<Int>::default();
        assert!(array.is_empty());
        assert_eq!(array.size(), 0);
    }

    #[test]
    fn test_typed_array_edge_cases() {
        let mut array = TypedArray::<Int>::new();

        // Test empty array operations
        assert_eq!(array.pop(), None);
        assert!(!array.contains(&Int::new(1)));
        assert_eq!(array.find(&Int::new(1)), None);

        // Test single element
        array.push(Int::new(42));
        assert_eq!(array.size(), 1);
        assert_eq!(array[0], Int::new(42));

        array.reverse();
        assert_eq!(array[0], Int::new(42));

        array.sort();
        assert_eq!(array[0], Int::new(42));
    }
}
