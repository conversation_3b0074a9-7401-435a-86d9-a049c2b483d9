//! Comprehensive Color implementation with RGBA components and color space conversions.
//!
//! This module provides a complete Color implementation with RGBA components stored as
//! floating-point values in the range 0.0-1.0. It includes color space conversions,
//! color operations, blending modes, and comprehensive utility functions for
//! graphics programming, UI development, and color manipulation.

use std::fmt;

/// ### A color represented by RGBA components using floating-point values.
///
/// This struct represents a color in RGBA format with floating-point red, green, blue, and alpha components.
/// Each component is typically in the range 0.0-1.0, where 0.0 represents no intensity and 1.0 represents
/// full intensity. The alpha component controls transparency, where 0.0 is fully transparent and 1.0 is fully opaque.
/// 
/// ## Use Cases
///
/// Color is ideal for:
/// - **Graphics Programming**: Pixel colors, texture sampling, shader uniforms
/// - **UI Development**: Widget colors, themes, visual feedback
/// - **Game Development**: Sprite tinting, lighting calculations, particle effects
/// - **Image Processing**: Color manipulation, filtering, color space conversions
/// - **Data Visualization**: Color mapping, gradients, heat maps
#[derive(Debu<PERSON>, <PERSON>lone, Copy, PartialEq)]
pub struct Color {
    /// The color's red component (0.0-1.0).
    pub r: f32,
    /// The color's green component (0.0-1.0).
    pub g: f32,
    /// The color's blue component (0.0-1.0).
    pub b: f32,
    /// The color's alpha component (0.0-1.0, where 0.0 is transparent and 1.0 is opaque).
    pub a: f32,
}

impl Color {
    /// ### Transparent color constant -> Color(0.0, 0.0, 0.0, 0.0).
    ///
    /// Represents a fully transparent color with no visible components.
    /// Useful as a default for transparent backgrounds and invisible elements.
    pub const TRANSPARENT: Color = Color { r: 0.0, g: 0.0, b: 0.0, a: 0.0 };

    /// ### White color constant -> Color(1.0, 1.0, 1.0, 1.0).
    ///
    /// Pure white color with full opacity. Commonly used as a neutral base color,
    /// default text color, and for additive blending operations.
    pub const WHITE: Color = Color { r: 1.0, g: 1.0, b: 1.0, a: 1.0 };

    /// ### Black color constant -> Color(0.0, 0.0, 0.0, 1.0).
    ///
    /// Pure black color with full opacity. Commonly used for backgrounds,
    /// text on light backgrounds, and subtractive blending operations.
    pub const BLACK: Color = Color { r: 0.0, g: 0.0, b: 0.0, a: 1.0 };

    /// ### Red color constant -> Color(1.0, 0.0, 0.0, 1.0).
    ///
    /// Pure red color with full opacity. Primary color used for error states,
    /// warnings, and red channel operations.
    pub const RED: Color = Color { r: 1.0, g: 0.0, b: 0.0, a: 1.0 };

    /// ### Green color constant -> Color(0.0, 1.0, 0.0, 1.0).
    ///
    /// Pure green color with full opacity. Primary color used for success states,
    /// nature elements, and green channel operations.
    pub const GREEN: Color = Color { r: 0.0, g: 1.0, b: 0.0, a: 1.0 };

    /// ### Blue color constant -> Color(0.0, 0.0, 1.0, 1.0).
    ///
    /// Pure blue color with full opacity. Primary color used for information states,
    /// water elements, and blue channel operations.
    pub const BLUE: Color = Color { r: 0.0, g: 0.0, b: 1.0, a: 1.0 };

    /// ### Yellow color constant -> Color(1.0, 1.0, 0.0, 1.0).
    ///
    /// Pure yellow color with full opacity. Secondary color created by combining
    /// red and green, commonly used for warnings and highlights.
    pub const YELLOW: Color = Color { r: 1.0, g: 1.0, b: 0.0, a: 1.0 };

    /// ### Cyan color constant -> Color(0.0, 1.0, 1.0, 1.0).
    ///
    /// Pure cyan color with full opacity. Secondary color created by combining
    /// green and blue, commonly used for information and cool tones.
    pub const CYAN: Color = Color { r: 0.0, g: 1.0, b: 1.0, a: 1.0 };

    /// ### Magenta color constant -> Color(1.0, 0.0, 1.0, 1.0).
    ///
    /// Pure magenta color with full opacity. Secondary color created by combining
    /// red and blue, commonly used for highlights and vibrant accents.
    pub const MAGENTA: Color = Color { r: 1.0, g: 0.0, b: 1.0, a: 1.0 };

    /// ### Creates a new Color with the specified RGBA components.
    ///
    /// This is the primary constructor for creating colors with explicit RGBA values.
    /// All components should typically be in the range 0.0-1.0, though values outside
    /// this range are allowed for HDR colors and special effects.
    ///
    /// # Arguments
    /// * `r` - The red component (typically 0.0-1.0)
    /// * `g` - The green component (typically 0.0-1.0)
    /// * `b` - The blue component (typically 0.0-1.0)
    /// * `a` - The alpha component (typically 0.0-1.0, where 0.0 is transparent)
    ///
    /// # Returns
    /// A new Color instance with the specified components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::new(1.0, 0.0, 0.0, 1.0);
    /// let semi_transparent_blue = Color::new(0.0, 0.0, 1.0, 0.5);
    /// let hdr_white = Color::new(2.0, 2.0, 2.0, 1.0); // HDR color
    /// ```
    #[inline]
    pub const fn new(r: f32, g: f32, b: f32, a: f32) -> Self {
        Self { r, g, b, a }
    }

    /// ### Creates a new Color from RGB components with full opacity.
    ///
    /// Convenience constructor for creating opaque colors when alpha is not needed.
    /// The alpha component is automatically set to 1.0 (fully opaque).
    ///
    /// # Arguments
    /// * `r` - The red component (typically 0.0-1.0)
    /// * `g` - The green component (typically 0.0-1.0)
    /// * `b` - The blue component (typically 0.0-1.0)
    ///
    /// # Returns
    /// A new Color instance with the specified RGB components and alpha = 1.0.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let orange = Color::from_rgb(1.0, 0.5, 0.0);
    /// assert_eq!(orange.a, 1.0); // Alpha is automatically set to 1.0
    /// ```
    #[inline]
    pub const fn from_rgb(r: f32, g: f32, b: f32) -> Self {
        Self::new(r, g, b, 1.0)
    }

    /// ### Creates a new Color from 8-bit RGBA integer components.
    ///
    /// Converts 8-bit integer values (0-255) to floating-point components (0.0-1.0).
    /// This is useful for working with traditional RGB color values and web colors.
    ///
    /// # Arguments
    /// * `r8` - The red component (0-255)
    /// * `g8` - The green component (0-255)
    /// * `b8` - The blue component (0-255)
    /// * `a8` - The alpha component (0-255, where 0 is transparent)
    ///
    /// # Returns
    /// A new Color instance with components converted to 0.0-1.0 range.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::from_rgba8(255, 0, 0, 255);
    /// let semi_transparent = Color::from_rgba8(128, 128, 128, 128);
    /// assert_eq!(red, Color::new(1.0, 0.0, 0.0, 1.0));
    /// ```
    #[inline]
    pub fn from_rgba8(r8: u8, g8: u8, b8: u8, a8: u8) -> Self {
        Self::new(
            r8 as f32 / 255.0,
            g8 as f32 / 255.0,
            b8 as f32 / 255.0,
            a8 as f32 / 255.0,
        )
    }

    /// ### Creates a new Color from HSV (Hue, Saturation, Value) components.
    ///
    /// Converts HSV color space to RGB color space. HSV is often more intuitive
    /// for color selection and manipulation than RGB.
    ///
    /// # Arguments
    /// * `h` - The hue component (0.0-1.0, where 0.0 and 1.0 are red)
    /// * `s` - The saturation component (0.0-1.0, where 0.0 is grayscale)
    /// * `v` - The value/brightness component (0.0-1.0, where 0.0 is black)
    /// * `a` - The alpha component (0.0-1.0, where 0.0 is transparent)
    ///
    /// # Returns
    /// A new Color instance converted from HSV to RGB color space.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::from_hsv(0.0, 1.0, 1.0, 1.0);
    /// let yellow = Color::from_hsv(1.0/6.0, 1.0, 1.0, 1.0);
    /// let gray = Color::from_hsv(0.0, 0.0, 0.5, 1.0);
    /// ```
    #[inline]
    pub fn from_hsv(h: f32, s: f32, v: f32, a: f32) -> Self {
        let c = v * s;
        let h_prime = (h * 6.0) % 6.0;
        let x = c * (1.0 - ((h_prime % 2.0) - 1.0).abs());
        let m = v - c;

        let (r, g, b) = if h_prime < 1.0 {
            (c, x, 0.0)
        } else if h_prime < 2.0 {
            (x, c, 0.0)
        } else if h_prime < 3.0 {
            (0.0, c, x)
        } else if h_prime < 4.0 {
            (0.0, x, c)
        } else if h_prime < 5.0 {
            (x, 0.0, c)
        } else {
            (c, 0.0, x)
        };

        Self::new(r + m, g + m, b + m, a)
    }

    /// ### Creates a new Color from an HTML color string.
    ///
    /// Parses HTML/CSS color strings including hex codes and named colors.
    /// Supports 3, 4, 6, and 8 digit hex codes with optional '#' prefix.
    ///
    /// # Arguments
    /// * `html` - The HTML color string (e.g., "#FF0000", "red", "rgb(255,0,0)")
    ///
    /// # Returns
    /// A new Color instance parsed from the HTML string, or WHITE if parsing fails.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::from_html("#FF0000");
    /// let blue = Color::from_html("#00F");
    /// let transparent_green = Color::from_html("#00FF0080");
    /// ```
    #[inline]
    pub fn from_html(html: &str) -> Self {
        let html = html.trim().trim_start_matches('#');

        match html.len() {
            3 => {
                // RGB format: "F0A" -> "FF00AA"
                if let (Ok(r), Ok(g), Ok(b)) = (
                    u8::from_str_radix(&html[0..1].repeat(2), 16),
                    u8::from_str_radix(&html[1..2].repeat(2), 16),
                    u8::from_str_radix(&html[2..3].repeat(2), 16),
                ) {
                    Self::from_rgba8(r, g, b, 255)
                } else {
                    Self::WHITE
                }
            }
            4 => {
                // RGBA format: "F0A8" -> "FF00AA88"
                if let (Ok(r), Ok(g), Ok(b), Ok(a)) = (
                    u8::from_str_radix(&html[0..1].repeat(2), 16),
                    u8::from_str_radix(&html[1..2].repeat(2), 16),
                    u8::from_str_radix(&html[2..3].repeat(2), 16),
                    u8::from_str_radix(&html[3..4].repeat(2), 16),
                ) {
                    Self::from_rgba8(r, g, b, a)
                } else {
                    Self::WHITE
                }
            }
            6 => {
                // RGB format: "FF00AA"
                if let (Ok(r), Ok(g), Ok(b)) = (
                    u8::from_str_radix(&html[0..2], 16),
                    u8::from_str_radix(&html[2..4], 16),
                    u8::from_str_radix(&html[4..6], 16),
                ) {
                    Self::from_rgba8(r, g, b, 255)
                } else {
                    Self::WHITE
                }
            }
            8 => {
                // RGBA format: "FF00AA88"
                if let (Ok(r), Ok(g), Ok(b), Ok(a)) = (
                    u8::from_str_radix(&html[0..2], 16),
                    u8::from_str_radix(&html[2..4], 16),
                    u8::from_str_radix(&html[4..6], 16),
                    u8::from_str_radix(&html[6..8], 16),
                ) {
                    Self::from_rgba8(r, g, b, a)
                } else {
                    Self::WHITE
                }
            }
            _ => Self::WHITE,
        }
    }

    /// ### Converts the color to HSV (Hue, Saturation, Value) color space.
    ///
    /// Returns a tuple containing (hue, saturation, value, alpha) components.
    /// HSV color space is often more intuitive for color manipulation.
    ///
    /// # Returns
    /// A tuple (h, s, v, a) where:
    /// - h: Hue (0.0-1.0, where 0.0 and 1.0 are red)
    /// - s: Saturation (0.0-1.0, where 0.0 is grayscale)
    /// - v: Value/brightness (0.0-1.0, where 0.0 is black)
    /// - a: Alpha (0.0-1.0, unchanged from original)
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::RED;
    /// let (h, s, v, a) = red.to_hsv();
    /// assert!((h - 0.0).abs() < 0.001);
    /// assert!((s - 1.0).abs() < 0.001);
    /// assert!((v - 1.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn to_hsv(self) -> (f32, f32, f32, f32) {
        let max = self.r.max(self.g).max(self.b);
        let min = self.r.min(self.g).min(self.b);
        let delta = max - min;

        let v = max;
        let s = if max == 0.0 { 0.0 } else { delta / max };

        let h = if delta == 0.0 {
            0.0
        } else if max == self.r {
            ((self.g - self.b) / delta) % 6.0
        } else if max == self.g {
            (self.b - self.r) / delta + 2.0
        } else {
            (self.r - self.g) / delta + 4.0
        };

        let h = (h / 6.0 + 1.0) % 1.0; // Normalize to 0.0-1.0

        (h, s, v, self.a)
    }

    /// ### Converts the color to an HTML hexadecimal string.
    ///
    /// Returns a hexadecimal color string in RRGGBB or RRGGBBAA format.
    /// The string does not include the '#' prefix for compatibility.
    ///
    /// # Arguments
    /// * `with_alpha` - Whether to include the alpha component in the output
    ///
    /// # Returns
    /// A hexadecimal string representing the color.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::RED;
    /// assert_eq!(red.to_html(false), "ff0000");
    /// assert_eq!(red.to_html(true), "ff0000ff");
    ///
    /// let semi_transparent = Color::new(1.0, 0.0, 0.0, 0.5);
    /// assert_eq!(semi_transparent.to_html(true), "ff000080");
    /// ```
    #[inline]
    pub fn to_html(self, with_alpha: bool) -> String {
        let r = (self.r.clamp(0.0, 1.0) * 255.0).round() as u8;
        let g = (self.g.clamp(0.0, 1.0) * 255.0).round() as u8;
        let b = (self.b.clamp(0.0, 1.0) * 255.0).round() as u8;

        if with_alpha {
            let a = (self.a.clamp(0.0, 1.0) * 255.0).round() as u8;
            format!("{:02x}{:02x}{:02x}{:02x}", r, g, b, a)
        } else {
            format!("{:02x}{:02x}{:02x}", r, g, b)
        }
    }

    /// ### Converts the color to a 32-bit RGBA integer.
    ///
    /// Packs the RGBA components into a single 32-bit integer in RGBA format.
    /// Each component is converted from 0.0-1.0 range to 0-255 range.
    ///
    /// # Returns
    /// A 32-bit integer representing the color in RGBA format.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::RED;
    /// let rgba32 = red.to_rgba32();
    /// assert_eq!(rgba32, 0xFF0000FF);
    /// ```
    #[inline]
    pub fn to_rgba32(self) -> u32 {
        let r = (self.r.clamp(0.0, 1.0) * 255.0).round() as u32;
        let g = (self.g.clamp(0.0, 1.0) * 255.0).round() as u32;
        let b = (self.b.clamp(0.0, 1.0) * 255.0).round() as u32;
        let a = (self.a.clamp(0.0, 1.0) * 255.0).round() as u32;

        (r << 24) | (g << 16) | (b << 8) | a
    }

    /// ### Linearly interpolates between this color and another color.
    ///
    /// Performs component-wise linear interpolation between two colors.
    /// The interpolation factor determines the blend ratio between the colors.
    ///
    /// # Arguments
    /// * `to` - The target color to interpolate towards
    /// * `weight` - The interpolation factor (0.0-1.0, where 0.0 returns self and 1.0 returns to)
    ///
    /// # Returns
    /// A new Color representing the interpolated result.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::RED;
    /// let blue = Color::BLUE;
    /// let purple = red.lerp(blue, 0.5);
    /// assert_eq!(purple, Color::new(0.5, 0.0, 0.5, 1.0));
    /// ```
    #[inline]
    pub fn lerp(self, to: Color, weight: f32) -> Color {
        Color::new(
            self.r + (to.r - self.r) * weight,
            self.g + (to.g - self.g) * weight,
            self.b + (to.b - self.b) * weight,
            self.a + (to.a - self.a) * weight,
        )
    }

    /// ### Returns a darkened version of the color.
    ///
    /// Reduces the brightness of the color by the specified amount.
    /// This is achieved by multiplying the RGB components by (1.0 - amount).
    ///
    /// # Arguments
    /// * `amount` - The darkening factor (0.0-1.0, where 0.0 returns the original color)
    ///
    /// # Returns
    /// A new Color that is darker than the original.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::RED;
    /// let dark_red = red.darkened(0.3);
    /// assert_eq!(dark_red, Color::new(0.7, 0.0, 0.0, 1.0));
    /// ```
    #[inline]
    pub fn darkened(self, amount: f32) -> Color {
        let factor = 1.0 - amount.clamp(0.0, 1.0);
        Color::new(self.r * factor, self.g * factor, self.b * factor, self.a)
    }

    /// ### Returns a lightened version of the color.
    ///
    /// Increases the brightness of the color by the specified amount.
    /// This is achieved by interpolating towards white.
    ///
    /// # Arguments
    /// * `amount` - The lightening factor (0.0-1.0, where 0.0 returns the original color)
    ///
    /// # Returns
    /// A new Color that is lighter than the original.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::RED;
    /// let light_red = red.lightened(0.3);
    /// assert_eq!(light_red, Color::new(1.0, 0.3, 0.3, 1.0));
    /// ```
    #[inline]
    pub fn lightened(self, amount: f32) -> Color {
        let factor = amount.clamp(0.0, 1.0);
        Color::new(
            self.r + (1.0 - self.r) * factor,
            self.g + (1.0 - self.g) * factor,
            self.b + (1.0 - self.b) * factor,
            self.a,
        )
    }

    /// ### Returns the inverted color.
    ///
    /// Inverts the RGB components by subtracting them from 1.0.
    /// The alpha component remains unchanged.
    ///
    /// # Returns
    /// A new Color with inverted RGB components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let red = Color::RED;
    /// let cyan = red.inverted();
    /// assert_eq!(cyan, Color::new(0.0, 1.0, 1.0, 1.0));
    /// ```
    #[inline]
    pub fn inverted(self) -> Color {
        Color::new(1.0 - self.r, 1.0 - self.g, 1.0 - self.b, self.a)
    }

    /// ### Blends this color over another color using alpha blending.
    ///
    /// Performs standard alpha blending where this color is composited over the background color.
    /// The result represents what you would see if this color were painted over the background.
    ///
    /// # Arguments
    /// * `over` - The background color to blend over
    ///
    /// # Returns
    /// A new Color representing the blended result.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let background = Color::WHITE;
    /// let foreground = Color::new(1.0, 0.0, 0.0, 0.5); // Semi-transparent red
    /// let blended = foreground.blend(background);
    /// assert_eq!(blended, Color::new(1.0, 0.5, 0.5, 1.0)); // Pink
    /// ```
    #[inline]
    pub fn blend(self, over: Color) -> Color {
        let inv_alpha = 1.0 - self.a;
        Color::new(
            self.r * self.a + over.r * inv_alpha,
            self.g * self.a + over.g * inv_alpha,
            self.b * self.a + over.b * inv_alpha,
            self.a + over.a * inv_alpha,
        )
    }

    /// ### Clamps all color components to the specified range.
    ///
    /// Ensures all RGBA components are within the specified minimum and maximum bounds.
    /// This is useful for keeping colors within valid ranges or creating specific constraints.
    ///
    /// # Arguments
    /// * `min` - The minimum color values for each component
    /// * `max` - The maximum color values for each component
    ///
    /// # Returns
    /// A new Color with all components clamped to the specified range.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let hdr_color = Color::new(2.0, -0.5, 1.5, 0.8);
    /// let clamped = hdr_color.clamp(Color::BLACK, Color::WHITE);
    /// assert_eq!(clamped, Color::new(1.0, 0.0, 1.0, 0.8));
    /// ```
    #[inline]
    pub fn clamp(self, min: Color, max: Color) -> Color {
        Color::new(
            self.r.clamp(min.r, max.r),
            self.g.clamp(min.g, max.g),
            self.b.clamp(min.b, max.b),
            self.a.clamp(min.a, max.a),
        )
    }

    /// ### Checks if this color is approximately equal to another color.
    ///
    /// Uses epsilon-based comparison for floating-point components to handle
    /// precision errors that can occur in color calculations.
    ///
    /// # Arguments
    /// * `other` - The color to compare against
    ///
    /// # Returns
    /// True if all components are approximately equal within epsilon tolerance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let color1 = Color::new(1.0, 0.5, 0.0, 1.0);
    /// let color2 = Color::new(1.0000001, 0.4999999, 0.0000001, 1.0);
    /// assert!(color1.is_equal_approx(color2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Color) -> bool {
        const EPSILON: f32 = 1e-5;
        (self.r - other.r).abs() < EPSILON
            && (self.g - other.g).abs() < EPSILON
            && (self.b - other.b).abs() < EPSILON
            && (self.a - other.a).abs() < EPSILON
    }

    /// ### Calculates the luminance of the color.
    ///
    /// Returns the perceived brightness of the color using the standard
    /// luminance formula. Useful for determining if a color is light or dark.
    ///
    /// # Returns
    /// The luminance value (0.0-1.0, where 0.0 is black and 1.0 is white).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// let white = Color::WHITE;
    /// let black = Color::BLACK;
    /// assert!((white.get_luminance() - 1.0).abs() < 0.001);
    /// assert!((black.get_luminance() - 0.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn get_luminance(self) -> f32 {
        0.299 * self.r + 0.587 * self.g + 0.114 * self.b
    }

    /// ### Converts the color to a Vector3 containing RGB components.
    ///
    /// Creates a 3D vector with the red, green, and blue components.
    /// The alpha component is discarded in this conversion.
    ///
    /// # Returns
    /// A Vector3 with (r, g, b) components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// # use verturion::core::math::Vector3;
    /// let color = Color::new(1.0, 0.5, 0.2, 0.8);
    /// let rgb_vector = color.to_vector3();
    /// assert_eq!(rgb_vector, Vector3::new(1.0, 0.5, 0.2));
    /// ```
    #[inline]
    pub fn to_vector3(self) -> crate::core::math::Vector3 {
        crate::core::math::Vector3::new(self.r, self.g, self.b)
    }

    /// ### Converts the color to a Vector4 containing RGBA components.
    ///
    /// Creates a 4D vector with the red, green, blue, and alpha components.
    /// This preserves all color information including transparency.
    ///
    /// # Returns
    /// A Vector4 with (r, g, b, a) components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// # use verturion::core::math::Vector4;
    /// let color = Color::new(1.0, 0.5, 0.2, 0.8);
    /// let rgba_vector = color.to_vector4();
    /// assert_eq!(rgba_vector, Vector4::new(1.0, 0.5, 0.2, 0.8));
    /// ```
    #[inline]
    pub fn to_vector4(self) -> crate::core::math::Vector4 {
        crate::core::math::Vector4::new(self.r, self.g, self.b, self.a)
    }

    /// ### Creates a Color from a Vector3 containing RGB components.
    ///
    /// Creates a color from a 3D vector with full opacity (alpha = 1.0).
    /// The vector components are used as red, green, and blue values.
    ///
    /// # Arguments
    /// * `vector` - The Vector3 containing (r, g, b) components
    ///
    /// # Returns
    /// A new Color with the vector's components and alpha = 1.0.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// # use verturion::core::math::Vector3;
    /// let rgb_vector = Vector3::new(1.0, 0.5, 0.2);
    /// let color = Color::from_vector3(rgb_vector);
    /// assert_eq!(color, Color::new(1.0, 0.5, 0.2, 1.0));
    /// ```
    #[inline]
    pub fn from_vector3(vector: crate::core::math::Vector3) -> Self {
        Self::new(vector.x, vector.y, vector.z, 1.0)
    }

    /// ### Creates a Color from a Vector4 containing RGBA components.
    ///
    /// Creates a color from a 4D vector preserving all components including alpha.
    /// The vector components are used as red, green, blue, and alpha values.
    ///
    /// # Arguments
    /// * `vector` - The Vector4 containing (r, g, b, a) components
    ///
    /// # Returns
    /// A new Color with the vector's components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Color;
    /// # use verturion::core::math::Vector4;
    /// let rgba_vector = Vector4::new(1.0, 0.5, 0.2, 0.8);
    /// let color = Color::from_vector4(rgba_vector);
    /// assert_eq!(color, Color::new(1.0, 0.5, 0.2, 0.8));
    /// ```
    #[inline]
    pub fn from_vector4(vector: crate::core::math::Vector4) -> Self {
        Self::new(vector.x, vector.y, vector.z, vector.w)
    }
}

impl fmt::Display for Color {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "({}, {}, {}, {})", self.r, self.g, self.b, self.a)
    }
}

// ============================================================================
// ARITHMETIC OPERATORS
// ============================================================================

impl std::ops::Add for Color {
    type Output = Self;

    #[inline]
    fn add(self, other: Self) -> Self {
        Self::new(
            self.r + other.r,
            self.g + other.g,
            self.b + other.b,
            self.a + other.a,
        )
    }
}

impl std::ops::Sub for Color {
    type Output = Self;

    #[inline]
    fn sub(self, other: Self) -> Self {
        Self::new(
            self.r - other.r,
            self.g - other.g,
            self.b - other.b,
            self.a - other.a,
        )
    }
}

impl std::ops::Mul<Color> for Color {
    type Output = Self;

    #[inline]
    fn mul(self, other: Color) -> Self {
        Self::new(
            self.r * other.r,
            self.g * other.g,
            self.b * other.b,
            self.a * other.a,
        )
    }
}

impl std::ops::Mul<f32> for Color {
    type Output = Self;

    #[inline]
    fn mul(self, scalar: f32) -> Self {
        Self::new(
            self.r * scalar,
            self.g * scalar,
            self.b * scalar,
            self.a * scalar,
        )
    }
}

impl std::ops::Mul<Color> for f32 {
    type Output = Color;

    #[inline]
    fn mul(self, color: Color) -> Color {
        Color::new(
            self * color.r,
            self * color.g,
            self * color.b,
            self * color.a,
        )
    }
}

impl std::ops::Div<f32> for Color {
    type Output = Self;

    #[inline]
    fn div(self, scalar: f32) -> Self {
        Self::new(
            self.r / scalar,
            self.g / scalar,
            self.b / scalar,
            self.a / scalar,
        )
    }
}

impl std::ops::Neg for Color {
    type Output = Self;

    #[inline]
    fn neg(self) -> Self {
        Self::new(-self.r, -self.g, -self.b, -self.a)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_color_creation() {
        let color = Color::new(1.0, 0.5, 0.2, 0.8);
        assert_eq!(color.r, 1.0);
        assert_eq!(color.g, 0.5);
        assert_eq!(color.b, 0.2);
        assert_eq!(color.a, 0.8);
    }

    #[test]
    fn test_color_constants() {
        assert_eq!(Color::WHITE, Color::new(1.0, 1.0, 1.0, 1.0));
        assert_eq!(Color::BLACK, Color::new(0.0, 0.0, 0.0, 1.0));
        assert_eq!(Color::RED, Color::new(1.0, 0.0, 0.0, 1.0));
        assert_eq!(Color::GREEN, Color::new(0.0, 1.0, 0.0, 1.0));
        assert_eq!(Color::BLUE, Color::new(0.0, 0.0, 1.0, 1.0));
        assert_eq!(Color::YELLOW, Color::new(1.0, 1.0, 0.0, 1.0));
        assert_eq!(Color::CYAN, Color::new(0.0, 1.0, 1.0, 1.0));
        assert_eq!(Color::MAGENTA, Color::new(1.0, 0.0, 1.0, 1.0));
        assert_eq!(Color::TRANSPARENT, Color::new(0.0, 0.0, 0.0, 0.0));
    }

    #[test]
    fn test_color_from_rgb() {
        let color = Color::from_rgb(1.0, 0.5, 0.2);
        assert_eq!(color, Color::new(1.0, 0.5, 0.2, 1.0));
    }

    #[test]
    fn test_color_from_rgba8() {
        let color = Color::from_rgba8(255, 128, 51, 204);
        assert!((color.r - 1.0).abs() < 0.001);
        assert!((color.g - 0.502).abs() < 0.01);
        assert!((color.b - 0.2).abs() < 0.01);
        assert!((color.a - 0.8).abs() < 0.01);
    }

    #[test]
    fn test_color_from_hsv() {
        // Test red (hue = 0)
        let red = Color::from_hsv(0.0, 1.0, 1.0, 1.0);
        assert!(red.is_equal_approx(Color::RED));

        // Test green (hue = 1/3)
        let green = Color::from_hsv(1.0/3.0, 1.0, 1.0, 1.0);
        assert!(green.is_equal_approx(Color::GREEN));

        // Test blue (hue = 2/3)
        let blue = Color::from_hsv(2.0/3.0, 1.0, 1.0, 1.0);
        assert!(blue.is_equal_approx(Color::BLUE));

        // Test gray (saturation = 0)
        let gray = Color::from_hsv(0.0, 0.0, 0.5, 1.0);
        assert!(gray.is_equal_approx(Color::new(0.5, 0.5, 0.5, 1.0)));
    }

    #[test]
    fn test_color_from_html() {
        // Test 6-digit hex
        let red = Color::from_html("#FF0000");
        assert!(red.is_equal_approx(Color::RED));

        // Test 3-digit hex
        let blue = Color::from_html("#00F");
        assert!(blue.is_equal_approx(Color::BLUE));

        // Test 8-digit hex with alpha (0x80 = 128/255 ≈ 0.502)
        let semi_red = Color::from_html("#FF000080");
        let expected_alpha = 128.0 / 255.0;
        assert!(semi_red.is_equal_approx(Color::new(1.0, 0.0, 0.0, expected_alpha)));

        // Test without # prefix
        let green = Color::from_html("00FF00");
        assert!(green.is_equal_approx(Color::GREEN));

        // Test invalid input
        let invalid = Color::from_html("invalid");
        assert_eq!(invalid, Color::WHITE);
    }

    #[test]
    fn test_color_to_hsv() {
        let red = Color::RED;
        let (h, s, v, a) = red.to_hsv();
        assert!((h - 0.0).abs() < 0.001);
        assert!((s - 1.0).abs() < 0.001);
        assert!((v - 1.0).abs() < 0.001);
        assert!((a - 1.0).abs() < 0.001);

        let gray = Color::new(0.5, 0.5, 0.5, 1.0);
        let (_h, s, v, a) = gray.to_hsv();
        assert!((s - 0.0).abs() < 0.001); // Gray has no saturation
        assert!((v - 0.5).abs() < 0.001);
        assert!((a - 1.0).abs() < 0.001);
    }

    #[test]
    fn test_color_to_html() {
        let red = Color::RED;
        assert_eq!(red.to_html(false), "ff0000");
        assert_eq!(red.to_html(true), "ff0000ff");

        let semi_transparent = Color::new(1.0, 0.0, 0.0, 0.5);
        assert_eq!(semi_transparent.to_html(true), "ff000080");
    }

    #[test]
    fn test_color_to_rgba32() {
        let red = Color::RED;
        assert_eq!(red.to_rgba32(), 0xFF0000FF);

        let white = Color::WHITE;
        assert_eq!(white.to_rgba32(), 0xFFFFFFFF);

        let black = Color::BLACK;
        assert_eq!(black.to_rgba32(), 0x000000FF);
    }

    #[test]
    fn test_color_lerp() {
        let red = Color::RED;
        let blue = Color::BLUE;

        let start = red.lerp(blue, 0.0);
        assert!(start.is_equal_approx(red));

        let end = red.lerp(blue, 1.0);
        assert!(end.is_equal_approx(blue));

        let middle = red.lerp(blue, 0.5);
        assert!(middle.is_equal_approx(Color::new(0.5, 0.0, 0.5, 1.0)));
    }

    #[test]
    fn test_color_darkened() {
        let red = Color::RED;
        let dark_red = red.darkened(0.3);
        assert!(dark_red.is_equal_approx(Color::new(0.7, 0.0, 0.0, 1.0)));

        // Test edge cases
        let no_change = red.darkened(0.0);
        assert!(no_change.is_equal_approx(red));

        let black = red.darkened(1.0);
        assert!(black.is_equal_approx(Color::new(0.0, 0.0, 0.0, 1.0)));
    }

    #[test]
    fn test_color_lightened() {
        let red = Color::RED;
        let light_red = red.lightened(0.3);
        assert!(light_red.is_equal_approx(Color::new(1.0, 0.3, 0.3, 1.0)));

        // Test edge cases
        let no_change = red.lightened(0.0);
        assert!(no_change.is_equal_approx(red));

        let white = red.lightened(1.0);
        assert!(white.is_equal_approx(Color::WHITE));
    }

    #[test]
    fn test_color_inverted() {
        let red = Color::RED;
        let cyan = red.inverted();
        assert!(cyan.is_equal_approx(Color::CYAN));

        let white = Color::WHITE;
        let black = white.inverted();
        assert!(black.is_equal_approx(Color::BLACK));
    }

    #[test]
    fn test_color_blend() {
        let background = Color::WHITE;
        let foreground = Color::new(1.0, 0.0, 0.0, 0.5); // Semi-transparent red
        let blended = foreground.blend(background);

        // Should result in pink (red mixed with white)
        assert!(blended.is_equal_approx(Color::new(1.0, 0.5, 0.5, 1.0)));
    }

    #[test]
    fn test_color_clamp() {
        let hdr_color = Color::new(2.0, -0.5, 1.5, 0.8);
        let clamped = hdr_color.clamp(Color::BLACK, Color::WHITE);
        // BLACK has alpha=1.0, so alpha gets clamped to 1.0, not 0.8
        assert!(clamped.is_equal_approx(Color::new(1.0, 0.0, 1.0, 1.0)));
    }

    #[test]
    fn test_color_is_equal_approx() {
        let color1 = Color::new(1.0, 0.5, 0.0, 1.0);
        let color2 = Color::new(1.0000001, 0.4999999, 0.0000001, 1.0);
        assert!(color1.is_equal_approx(color2));

        let color3 = Color::new(1.1, 0.5, 0.0, 1.0);
        assert!(!color1.is_equal_approx(color3));
    }

    #[test]
    fn test_color_get_luminance() {
        let white = Color::WHITE;
        assert!((white.get_luminance() - 1.0).abs() < 0.001);

        let black = Color::BLACK;
        assert!((black.get_luminance() - 0.0).abs() < 0.001);

        // Test standard luminance formula
        let color = Color::new(1.0, 0.0, 0.0, 1.0); // Pure red
        let expected_luminance = 0.299 * 1.0 + 0.587 * 0.0 + 0.114 * 0.0;
        assert!((color.get_luminance() - expected_luminance).abs() < 0.001);
    }

    #[test]
    fn test_color_vector_conversions() {
        let color = Color::new(1.0, 0.5, 0.2, 0.8);

        // Test to_vector3 (RGB only)
        let rgb_vector = color.to_vector3();
        assert_eq!(rgb_vector.x, 1.0);
        assert_eq!(rgb_vector.y, 0.5);
        assert_eq!(rgb_vector.z, 0.2);

        // Test to_vector4 (RGBA)
        let rgba_vector = color.to_vector4();
        assert_eq!(rgba_vector.x, 1.0);
        assert_eq!(rgba_vector.y, 0.5);
        assert_eq!(rgba_vector.z, 0.2);
        assert_eq!(rgba_vector.w, 0.8);

        // Test from_vector3 (RGB with alpha = 1.0)
        let rgb_vec = crate::core::math::Vector3::new(0.7, 0.3, 0.9);
        let color_from_rgb = Color::from_vector3(rgb_vec);
        assert_eq!(color_from_rgb, Color::new(0.7, 0.3, 0.9, 1.0));

        // Test from_vector4 (RGBA)
        let rgba_vec = crate::core::math::Vector4::new(0.7, 0.3, 0.9, 0.6);
        let color_from_rgba = Color::from_vector4(rgba_vec);
        assert_eq!(color_from_rgba, Color::new(0.7, 0.3, 0.9, 0.6));
    }

    #[test]
    fn test_color_arithmetic_operators() {
        let color1 = Color::new(0.8, 0.4, 0.2, 1.0);
        let color2 = Color::new(0.2, 0.6, 0.8, 0.5);

        // Test addition
        let added = color1 + color2;
        assert_eq!(added, Color::new(1.0, 1.0, 1.0, 1.5));

        // Test subtraction
        let subtracted = color1 - color2;
        assert!(subtracted.is_equal_approx(Color::new(0.6, -0.2, -0.6, 0.5)));

        // Test multiplication by color
        let multiplied = color1 * color2;
        assert!(multiplied.is_equal_approx(Color::new(0.16, 0.24, 0.16, 0.5)));

        // Test multiplication by scalar
        let scaled = color1 * 2.0;
        assert_eq!(scaled, Color::new(1.6, 0.8, 0.4, 2.0));

        // Test scalar multiplication (commutative)
        let scaled_commutative = 2.0 * color1;
        assert_eq!(scaled_commutative, scaled);

        // Test division by scalar
        let divided = color1 / 2.0;
        assert_eq!(divided, Color::new(0.4, 0.2, 0.1, 0.5));

        // Test negation
        let negated = -color1;
        assert_eq!(negated, Color::new(-0.8, -0.4, -0.2, -1.0));
    }

    #[test]
    fn test_color_display() {
        let color = Color::new(1.0, 0.5, 0.2, 0.8);
        let display_string = format!("{}", color);
        assert_eq!(display_string, "(1, 0.5, 0.2, 0.8)");
    }

    #[test]
    fn test_color_edge_cases() {
        // Test HSV conversion edge cases
        let black = Color::BLACK;
        let (_h, _s, v, a) = black.to_hsv();
        assert_eq!(v, 0.0); // Black has zero value
        assert_eq!(a, 1.0); // Alpha preserved

        // Test HTML parsing edge cases
        let empty = Color::from_html("");
        assert_eq!(empty, Color::WHITE); // Invalid input returns white

        let too_long = Color::from_html("#FF0000FF00");
        assert_eq!(too_long, Color::WHITE); // Invalid length returns white

        // Test clamping edge cases
        let extreme = Color::new(10.0, -5.0, 2.0, -1.0);
        let clamped = extreme.clamp(Color::new(0.0, 0.0, 0.0, 0.0), Color::new(1.0, 1.0, 1.0, 1.0));
        assert!(clamped.is_equal_approx(Color::new(1.0, 0.0, 1.0, 0.0)));

        // Test blend with transparent colors
        let transparent = Color::TRANSPARENT;
        let opaque = Color::RED;
        let blended = transparent.blend(opaque);
        assert_eq!(blended, opaque); // Transparent over opaque = opaque
    }

    #[test]
    fn test_color_precision() {
        // Test that very small differences are handled correctly
        let color1 = Color::new(0.1, 0.2, 0.3, 0.4);
        let color2 = Color::new(0.1000001, 0.1999999, 0.3000001, 0.3999999);
        assert!(color1.is_equal_approx(color2));

        // Test that larger differences are detected
        let color3 = Color::new(0.11, 0.2, 0.3, 0.4);
        assert!(!color1.is_equal_approx(color3));
    }
}
