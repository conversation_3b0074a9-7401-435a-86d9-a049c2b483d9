/// ### 64-bit Signed Integer Wrapper
///
/// A Godot-compatible wrapper around Rust's i64 primitive type that provides
/// type safety, utility methods, and seamless integration with the Variant system.
/// This type is designed to match <PERSON><PERSON>'s int behavior while maintaining Rust's
/// performance characteristics.
///
/// ## Features
///
/// - **Type Safety**: Prevents accidental mixing of different numeric types
/// - **Godot Compatibility**: Matches Godot's int API and behavior patterns
/// - **Performance**: Zero-cost abstraction with Copy semantics
/// - **Integration**: Works seamlessly with Dictionary, Array, and Variant systems
/// - **Utility Methods**: Provides mathematical operations and conversions
///
/// ## Examples
///
/// ```
/// # use verturion::core::variant::Int;
/// // Create integers
/// let a = Int::new(42);
/// let b = Int::from_primitive(-17);
/// let c = Int::default(); // 0
///
/// // Mathematical operations
/// let abs_val = b.abs();
/// let sign = b.sign();
/// let clamped = a.clamp(Int::new(0), Int::new(100));
///
/// // Conversions
/// let as_float = a.to_float();
/// let as_primitive = a.get();
/// ```

use std::fmt;
use std::hash::{Hash, Hasher};


/// ### 64-bit signed integer wrapper type.
///
/// Provides a type-safe wrapper around i64 with Godot-compatible methods
/// and seamless integration with the Variant system.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct Int {
    /// The wrapped integer value
    value: i64,
}

impl Int {
    /// ### Creates a new Int with the specified value.
    ///
    /// This is the primary constructor for creating Int instances.
    /// The value can be any i64 within the valid range.
    ///
    /// # Arguments
    /// * `value` - The integer value to wrap
    ///
    /// # Returns
    /// A new Int instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let positive = Int::new(42);
    /// let negative = Int::new(-17);
    /// let zero = Int::new(0);
    /// let large = Int::new(9223372036854775807); // i64::MAX
    /// ```
    #[inline]
    pub const fn new(value: i64) -> Self {
        Self { value }
    }

    /// ### Creates a new Int from a primitive i64 value.
    ///
    /// This method provides an alternative constructor that makes the
    /// conversion from primitive types explicit in the code.
    ///
    /// # Arguments
    /// * `value` - The primitive i64 value to wrap
    ///
    /// # Returns
    /// A new Int instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let from_literal = Int::from_primitive(100);
    /// let from_variable = Int::from_primitive(some_i64_value);
    /// ```
    #[inline]
    pub const fn from_primitive(value: i64) -> Self {
        Self::new(value)
    }

    /// ### Gets the wrapped integer value.
    ///
    /// Returns the underlying i64 value contained in this Int wrapper.
    /// This is the primary method for extracting the primitive value.
    ///
    /// # Returns
    /// The wrapped i64 value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let int_val = Int::new(42);
    /// let primitive: i64 = int_val.get();
    /// assert_eq!(primitive, 42);
    /// ```
    #[inline]
    pub const fn get(self) -> i64 {
        self.value
    }

    /// ### Sets the wrapped integer value.
    ///
    /// Updates the value contained in this Int wrapper.
    /// This method provides mutable access to the wrapped value.
    ///
    /// # Arguments
    /// * `value` - The new value to store
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let mut int_val = Int::new(10);
    /// int_val.set(20);
    /// assert_eq!(int_val.get(), 20);
    /// ```
    #[inline]
    pub fn set(&mut self, value: i64) {
        self.value = value;
    }

    /// ### Returns the wrapped value as a primitive i64.
    ///
    /// This method provides an alternative to `get()` with a more explicit name
    /// indicating the conversion to primitive type.
    ///
    /// # Returns
    /// The wrapped i64 value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let int_val = Int::new(-42);
    /// let primitive = int_val.as_primitive();
    /// assert_eq!(primitive, -42);
    /// ```
    #[inline]
    pub const fn as_primitive(self) -> i64 {
        self.value
    }
}

impl Int {
    /// ### Returns the absolute value of the integer.
    ///
    /// Computes the absolute value, returning a positive Int.
    /// For the minimum value (i64::MIN), this will wrap around due to
    /// two's complement representation limitations.
    ///
    /// # Returns
    /// An Int containing the absolute value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let negative = Int::new(-42);
    /// let positive = Int::new(17);
    ///
    /// assert_eq!(negative.abs(), Int::new(42));
    /// assert_eq!(positive.abs(), Int::new(17));
    /// assert_eq!(Int::new(0).abs(), Int::new(0));
    /// ```
    #[inline]
    pub fn abs(self) -> Self {
        Self::new(self.value.abs())
    }

    /// ### Returns the sign of the integer.
    ///
    /// Returns -1 for negative numbers, 0 for zero, and 1 for positive numbers.
    /// This is useful for determining the direction or polarity of a value.
    ///
    /// # Returns
    /// An Int containing -1, 0, or 1 representing the sign.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// assert_eq!(Int::new(-42).sign(), Int::new(-1));
    /// assert_eq!(Int::new(0).sign(), Int::new(0));
    /// assert_eq!(Int::new(42).sign(), Int::new(1));
    /// ```
    #[inline]
    pub fn sign(self) -> Self {
        Self::new(self.value.signum())
    }

    /// ### Clamps the integer to the specified range.
    ///
    /// Returns a value that is constrained to be between min and max (inclusive).
    /// If the value is less than min, returns min. If greater than max, returns max.
    /// Otherwise returns the original value.
    ///
    /// # Arguments
    /// * `min` - The minimum allowed value
    /// * `max` - The maximum allowed value
    ///
    /// # Returns
    /// An Int clamped to the specified range.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let value = Int::new(150);
    /// let min = Int::new(0);
    /// let max = Int::new(100);
    ///
    /// assert_eq!(value.clamp(min, max), Int::new(100));
    /// assert_eq!(Int::new(-10).clamp(min, max), Int::new(0));
    /// assert_eq!(Int::new(50).clamp(min, max), Int::new(50));
    /// ```
    #[inline]
    pub fn clamp(self, min: Self, max: Self) -> Self {
        Self::new(self.value.clamp(min.value, max.value))
    }

    /// ### Returns the minimum of two integers.
    ///
    /// Compares two Int values and returns the smaller one.
    ///
    /// # Arguments
    /// * `other` - The other Int to compare with
    ///
    /// # Returns
    /// The smaller of the two Int values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let a = Int::new(10);
    /// let b = Int::new(20);
    ///
    /// assert_eq!(a.min(b), Int::new(10));
    /// assert_eq!(b.min(a), Int::new(10));
    /// ```
    #[inline]
    pub fn min(self, other: Self) -> Self {
        Self::new(self.value.min(other.value))
    }

    /// ### Returns the maximum of two integers.
    ///
    /// Compares two Int values and returns the larger one.
    ///
    /// # Arguments
    /// * `other` - The other Int to compare with
    ///
    /// # Returns
    /// The larger of the two Int values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let a = Int::new(10);
    /// let b = Int::new(20);
    ///
    /// assert_eq!(a.max(b), Int::new(20));
    /// assert_eq!(b.max(a), Int::new(20));
    /// ```
    #[inline]
    pub fn max(self, other: Self) -> Self {
        Self::new(self.value.max(other.value))
    }
}

impl Int {
    /// ### Converts the integer to a floating-point value.
    ///
    /// Creates a new Float wrapper containing the integer value converted
    /// to f64. This conversion is always exact for values within the
    /// representable range of f64.
    ///
    /// # Returns
    /// A Float containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Int, Float};
    /// let int_val = Int::new(42);
    /// let float_val = int_val.to_float();
    /// assert_eq!(float_val.get(), 42.0);
    /// ```
    #[inline]
    pub fn to_float(self) -> super::Float {
        super::Float::new(self.value as f64)
    }

    /// ### Converts the integer to a Short (16-bit integer).
    ///
    /// Creates a new Short wrapper containing the integer value converted
    /// to i16. Values outside the i16 range will be truncated.
    ///
    /// # Returns
    /// A Short containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Int, Short};
    /// let int_val = Int::new(1000);
    /// let short_val = int_val.to_short();
    /// assert_eq!(short_val.get(), 1000);
    /// ```
    #[inline]
    pub fn to_short(self) -> super::Short {
        super::Short::new(self.value as i16)
    }

    /// ### Converts the integer to an Int8 (8-bit integer).
    ///
    /// Creates a new Int8 wrapper containing the integer value converted
    /// to i8. Values outside the i8 range will be truncated.
    ///
    /// # Returns
    /// An Int8 containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Int, Int8};
    /// let int_val = Int::new(100);
    /// let int8_val = int_val.to_int8();
    /// assert_eq!(int8_val.get(), 100);
    /// ```
    #[inline]
    pub fn to_int8(self) -> super::Int8 {
        super::Int8::new(self.value as i8)
    }
}

impl Default for Int {
    /// ### Creates a default Int with value 0.
    ///
    /// This implementation allows Int to be used in contexts where
    /// a default value is needed, such as in collections or when
    /// using the `Default::default()` method.
    ///
    /// # Returns
    /// An Int with value 0.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let default_int = Int::default();
    /// assert_eq!(default_int.get(), 0);
    ///
    /// let also_default: Int = Default::default();
    /// assert_eq!(also_default, Int::new(0));
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new(0)
    }
}

impl fmt::Display for Int {
    /// ### Formats the Int for display.
    ///
    /// Provides a human-readable string representation of the integer value.
    /// The output matches the standard formatting of i64 values.
    ///
    /// # Arguments
    /// * `f` - The formatter to write to
    ///
    /// # Returns
    /// A formatting result.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Int;
    /// let int_val = Int::new(42);
    /// assert_eq!(format!("{}", int_val), "42");
    ///
    /// let negative = Int::new(-17);
    /// assert_eq!(format!("{}", negative), "-17");
    /// ```
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.value)
    }
}

impl Hash for Int {
    /// ### Computes the hash of the Int.
    ///
    /// Provides hash functionality for use as dictionary keys.
    /// The hash is computed from the underlying i64 value.
    ///
    /// # Arguments
    /// * `state` - The hasher to write to
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.value.hash(state);
    }
}

// Arithmetic operations
impl std::ops::Add for Int {
    type Output = Self;

    #[inline]
    fn add(self, rhs: Self) -> Self::Output {
        Self::new(self.value + rhs.value)
    }
}

impl std::ops::Sub for Int {
    type Output = Self;

    #[inline]
    fn sub(self, rhs: Self) -> Self::Output {
        Self::new(self.value - rhs.value)
    }
}

impl std::ops::Mul for Int {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: Self) -> Self::Output {
        Self::new(self.value * rhs.value)
    }
}

impl std::ops::Div for Int {
    type Output = Self;

    #[inline]
    fn div(self, rhs: Self) -> Self::Output {
        Self::new(self.value / rhs.value)
    }
}

impl std::ops::Rem for Int {
    type Output = Self;

    #[inline]
    fn rem(self, rhs: Self) -> Self::Output {
        Self::new(self.value % rhs.value)
    }
}

impl std::ops::Neg for Int {
    type Output = Self;

    #[inline]
    fn neg(self) -> Self::Output {
        Self::new(-self.value)
    }
}

// Assignment operations
impl std::ops::AddAssign for Int {
    #[inline]
    fn add_assign(&mut self, rhs: Self) {
        self.value += rhs.value;
    }
}

impl std::ops::SubAssign for Int {
    #[inline]
    fn sub_assign(&mut self, rhs: Self) {
        self.value -= rhs.value;
    }
}

impl std::ops::MulAssign for Int {
    #[inline]
    fn mul_assign(&mut self, rhs: Self) {
        self.value *= rhs.value;
    }
}

impl std::ops::DivAssign for Int {
    #[inline]
    fn div_assign(&mut self, rhs: Self) {
        self.value /= rhs.value;
    }
}

impl std::ops::RemAssign for Int {
    #[inline]
    fn rem_assign(&mut self, rhs: Self) {
        self.value %= rhs.value;
    }
}

// Conversions from primitive types
impl From<i64> for Int {
    #[inline]
    fn from(value: i64) -> Self {
        Self::new(value)
    }
}

impl From<i32> for Int {
    #[inline]
    fn from(value: i32) -> Self {
        Self::new(value as i64)
    }
}

impl From<i16> for Int {
    #[inline]
    fn from(value: i16) -> Self {
        Self::new(value as i64)
    }
}

impl From<i8> for Int {
    #[inline]
    fn from(value: i8) -> Self {
        Self::new(value as i64)
    }
}

impl From<u32> for Int {
    #[inline]
    fn from(value: u32) -> Self {
        Self::new(value as i64)
    }
}

impl From<u16> for Int {
    #[inline]
    fn from(value: u16) -> Self {
        Self::new(value as i64)
    }
}

impl From<u8> for Int {
    #[inline]
    fn from(value: u8) -> Self {
        Self::new(value as i64)
    }
}

// Conversions to primitive types
impl From<Int> for i64 {
    #[inline]
    fn from(int: Int) -> Self {
        int.value
    }
}

impl From<Int> for f64 {
    #[inline]
    fn from(int: Int) -> Self {
        int.value as f64
    }
}

impl From<Int> for f32 {
    #[inline]
    fn from(int: Int) -> Self {
        int.value as f32
    }
}

// Variant system integration - From<Int> is already implemented in variant.rs

impl TryFrom<super::Variant> for Int {
    type Error = &'static str;

    /// ### Attempts to extract an Int from a Variant.
    ///
    /// Tries to convert a Variant to an Int. Succeeds if the Variant
    /// contains an IntWrapper, otherwise returns an error.
    ///
    /// # Parameters
    /// - `variant`: The Variant to convert
    ///
    /// # Returns
    /// Result containing the Int on success, error message on failure.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Int, Variant};
    /// let variant = Variant::IntWrapper(Int::new(42));
    /// let int_val: Int = variant.try_into().unwrap();
    /// assert_eq!(int_val.get(), 42);
    /// ```
    fn try_from(variant: super::Variant) -> Result<Self, Self::Error> {
        match variant {
            super::Variant::IntWrapper(int) => Ok(int),
            _ => Err("Variant is not an IntWrapper"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_int_creation() {
        let int1 = Int::new(42);
        assert_eq!(int1.get(), 42);

        let int2 = Int::from_primitive(-17);
        assert_eq!(int2.get(), -17);

        let int3 = Int::default();
        assert_eq!(int3.get(), 0);

        let int4: Int = 100.into();
        assert_eq!(int4.get(), 100);
    }

    #[test]
    fn test_int_value_access() {
        let mut int_val = Int::new(10);
        assert_eq!(int_val.get(), 10);
        assert_eq!(int_val.as_primitive(), 10);

        int_val.set(20);
        assert_eq!(int_val.get(), 20);
    }

    #[test]
    fn test_int_mathematical_operations() {
        let positive = Int::new(42);
        let negative = Int::new(-17);
        let zero = Int::new(0);

        // Test abs
        assert_eq!(positive.abs(), Int::new(42));
        assert_eq!(negative.abs(), Int::new(17));
        assert_eq!(zero.abs(), Int::new(0));

        // Test sign
        assert_eq!(positive.sign(), Int::new(1));
        assert_eq!(negative.sign(), Int::new(-1));
        assert_eq!(zero.sign(), Int::new(0));

        // Test min/max
        assert_eq!(positive.min(negative), negative);
        assert_eq!(positive.max(negative), positive);
        assert_eq!(positive.min(positive), positive);
    }

    #[test]
    fn test_int_clamp() {
        let min = Int::new(0);
        let max = Int::new(100);

        assert_eq!(Int::new(50).clamp(min, max), Int::new(50));
        assert_eq!(Int::new(-10).clamp(min, max), Int::new(0));
        assert_eq!(Int::new(150).clamp(min, max), Int::new(100));
        assert_eq!(Int::new(0).clamp(min, max), Int::new(0));
        assert_eq!(Int::new(100).clamp(min, max), Int::new(100));
    }

    #[test]
    fn test_int_arithmetic_operations() {
        let a = Int::new(10);
        let b = Int::new(3);

        assert_eq!(a + b, Int::new(13));
        assert_eq!(a - b, Int::new(7));
        assert_eq!(a * b, Int::new(30));
        assert_eq!(a / b, Int::new(3));
        assert_eq!(a % b, Int::new(1));
        assert_eq!(-a, Int::new(-10));
    }

    #[test]
    fn test_int_assignment_operations() {
        let mut a = Int::new(10);
        let b = Int::new(3);

        a += b;
        assert_eq!(a, Int::new(13));

        a -= b;
        assert_eq!(a, Int::new(10));

        a *= b;
        assert_eq!(a, Int::new(30));

        a /= b;
        assert_eq!(a, Int::new(10));

        a %= b;
        assert_eq!(a, Int::new(1));
    }

    #[test]
    fn test_int_conversions() {
        let int_val = Int::new(42);

        // Test to other wrapper types
        let float_val = int_val.to_float();
        assert_eq!(float_val.get(), 42.0);

        // Test from primitive types
        let from_i32: Int = (42i32).into();
        assert_eq!(from_i32.get(), 42);

        let from_i16: Int = (42i16).into();
        assert_eq!(from_i16.get(), 42);

        let from_i8: Int = (42i8).into();
        assert_eq!(from_i8.get(), 42);

        let from_u32: Int = (42u32).into();
        assert_eq!(from_u32.get(), 42);

        // Test to primitive types
        let to_i64: i64 = int_val.into();
        assert_eq!(to_i64, 42);

        let to_f64: f64 = int_val.into();
        assert_eq!(to_f64, 42.0);

        let to_f32: f32 = int_val.into();
        assert_eq!(to_f32, 42.0);
    }

    #[test]
    fn test_int_comparison() {
        let a = Int::new(10);
        let b = Int::new(20);
        let c = Int::new(10);

        assert!(a < b);
        assert!(b > a);
        assert_eq!(a, c);
        assert_ne!(a, b);
        assert!(a <= c);
        assert!(a >= c);
    }

    #[test]
    fn test_int_display() {
        assert_eq!(format!("{}", Int::new(42)), "42");
        assert_eq!(format!("{}", Int::new(-17)), "-17");
        assert_eq!(format!("{}", Int::new(0)), "0");
        assert_eq!(format!("{}", Int::new(9223372036854775807)), "9223372036854775807");
    }

    #[test]
    fn test_int_hash() {
        use std::collections::HashMap;

        let mut map = HashMap::new();
        let key1 = Int::new(42);
        let key2 = Int::new(17);

        map.insert(key1, "forty-two");
        map.insert(key2, "seventeen");

        assert_eq!(map.get(&Int::new(42)), Some(&"forty-two"));
        assert_eq!(map.get(&Int::new(17)), Some(&"seventeen"));
        assert_eq!(map.get(&Int::new(100)), None);
    }

    #[test]
    fn test_int_edge_cases() {
        // Test with extreme values
        let max_val = Int::new(i64::MAX);
        let min_val = Int::new(i64::MIN);

        assert_eq!(max_val.get(), i64::MAX);
        assert_eq!(min_val.get(), i64::MIN);

        // Test sign of extreme values
        assert_eq!(max_val.sign(), Int::new(1));
        assert_eq!(min_val.sign(), Int::new(-1));

        // Test abs of min value (wraps around due to two's complement)
        // Note: i64::MIN.abs() panics in debug mode, so we test the wrapping behavior
        let near_min = Int::new(i64::MIN + 1);
        assert_eq!(near_min.abs(), Int::new(i64::MAX));

        // For MIN value, abs() will wrap around in release mode or panic in debug mode
        // This is expected behavior for two's complement arithmetic
    }

    #[test]
    fn test_int_copy_semantics() {
        let original = Int::new(42);
        let copied = original;

        // Both should have the same value
        assert_eq!(original.get(), 42);
        assert_eq!(copied.get(), 42);

        // Modifying one shouldn't affect the other (if we had a mutable copy)
        let mut mutable_copy = original;
        mutable_copy.set(100);
        assert_eq!(original.get(), 42);
        assert_eq!(mutable_copy.get(), 100);
    }
}
