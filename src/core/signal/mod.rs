//! Comprehensive signal system for Godot-compatible node communication.
//!
//! This module provides a complete signal/slot implementation that matches Godot's
//! signal system, enabling type-safe node communication, event handling, and
//! decoupled architecture patterns. It supports signal emission, connection
//! management, and automatic cleanup.

pub mod signal;
pub mod signal_manager;
pub mod callable;

// Re-export main types
pub use signal::{Signal, SignalId, SignalData};
pub use signal_manager::{SignalManager, ConnectionId, ConnectionFlags};
pub use callable::{Callable, CallableTarget};
