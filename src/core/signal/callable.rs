//! Callable system for signal targets and method invocation.
//!
//! This module provides the Callable type that represents targets for signal
//! connections, including method calls, closures, and function pointers.
//! It maintains Godot compatibility while providing flexible callback mechanisms.

use std::fmt;
use std::rc::Rc;
use crate::core::variant::Variant;
use crate::core::scene::node::NodeId;
use super::SignalData;

/// ### Result type for callable execution.
///
/// Represents the result of executing a callable, which can either succeed
/// with an optional return value or fail with an error message.
pub type CallableResult = Result<Option<Variant>, String>;

/// ### Function signature for callable functions.
///
/// Defines the signature for functions that can be called by the signal system.
/// Functions receive signal data and return a result.
pub type CallableFn = dyn Fn(&SignalData) -> CallableResult;

/// ### Target types for callable connections.
///
/// Defines the different types of targets that can receive signal calls,
/// including node methods, global functions, and closures.
pub enum CallableTarget {
    /// Method call on a specific node
    NodeMethod {
        /// Target node ID
        node_id: NodeId,
        /// Method name to call
        method_name: String,
    },
    /// Global function call
    Function {
        /// Function name for identification
        name: String,
        /// Function pointer
        function: Rc<CallableFn>,
    },
    /// Closure or lambda function
    Closure {
        /// Closure name for identification
        name: String,
        /// Closure function
        closure: Rc<CallableFn>,
    },
}

impl CallableTarget {
    /// ### Creates a new node method callable target.
    ///
    /// # Parameters
    /// - `node_id`: The ID of the target node
    /// - `method_name`: The name of the method to call
    ///
    /// # Returns
    /// A new CallableTarget for the specified node method.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::CallableTarget;
    /// # use verturion::core::scene::NodeId;
    /// let target = CallableTarget::node_method(NodeId::new(), "on_button_pressed");
    /// ```
    #[inline]
    pub fn node_method(node_id: NodeId, method_name: &str) -> Self {
        Self::NodeMethod {
            node_id,
            method_name: method_name.to_string(),
        }
    }

    /// ### Creates a new function callable target.
    ///
    /// # Parameters
    /// - `name`: Name for the function (for debugging)
    /// - `function`: The function to call
    ///
    /// # Returns
    /// A new CallableTarget for the specified function.
    #[inline]
    pub fn function<F>(name: &str, function: F) -> Self
    where
        F: Fn(&SignalData) -> CallableResult + 'static,
    {
        Self::Function {
            name: name.to_string(),
            function: Rc::new(function),
        }
    }

    /// ### Creates a new closure callable target.
    ///
    /// # Parameters
    /// - `name`: Name for the closure (for debugging)
    /// - `closure`: The closure to call
    ///
    /// # Returns
    /// A new CallableTarget for the specified closure.
    #[inline]
    pub fn closure<F>(name: &str, closure: F) -> Self
    where
        F: Fn(&SignalData) -> CallableResult + 'static,
    {
        Self::Closure {
            name: name.to_string(),
            closure: Rc::new(closure),
        }
    }

    /// ### Gets the name of this callable target.
    ///
    /// # Returns
    /// A string identifying this callable target.
    #[inline]
    pub fn name(&self) -> &str {
        match self {
            CallableTarget::NodeMethod { method_name, .. } => method_name,
            CallableTarget::Function { name, .. } => name,
            CallableTarget::Closure { name, .. } => name,
        }
    }

    /// ### Gets the target node ID if this is a node method.
    ///
    /// # Returns
    /// Some(NodeId) if this is a node method, None otherwise.
    #[inline]
    pub fn node_id(&self) -> Option<NodeId> {
        match self {
            CallableTarget::NodeMethod { node_id, .. } => Some(*node_id),
            _ => None,
        }
    }

    /// ### Executes this callable target with the given signal data.
    ///
    /// # Parameters
    /// - `data`: The signal data to pass to the callable
    ///
    /// # Returns
    /// The result of executing the callable.
    #[inline]
    pub fn call(&self, data: &SignalData) -> CallableResult {
        match self {
            CallableTarget::NodeMethod { node_id, method_name } => {
                // In a real implementation, this would look up the node
                // and call the specified method
                Err(format!("Node method calls not yet implemented: {}::{}",
                           node_id, method_name))
            }
            CallableTarget::Function { function, .. } => {
                function(data)
            }
            CallableTarget::Closure { closure, .. } => {
                closure(data)
            }
        }
    }
}

impl fmt::Debug for CallableTarget {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CallableTarget::NodeMethod { node_id, method_name } => {
                f.debug_struct("NodeMethod")
                    .field("node_id", node_id)
                    .field("method_name", method_name)
                    .finish()
            }
            CallableTarget::Function { name, .. } => {
                f.debug_struct("Function")
                    .field("name", name)
                    .field("function", &"<function>")
                    .finish()
            }
            CallableTarget::Closure { name, .. } => {
                f.debug_struct("Closure")
                    .field("name", name)
                    .field("closure", &"<closure>")
                    .finish()
            }
        }
    }
}

impl fmt::Display for CallableTarget {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CallableTarget::NodeMethod { node_id, method_name } => {
                write!(f, "NodeMethod({}::{})", node_id, method_name)
            }
            CallableTarget::Function { name, .. } => {
                write!(f, "Function({})", name)
            }
            CallableTarget::Closure { name, .. } => {
                write!(f, "Closure({})", name)
            }
        }
    }
}

/// ### Callable wrapper for signal connection targets.
///
/// Callable provides a unified interface for different types of signal targets,
/// including node methods, functions, and closures. It maintains compatibility
/// with Godot's Callable class while providing type-safe execution.
///
/// ## Core Features
///
/// - **Unified Interface**: Single type for all callable targets
/// - **Type Safety**: Compile-time and runtime type checking
/// - **Flexible Targets**: Support for methods, functions, and closures
/// - **Error Handling**: Comprehensive error reporting for failed calls
/// - **Godot Compatibility**: API matching Godot's Callable class
///
/// # Examples
/// ```
/// # use verturion::core::signal::{Callable, CallableTarget, SignalData};
/// # use verturion::core::variant::Variant;
/// // Create a function callable
/// let callable = Callable::new(CallableTarget::function("test_fn", |data| {
///     println!("Called with {} args", data.arg_count());
///     Ok(Some(Variant::from(42)))
/// }));
///
/// // Execute the callable
/// let data = SignalData::empty();
/// let result = callable.call(&data);
/// assert!(result.is_ok());
/// ```
pub struct Callable {
    /// The target for this callable
    target: CallableTarget,
    /// Whether this callable is valid
    valid: bool,
}

impl Callable {
    /// ### Creates a new Callable with the specified target.
    ///
    /// # Parameters
    /// - `target`: The CallableTarget to wrap
    ///
    /// # Returns
    /// A new Callable instance ready for execution.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::{Callable, CallableTarget};
    /// # use verturion::core::scene::NodeId;
    /// let callable = Callable::new(
    ///     CallableTarget::node_method(NodeId::new(), "on_signal")
    /// );
    /// assert!(callable.is_valid());
    /// ```
    #[inline]
    pub fn new(target: CallableTarget) -> Self {
        Self {
            target,
            valid: true,
        }
    }

    /// ### Creates a callable for a node method.
    ///
    /// # Parameters
    /// - `node_id`: The ID of the target node
    /// - `method_name`: The name of the method to call
    ///
    /// # Returns
    /// A new Callable for the specified node method.
    #[inline]
    pub fn node_method(node_id: NodeId, method_name: &str) -> Self {
        Self::new(CallableTarget::node_method(node_id, method_name))
    }

    /// ### Creates a callable for a function.
    ///
    /// # Parameters
    /// - `name`: Name for the function (for debugging)
    /// - `function`: The function to call
    ///
    /// # Returns
    /// A new Callable for the specified function.
    #[inline]
    pub fn function<F>(name: &str, function: F) -> Self
    where
        F: Fn(&SignalData) -> CallableResult + 'static,
    {
        Self::new(CallableTarget::function(name, function))
    }

    /// ### Creates a callable for a closure.
    ///
    /// # Parameters
    /// - `name`: Name for the closure (for debugging)
    /// - `closure`: The closure to call
    ///
    /// # Returns
    /// A new Callable for the specified closure.
    #[inline]
    pub fn closure<F>(name: &str, closure: F) -> Self
    where
        F: Fn(&SignalData) -> CallableResult + 'static,
    {
        Self::new(CallableTarget::closure(name, closure))
    }

    /// ### Checks if this callable is valid.
    ///
    /// # Returns
    /// True if the callable is valid and can be executed, false otherwise.
    #[inline]
    pub fn is_valid(&self) -> bool {
        self.valid
    }

    /// ### Invalidates this callable.
    ///
    /// This is typically called when the target node is deleted.
    #[inline]
    pub fn invalidate(&mut self) {
        self.valid = false;
    }

    /// ### Gets the name of this callable.
    ///
    /// # Returns
    /// A string identifying this callable.
    #[inline]
    pub fn name(&self) -> &str {
        self.target.name()
    }

    /// ### Gets the target node ID if this is a node method callable.
    ///
    /// # Returns
    /// Some(NodeId) if this is a node method, None otherwise.
    #[inline]
    pub fn node_id(&self) -> Option<NodeId> {
        self.target.node_id()
    }

    /// ### Executes this callable with the given signal data.
    ///
    /// # Parameters
    /// - `data`: The signal data to pass to the callable
    ///
    /// # Returns
    /// The result of executing the callable, or an error if invalid.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::{Callable, SignalData};
    /// # use verturion::core::variant::Variant;
    /// let callable = Callable::function("test", |_| Ok(Some(Variant::from(100))));
    /// let data = SignalData::empty();
    /// let result = callable.call(&data).unwrap();
    /// assert_eq!(result.unwrap().as_int(), Some(100));
    /// ```
    #[inline]
    pub fn call(&self, data: &SignalData) -> CallableResult {
        if !self.valid {
            return Err("Callable is invalid".to_string());
        }

        self.target.call(data)
    }
}

impl fmt::Debug for Callable {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("Callable")
            .field("target", &self.target)
            .field("valid", &self.valid)
            .finish()
    }
}

impl fmt::Display for Callable {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Callable({}, valid: {})", self.target, self.valid)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_callable_target_node_method() {
        let node_id = NodeId::new();
        let target = CallableTarget::node_method(node_id, "test_method");

        assert_eq!(target.name(), "test_method");
        assert_eq!(target.node_id(), Some(node_id));
    }

    #[test]
    fn test_callable_target_function() {
        let target = CallableTarget::function("test_fn", |data| {
            Ok(Some(Variant::from(data.arg_count() as i32)))
        });

        assert_eq!(target.name(), "test_fn");
        assert!(target.node_id().is_none());

        let data = SignalData::new(vec![Variant::from(1), Variant::from(2)]);
        let result = target.call(&data).unwrap();
        assert_eq!(result.unwrap().as_int(), Some(2));
    }

    #[test]
    fn test_callable_target_closure() {
        let multiplier = 5;
        let target = CallableTarget::closure("multiply", move |data| {
            if let Some(arg) = data.get_arg(0) {
                if let Some(value) = arg.as_int() {
                    return Ok(Some(Variant::from(value * multiplier)));
                }
            }
            Ok(None)
        });

        assert_eq!(target.name(), "multiply");

        let data = SignalData::new(vec![Variant::from(10)]);
        let result = target.call(&data).unwrap();
        assert_eq!(result.unwrap().as_int(), Some(50));
    }

    #[test]
    fn test_callable_creation() {
        let node_id = NodeId::new();
        let callable = Callable::node_method(node_id, "test_method");

        assert!(callable.is_valid());
        assert_eq!(callable.name(), "test_method");
        assert_eq!(callable.node_id(), Some(node_id));
    }

    #[test]
    fn test_callable_function() {
        let callable = Callable::function("add", |data| {
            let a = data.get_arg(0).ok_or("Missing first arg")?.as_int().ok_or("Invalid first arg")?;
            let b = data.get_arg(1).ok_or("Missing second arg")?.as_int().ok_or("Invalid second arg")?;
            Ok(Some(Variant::from(a + b)))
        });

        let data = SignalData::new(vec![Variant::from(10), Variant::from(20)]);
        let result = callable.call(&data).unwrap();
        assert_eq!(result.unwrap().as_int(), Some(30));
    }

    #[test]
    fn test_callable_invalidation() {
        let mut callable = Callable::function("test", |_| Ok(None));

        assert!(callable.is_valid());

        callable.invalidate();
        assert!(!callable.is_valid());

        let data = SignalData::empty();
        let result = callable.call(&data);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("invalid"));
    }

    #[test]
    fn test_callable_display() {
        let callable = Callable::function("test_function", |_| Ok(None));
        let display_str = format!("{}", callable);

        assert!(display_str.contains("Callable"));
        assert!(display_str.contains("test_function"));
        assert!(display_str.contains("valid: true"));
    }
}
