//! Core signal types and data structures for the signal system.
//!
//! This module provides the fundamental signal types including Signal, SignalId,
//! and SignalData that form the foundation of the Godot-compatible signal system.
//! It ensures type safety, efficient signal identification, and flexible data passing.

use std::fmt;
use std::sync::atomic::{AtomicU64, Ordering};
use crate::core::variant::Variant;
use crate::core::scene::node::NodeId;

/// Global signal ID counter for unique identification
static NEXT_SIGNAL_ID: AtomicU64 = AtomicU64::new(1);

/// ### Unique identifier for signals in the system.
///
/// SignalId provides a unique, stable identifier for each signal instance,
/// enabling efficient signal tracking, connection management, and debugging.
/// Each signal gets a globally unique ID when created.
///
/// # Examples
/// ```
/// # use verturion::core::signal::SignalId;
/// let id1 = SignalId::new();
/// let id2 = SignalId::new();
/// assert_ne!(id1, id2); // Each ID is unique
/// ```
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Hash)]
pub struct SignalId(pub u64);

impl SignalId {
    /// ### Creates a new unique SignalId.
    ///
    /// # Returns
    /// A new SignalId with a globally unique identifier.
    #[inline]
    pub fn new() -> Self {
        Self(NEXT_SIGNAL_ID.fetch_add(1, Ordering::Relaxed))
    }

    /// ### Gets the raw ID value.
    ///
    /// # Returns
    /// The underlying u64 identifier.
    #[inline]
    pub fn id(self) -> u64 {
        self.0
    }
}

impl fmt::Display for SignalId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "SignalId({})", self.0)
    }
}

/// ### Signal data container for passing arguments with signals.
///
/// SignalData encapsulates the arguments passed when a signal is emitted,
/// providing type-safe data transmission between signal emitters and receivers.
/// It supports multiple arguments through a vector of Variants.
///
/// # Examples
/// ```
/// # use verturion::core::signal::SignalData;
/// # use verturion::core::variant::Variant;
/// // Create signal data with multiple arguments
/// let data = SignalData::new(vec![
///     Variant::from(42),
///     Variant::from("hello"),
///     Variant::from(3.14),
/// ]);
///
/// assert_eq!(data.arg_count(), 3);
/// assert_eq!(data.get_arg(0).unwrap().as_int(), Some(42));
/// ```
#[derive(Debug, Clone)]
pub struct SignalData {
    /// Signal arguments as Variants
    args: Vec<Variant>,
}

impl SignalData {
    /// ### Creates new SignalData with the given arguments.
    ///
    /// # Parameters
    /// - `args`: Vector of Variant arguments to pass with the signal
    ///
    /// # Returns
    /// A new SignalData instance containing the arguments.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::SignalData;
    /// # use verturion::core::variant::Variant;
    /// let data = SignalData::new(vec![Variant::from(100)]);
    /// assert_eq!(data.arg_count(), 1);
    /// ```
    #[inline]
    pub fn new(args: Vec<Variant>) -> Self {
        Self { args }
    }

    /// ### Creates empty SignalData with no arguments.
    ///
    /// # Returns
    /// A new SignalData instance with no arguments.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::SignalData;
    /// let data = SignalData::empty();
    /// assert_eq!(data.arg_count(), 0);
    /// ```
    #[inline]
    pub fn empty() -> Self {
        Self { args: Vec::new() }
    }

    /// ### Gets the number of arguments.
    ///
    /// # Returns
    /// The number of arguments in this signal data.
    #[inline]
    pub fn arg_count(&self) -> usize {
        self.args.len()
    }

    /// ### Gets a specific argument by index.
    ///
    /// # Parameters
    /// - `index`: The index of the argument to retrieve
    ///
    /// # Returns
    /// Some(&Variant) if the index is valid, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::SignalData;
    /// # use verturion::core::variant::Variant;
    /// let data = SignalData::new(vec![Variant::from("test")]);
    /// assert_eq!(data.get_arg(0).unwrap().as_string().unwrap().as_str(), "test");
    /// assert!(data.get_arg(1).is_none());
    /// ```
    #[inline]
    pub fn get_arg(&self, index: usize) -> Option<&Variant> {
        self.args.get(index)
    }

    /// ### Gets all arguments as a slice.
    ///
    /// # Returns
    /// A slice containing all signal arguments.
    #[inline]
    pub fn args(&self) -> &[Variant] {
        &self.args
    }

    /// ### Adds an argument to the signal data.
    ///
    /// # Parameters
    /// - `arg`: The Variant argument to add
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::SignalData;
    /// # use verturion::core::variant::Variant;
    /// let mut data = SignalData::empty();
    /// data.add_arg(Variant::from(42));
    /// assert_eq!(data.arg_count(), 1);
    /// ```
    #[inline]
    pub fn add_arg(&mut self, arg: Variant) {
        self.args.push(arg);
    }
}

impl fmt::Display for SignalData {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "SignalData({} args)", self.args.len())
    }
}

/// ### Core signal structure for event emission and handling.
///
/// Signal represents a named event that can be emitted by nodes and connected
/// to callable targets. It maintains Godot compatibility while providing
/// type-safe signal handling and efficient connection management.
///
/// ## Core Features
///
/// - **Named Events**: Signals have string names for identification
/// - **Type Safety**: Arguments passed through Variant system
/// - **Connection Management**: Support for multiple connections per signal
/// - **Emission Control**: Emit signals with arbitrary arguments
/// - **Godot Compatibility**: API matching Godot's signal system
///
/// # Examples
/// ```
/// # use verturion::core::signal::Signal;
/// # use verturion::core::scene::NodeId;
/// // Create a signal
/// let signal = Signal::new("button_pressed", NodeId::new());
/// assert_eq!(signal.name(), "button_pressed");
/// assert_eq!(signal.connection_count(), 0);
/// ```
#[derive(Debug, Clone)]
pub struct Signal {
    /// Unique signal identifier
    id: SignalId,
    /// Signal name for identification
    name: String,
    /// Node that owns this signal
    owner: NodeId,
    /// Number of active connections
    connection_count: usize,
}

impl Signal {
    /// ### Creates a new Signal with the specified name and owner.
    ///
    /// # Parameters
    /// - `name`: The name of the signal for identification
    /// - `owner`: The NodeId of the node that owns this signal
    ///
    /// # Returns
    /// A new Signal instance ready for connection and emission.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::Signal;
    /// # use verturion::core::scene::NodeId;
    /// let signal = Signal::new("health_changed", NodeId::new());
    /// assert_eq!(signal.name(), "health_changed");
    /// ```
    #[inline]
    pub fn new(name: &str, owner: NodeId) -> Self {
        Self {
            id: SignalId::new(),
            name: name.to_string(),
            owner,
            connection_count: 0,
        }
    }

    /// ### Gets the signal's unique identifier.
    ///
    /// # Returns
    /// The SignalId for this signal.
    #[inline]
    pub fn id(&self) -> SignalId {
        self.id
    }

    /// ### Gets the signal's name.
    ///
    /// # Returns
    /// The name of this signal.
    #[inline]
    pub fn name(&self) -> &str {
        &self.name
    }

    /// ### Gets the owner node ID.
    ///
    /// # Returns
    /// The NodeId of the node that owns this signal.
    #[inline]
    pub fn owner(&self) -> NodeId {
        self.owner
    }

    /// ### Gets the number of active connections.
    ///
    /// # Returns
    /// The number of connections currently attached to this signal.
    #[inline]
    pub fn connection_count(&self) -> usize {
        self.connection_count
    }

    /// ### Increments the connection count.
    ///
    /// This is called internally when a new connection is made.
    #[inline]
    pub(crate) fn add_connection(&mut self) {
        self.connection_count += 1;
    }

    /// ### Decrements the connection count.
    ///
    /// This is called internally when a connection is removed.
    #[inline]
    pub(crate) fn remove_connection(&mut self) {
        if self.connection_count > 0 {
            self.connection_count -= 1;
        }
    }
}

impl fmt::Display for Signal {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Signal({}, owner: {}, connections: {})",
               self.name, self.owner, self.connection_count)
    }
}

impl PartialEq for Signal {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

impl Eq for Signal {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_signal_id_creation() {
        let id1 = SignalId::new();
        let id2 = SignalId::new();

        assert_ne!(id1, id2);
        assert!(id1.id() > 0);
        assert!(id2.id() > 0);
        assert!(id2.id() > id1.id());
    }

    #[test]
    fn test_signal_id_display() {
        let id = SignalId::new();
        let display_str = format!("{}", id);
        assert!(display_str.contains("SignalId"));
        assert!(display_str.contains(&id.id().to_string()));
    }

    #[test]
    fn test_signal_data_creation() {
        let data = SignalData::new(vec![
            Variant::from(42),
            Variant::from("test"),
        ]);

        assert_eq!(data.arg_count(), 2);
        assert_eq!(data.get_arg(0).unwrap().as_int(), Some(42));
        assert_eq!(data.get_arg(1).unwrap().as_string().unwrap().as_str(), "test");
        assert!(data.get_arg(2).is_none());
    }

    #[test]
    fn test_signal_data_empty() {
        let data = SignalData::empty();
        assert_eq!(data.arg_count(), 0);
        assert!(data.get_arg(0).is_none());
    }

    #[test]
    fn test_signal_data_add_arg() {
        let mut data = SignalData::empty();
        assert_eq!(data.arg_count(), 0);

        data.add_arg(Variant::from(100));
        assert_eq!(data.arg_count(), 1);
        assert_eq!(data.get_arg(0).unwrap().as_int(), Some(100));
    }

    #[test]
    fn test_signal_creation() {
        let owner = NodeId::new();
        let signal = Signal::new("test_signal", owner);

        assert_eq!(signal.name(), "test_signal");
        assert_eq!(signal.owner(), owner);
        assert_eq!(signal.connection_count(), 0);
    }

    #[test]
    fn test_signal_connection_count() {
        let owner = NodeId::new();
        let mut signal = Signal::new("test_signal", owner);

        assert_eq!(signal.connection_count(), 0);

        signal.add_connection();
        assert_eq!(signal.connection_count(), 1);

        signal.add_connection();
        assert_eq!(signal.connection_count(), 2);

        signal.remove_connection();
        assert_eq!(signal.connection_count(), 1);

        signal.remove_connection();
        assert_eq!(signal.connection_count(), 0);

        // Test underflow protection
        signal.remove_connection();
        assert_eq!(signal.connection_count(), 0);
    }

    #[test]
    fn test_signal_equality() {
        let owner = NodeId::new();
        let signal1 = Signal::new("test", owner);
        let signal2 = Signal::new("test", owner);
        let signal1_clone = signal1.clone();

        assert_eq!(signal1, signal1_clone);
        assert_ne!(signal1, signal2); // Different IDs
    }

    #[test]
    fn test_signal_display() {
        let owner = NodeId::new();
        let signal = Signal::new("button_pressed", owner);
        let display_str = format!("{}", signal);

        assert!(display_str.contains("button_pressed"));
        assert!(display_str.contains("connections: 0"));
    }
}
