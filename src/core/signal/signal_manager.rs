//! Signal manager for connection management and signal emission.
//!
//! This module provides the SignalManager that handles signal connections,
//! emission, and cleanup. It maintains a global registry of signals and
//! connections while ensuring memory safety and efficient signal processing.

use std::fmt;
use std::collections::HashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use crate::core::scene::node::NodeId;
use super::{Signal, SignalId, SignalData, Callable};

/// Global connection ID counter for unique identification
static NEXT_CONNECTION_ID: AtomicU64 = AtomicU64::new(1);

/// ### Unique identifier for signal connections.
///
/// ConnectionId provides a unique identifier for each signal connection,
/// enabling efficient connection tracking and removal.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub struct ConnectionId(pub u64);

impl ConnectionId {
    /// ### Creates a new unique ConnectionId.
    ///
    /// # Returns
    /// A new ConnectionId with a globally unique identifier.
    #[inline]
    pub fn new() -> Self {
        Self(NEXT_CONNECTION_ID.fetch_add(1, Ordering::Relaxed))
    }

    /// ### Gets the raw ID value.
    ///
    /// # Returns
    /// The underlying u64 identifier.
    #[inline]
    pub fn id(self) -> u64 {
        self.0
    }
}

impl fmt::Display for ConnectionId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "ConnectionId({})", self.0)
    }
}

/// ### Flags for signal connection behavior.
///
/// Defines how signal connections should behave when emitted,
/// including one-shot connections and deferred execution.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct ConnectionFlags {
    /// Whether this connection should be removed after first emission
    pub one_shot: bool,
    /// Whether this connection should be executed deferred
    pub deferred: bool,
}

impl ConnectionFlags {
    /// ### Creates default connection flags.
    ///
    /// # Returns
    /// ConnectionFlags with default settings (persistent, immediate).
    #[inline]
    pub fn default() -> Self {
        Self {
            one_shot: false,
            deferred: false,
        }
    }

    /// ### Creates one-shot connection flags.
    ///
    /// # Returns
    /// ConnectionFlags for a one-time connection.
    #[inline]
    pub fn one_shot() -> Self {
        Self {
            one_shot: true,
            deferred: false,
        }
    }

    /// ### Creates deferred connection flags.
    ///
    /// # Returns
    /// ConnectionFlags for deferred execution.
    #[inline]
    pub fn deferred() -> Self {
        Self {
            one_shot: false,
            deferred: true,
        }
    }
}

/// ### Internal connection data structure.
///
/// Stores the connection information including the callable target,
/// flags, and metadata for efficient connection management.
struct Connection {
    /// Unique connection identifier
    id: ConnectionId,
    /// Signal this connection is attached to
    signal_id: SignalId,
    /// Callable target for this connection
    callable: Callable,
    /// Connection behavior flags
    flags: ConnectionFlags,
    /// Whether this connection is active
    active: bool,
}

impl Connection {
    /// ### Creates a new Connection.
    ///
    /// # Parameters
    /// - `signal_id`: The signal this connection is attached to
    /// - `callable`: The callable target
    /// - `flags`: Connection behavior flags
    ///
    /// # Returns
    /// A new Connection instance.
    #[inline]
    fn new(signal_id: SignalId, callable: Callable, flags: ConnectionFlags) -> Self {
        Self {
            id: ConnectionId::new(),
            signal_id,
            callable,
            flags,
            active: true,
        }
    }
}

/// ### Central signal management system.
///
/// SignalManager provides the core signal system functionality including
/// signal registration, connection management, and emission handling.
/// It maintains a global registry of all signals and their connections.
///
/// ## Core Features
///
/// - **Signal Registry**: Central registry of all signals in the system
/// - **Connection Management**: Efficient connection creation and removal
/// - **Signal Emission**: Broadcast signals to all connected callables
/// - **Memory Management**: Automatic cleanup of invalid connections
/// - **Thread Safety**: Safe concurrent access to signal data
///
/// # Examples
/// ```
/// # use verturion::core::signal::{SignalManager, Signal, Callable, SignalData};
/// # use verturion::core::scene::NodeId;
/// # use verturion::core::variant::Variant;
/// let mut manager = SignalManager::new();
///
/// // Register a signal
/// let owner = NodeId::new();
/// let signal = Signal::new("test_signal", owner);
/// let signal_id = signal.id();
/// manager.register_signal(signal);
///
/// // Connect a callable
/// let callable = Callable::function("handler", |data| {
///     println!("Signal received with {} args", data.arg_count());
///     Ok(None)
/// });
/// let connection_id = manager.connect(signal_id, callable, Default::default()).unwrap();
///
/// // Emit the signal
/// let data = SignalData::new(vec![Variant::from(42)]);
/// manager.emit(signal_id, data);
/// ```
pub struct SignalManager {
    /// Registry of all signals by ID
    signals: HashMap<SignalId, Signal>,
    /// Registry of all connections by ID
    connections: HashMap<ConnectionId, Connection>,
    /// Mapping from signal ID to connection IDs
    signal_connections: HashMap<SignalId, Vec<ConnectionId>>,
    /// Mapping from node ID to owned signal IDs
    node_signals: HashMap<NodeId, Vec<SignalId>>,
}

impl SignalManager {
    /// ### Creates a new SignalManager.
    ///
    /// # Returns
    /// A new SignalManager instance ready for signal management.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::SignalManager;
    /// let manager = SignalManager::new();
    /// assert_eq!(manager.signal_count(), 0);
    /// assert_eq!(manager.connection_count(), 0);
    /// ```
    #[inline]
    pub fn new() -> Self {
        Self {
            signals: HashMap::new(),
            connections: HashMap::new(),
            signal_connections: HashMap::new(),
            node_signals: HashMap::new(),
        }
    }

    /// ### Registers a new signal in the manager.
    ///
    /// # Parameters
    /// - `signal`: The signal to register
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::{SignalManager, Signal};
    /// # use verturion::core::scene::NodeId;
    /// let mut manager = SignalManager::new();
    /// let signal = Signal::new("test", NodeId::new());
    /// manager.register_signal(signal);
    /// assert_eq!(manager.signal_count(), 1);
    /// ```
    #[inline]
    pub fn register_signal(&mut self, signal: Signal) {
        let signal_id = signal.id();
        let owner = signal.owner();

        // Add to signals registry
        self.signals.insert(signal_id, signal);

        // Add to node signals mapping
        self.node_signals.entry(owner).or_insert_with(Vec::new).push(signal_id);

        // Initialize empty connections list
        self.signal_connections.insert(signal_id, Vec::new());
    }

    /// ### Connects a callable to a signal.
    ///
    /// # Parameters
    /// - `signal_id`: The ID of the signal to connect to
    /// - `callable`: The callable to connect
    /// - `flags`: Connection behavior flags
    ///
    /// # Returns
    /// Result containing the ConnectionId if successful, or an error message.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::{SignalManager, Signal, Callable, ConnectionFlags};
    /// # use verturion::core::scene::NodeId;
    /// let mut manager = SignalManager::new();
    /// let signal = Signal::new("test", NodeId::new());
    /// let signal_id = signal.id();
    /// manager.register_signal(signal);
    ///
    /// let callable = Callable::function("handler", |_| Ok(None));
    /// let connection_id = manager.connect(signal_id, callable, ConnectionFlags::default());
    /// assert!(connection_id.is_ok());
    /// ```
    #[inline]
    pub fn connect(&mut self, signal_id: SignalId, callable: Callable, flags: ConnectionFlags) -> Result<ConnectionId, String> {
        // Check if signal exists
        if !self.signals.contains_key(&signal_id) {
            return Err(format!("Signal {} not found", signal_id));
        }

        // Create connection
        let connection = Connection::new(signal_id, callable, flags);
        let connection_id = connection.id;

        // Add to connections registry
        self.connections.insert(connection_id, connection);

        // Add to signal connections mapping
        if let Some(connections) = self.signal_connections.get_mut(&signal_id) {
            connections.push(connection_id);
        }

        // Update signal connection count
        if let Some(signal) = self.signals.get_mut(&signal_id) {
            signal.add_connection();
        }

        Ok(connection_id)
    }

    /// ### Disconnects a specific connection.
    ///
    /// # Parameters
    /// - `connection_id`: The ID of the connection to disconnect
    ///
    /// # Returns
    /// True if the connection was found and removed, false otherwise.
    #[inline]
    pub fn disconnect(&mut self, connection_id: ConnectionId) -> bool {
        if let Some(connection) = self.connections.remove(&connection_id) {
            // Remove from signal connections mapping
            if let Some(connections) = self.signal_connections.get_mut(&connection.signal_id) {
                connections.retain(|&id| id != connection_id);
            }

            // Update signal connection count
            if let Some(signal) = self.signals.get_mut(&connection.signal_id) {
                signal.remove_connection();
            }

            true
        } else {
            false
        }
    }

    /// ### Emits a signal with the given data.
    ///
    /// # Parameters
    /// - `signal_id`: The ID of the signal to emit
    /// - `data`: The signal data to pass to connected callables
    ///
    /// # Returns
    /// The number of callables that were successfully executed.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::signal::{SignalManager, Signal, Callable, SignalData};
    /// # use verturion::core::scene::NodeId;
    /// # use verturion::core::variant::Variant;
    /// let mut manager = SignalManager::new();
    /// let signal = Signal::new("test", NodeId::new());
    /// let signal_id = signal.id();
    /// manager.register_signal(signal);
    ///
    /// let callable = Callable::function("handler", |_| Ok(None));
    /// manager.connect(signal_id, callable, Default::default()).unwrap();
    ///
    /// let data = SignalData::new(vec![Variant::from(42)]);
    /// let executed = manager.emit(signal_id, data);
    /// assert_eq!(executed, 1);
    /// ```
    #[inline]
    pub fn emit(&mut self, signal_id: SignalId, data: SignalData) -> usize {
        let mut executed_count = 0;
        let mut connections_to_remove = Vec::new();

        // Get connections for this signal
        if let Some(connection_ids) = self.signal_connections.get(&signal_id).cloned() {
            for connection_id in connection_ids {
                if let Some(connection) = self.connections.get_mut(&connection_id) {
                    if connection.active && connection.callable.is_valid() {
                        // Execute the callable
                        match connection.callable.call(&data) {
                            Ok(_) => {
                                executed_count += 1;

                                // Mark one-shot connections for removal
                                if connection.flags.one_shot {
                                    connections_to_remove.push(connection_id);
                                }
                            }
                            Err(err) => {
                                eprintln!("Error executing callable {}: {}", connection.callable.name(), err);
                                // Mark failed connections as inactive
                                connection.active = false;
                            }
                        }
                    } else {
                        // Mark invalid connections for removal
                        connections_to_remove.push(connection_id);
                    }
                }
            }
        }

        // Remove one-shot and invalid connections
        for connection_id in connections_to_remove {
            self.disconnect(connection_id);
        }

        executed_count
    }

    /// ### Gets the number of registered signals.
    ///
    /// # Returns
    /// The number of signals in the registry.
    #[inline]
    pub fn signal_count(&self) -> usize {
        self.signals.len()
    }

    /// ### Gets the number of active connections.
    ///
    /// # Returns
    /// The number of connections in the registry.
    #[inline]
    pub fn connection_count(&self) -> usize {
        self.connections.len()
    }

    /// ### Checks if a signal exists.
    ///
    /// # Parameters
    /// - `signal_id`: The ID of the signal to check
    ///
    /// # Returns
    /// True if the signal exists, false otherwise.
    #[inline]
    pub fn has_signal(&self, signal_id: SignalId) -> bool {
        self.signals.contains_key(&signal_id)
    }

    /// ### Gets a signal by ID.
    ///
    /// # Parameters
    /// - `signal_id`: The ID of the signal to get
    ///
    /// # Returns
    /// Some(&Signal) if found, None otherwise.
    #[inline]
    pub fn get_signal(&self, signal_id: SignalId) -> Option<&Signal> {
        self.signals.get(&signal_id)
    }

    /// ### Removes all signals owned by a specific node.
    ///
    /// This is typically called when a node is deleted to clean up its signals.
    ///
    /// # Parameters
    /// - `node_id`: The ID of the node whose signals should be removed
    ///
    /// # Returns
    /// The number of signals that were removed.
    #[inline]
    pub fn remove_node_signals(&mut self, node_id: NodeId) -> usize {
        let mut removed_count = 0;

        if let Some(signal_ids) = self.node_signals.remove(&node_id) {
            for signal_id in signal_ids {
                // Remove all connections for this signal
                if let Some(connection_ids) = self.signal_connections.remove(&signal_id) {
                    for connection_id in connection_ids {
                        self.connections.remove(&connection_id);
                    }
                }

                // Remove the signal itself
                if self.signals.remove(&signal_id).is_some() {
                    removed_count += 1;
                }
            }
        }

        removed_count
    }
}

impl Default for SignalManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::variant::Variant;

    #[test]
    fn test_connection_id_creation() {
        let id1 = ConnectionId::new();
        let id2 = ConnectionId::new();

        assert_ne!(id1, id2);
        assert!(id1.id() > 0);
        assert!(id2.id() > id1.id());
    }

    #[test]
    fn test_connection_flags() {
        let default_flags = ConnectionFlags::default();
        assert!(!default_flags.one_shot);
        assert!(!default_flags.deferred);

        let one_shot_flags = ConnectionFlags::one_shot();
        assert!(one_shot_flags.one_shot);
        assert!(!one_shot_flags.deferred);

        let deferred_flags = ConnectionFlags::deferred();
        assert!(!deferred_flags.one_shot);
        assert!(deferred_flags.deferred);
    }

    #[test]
    fn test_signal_manager_creation() {
        let manager = SignalManager::new();
        assert_eq!(manager.signal_count(), 0);
        assert_eq!(manager.connection_count(), 0);
    }

    #[test]
    fn test_signal_registration() {
        let mut manager = SignalManager::new();
        let owner = NodeId::new();
        let signal = Signal::new("test_signal", owner);
        let signal_id = signal.id();

        manager.register_signal(signal);
        assert_eq!(manager.signal_count(), 1);
        assert!(manager.has_signal(signal_id));
        assert!(manager.get_signal(signal_id).is_some());
    }

    #[test]
    fn test_signal_connection() {
        let mut manager = SignalManager::new();
        let owner = NodeId::new();
        let signal = Signal::new("test_signal", owner);
        let signal_id = signal.id();
        manager.register_signal(signal);

        let callable = Callable::function("test_handler", |_| Ok(None));
        let connection_result = manager.connect(signal_id, callable, ConnectionFlags::default());

        assert!(connection_result.is_ok());
        assert_eq!(manager.connection_count(), 1);
    }

    #[test]
    fn test_signal_emission() {
        let mut manager = SignalManager::new();
        let owner = NodeId::new();
        let signal = Signal::new("test_signal", owner);
        let signal_id = signal.id();
        manager.register_signal(signal);

        let callable = Callable::function("counter", |_| {
            // Note: This won't actually work due to closure capture rules
            // In a real implementation, we'd use a different approach
            Ok(Some(Variant::from(42)))
        });

        manager.connect(signal_id, callable, ConnectionFlags::default()).unwrap();

        let data = SignalData::new(vec![Variant::from(100)]);
        let executed = manager.emit(signal_id, data);
        assert_eq!(executed, 1);
    }

    #[test]
    fn test_one_shot_connection() {
        let mut manager = SignalManager::new();
        let owner = NodeId::new();
        let signal = Signal::new("test_signal", owner);
        let signal_id = signal.id();
        manager.register_signal(signal);

        let callable = Callable::function("one_shot", |_| Ok(None));
        manager.connect(signal_id, callable, ConnectionFlags::one_shot()).unwrap();

        assert_eq!(manager.connection_count(), 1);

        // First emission should execute and remove the connection
        let data = SignalData::empty();
        let executed = manager.emit(signal_id, data.clone());
        assert_eq!(executed, 1);
        assert_eq!(manager.connection_count(), 0);

        // Second emission should not execute anything
        let executed = manager.emit(signal_id, data);
        assert_eq!(executed, 0);
    }

    #[test]
    fn test_node_signal_cleanup() {
        let mut manager = SignalManager::new();
        let owner = NodeId::new();

        let signal1 = Signal::new("signal1", owner);
        let signal2 = Signal::new("signal2", owner);
        let signal1_id = signal1.id();
        let signal2_id = signal2.id();

        manager.register_signal(signal1);
        manager.register_signal(signal2);
        assert_eq!(manager.signal_count(), 2);

        let removed = manager.remove_node_signals(owner);
        assert_eq!(removed, 2);
        assert_eq!(manager.signal_count(), 0);
        assert!(!manager.has_signal(signal1_id));
        assert!(!manager.has_signal(signal2_id));
    }
}
