use std::fmt;
use super::Vector2;

/// ### 2D Transformation Matrix
///
/// Transform2D represents a 2D transformation matrix that can perform translation,
/// rotation, and scaling operations on 2D vectors and points. It uses a 2x3 matrix
/// representation compatible with <PERSON><PERSON>'s Transform2D.
///
/// The matrix is structured as:
/// ```
/// | x.x  y.x  origin.x |
/// | x.y  y.y  origin.y |
/// |  0    0      1     | (implicit)
/// ```
///
/// Where:
/// - `x` is the first column vector (transformed X axis)
/// - `y` is the second column vector (transformed Y axis)
/// - `origin` is the translation vector
///
/// # Examples
/// ```
/// # use verturion::core::math::{Transform2D, Vector2};
/// // Create identity transform
/// let identity = Transform2D::IDENTITY;
///
/// // Create translation transform
/// let translation = Transform2D::from_translation(Vector2::new(10.0, 20.0));
///
/// // Create rotation transform
/// let rotation = Transform2D::from_rotation(std::f32::consts::FRAC_PI_4);
///
/// // Transform a point
/// let point = Vector2::new(1.0, 0.0);
/// let transformed = rotation.transform_point(point);
/// ```
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct Transform2D {
    /// The first column vector (transformed X axis)
    pub x: Vector2,
    /// The second column vector (transformed Y axis)
    pub y: Vector2,
    /// The translation vector (origin)
    pub origin: Vector2,
}

impl Transform2D {
    /// Identity transformation matrix (no transformation applied)
    pub const IDENTITY: Transform2D = Transform2D {
        x: Vector2::new(1.0, 0.0),
        y: Vector2::new(0.0, 1.0),
        origin: Vector2::ZERO,
    };

    /// Flip transformation along the X axis
    pub const FLIP_X: Transform2D = Transform2D {
        x: Vector2::new(-1.0, 0.0),
        y: Vector2::new(0.0, 1.0),
        origin: Vector2::ZERO,
    };

    /// Flip transformation along the Y axis
    pub const FLIP_Y: Transform2D = Transform2D {
        x: Vector2::new(1.0, 0.0),
        y: Vector2::new(0.0, -1.0),
        origin: Vector2::ZERO,
    };

    /// ### Creates a new Transform2D from column vectors and origin.
    ///
    /// # Arguments
    /// * `x` - The first column vector (transformed X axis)
    /// * `y` - The second column vector (transformed Y axis)
    /// * `origin` - The translation vector
    ///
    /// # Returns
    /// A new Transform2D with the specified components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::new(
    ///     Vector2::new(1.0, 0.0),
    ///     Vector2::new(0.0, 1.0),
    ///     Vector2::new(10.0, 20.0)
    /// );
    /// ```
    #[inline]
    pub const fn new(x: Vector2, y: Vector2, origin: Vector2) -> Self {
        Self { x, y, origin }
    }

    /// ### Creates a Transform2D from rotation angle and translation.
    ///
    /// # Arguments
    /// * `rotation` - Rotation angle in radians
    /// * `translation` - Translation vector
    ///
    /// # Returns
    /// A new Transform2D representing the rotation and translation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_rotation_translation(
    ///     std::f32::consts::FRAC_PI_4,
    ///     Vector2::new(10.0, 20.0)
    /// );
    /// ```
    #[inline]
    pub fn from_rotation_translation(rotation: f32, translation: Vector2) -> Self {
        let (sin, cos) = rotation.sin_cos();
        Self {
            x: Vector2::new(cos, sin),
            y: Vector2::new(-sin, cos),
            origin: translation,
        }
    }

    /// ### Creates a Transform2D from rotation, scale, and translation.
    ///
    /// # Arguments
    /// * `rotation` - Rotation angle in radians
    /// * `scale` - Scale vector
    /// * `translation` - Translation vector
    ///
    /// # Returns
    /// A new Transform2D representing the combined transformation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_rotation_scale_translation(
    ///     std::f32::consts::FRAC_PI_4,
    ///     Vector2::new(2.0, 2.0),
    ///     Vector2::new(10.0, 20.0)
    /// );
    /// ```
    #[inline]
    pub fn from_rotation_scale_translation(rotation: f32, scale: Vector2, translation: Vector2) -> Self {
        let (sin, cos) = rotation.sin_cos();
        Self {
            x: Vector2::new(cos * scale.x, sin * scale.x),
            y: Vector2::new(-sin * scale.y, cos * scale.y),
            origin: translation,
        }
    }

    /// ### Creates a Transform2D representing only translation.
    ///
    /// # Arguments
    /// * `translation` - Translation vector
    ///
    /// # Returns
    /// A new Transform2D with only translation applied.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_translation(Vector2::new(10.0, 20.0));
    /// ```
    #[inline]
    pub fn from_translation(translation: Vector2) -> Self {
        Self {
            x: Vector2::new(1.0, 0.0),
            y: Vector2::new(0.0, 1.0),
            origin: translation,
        }
    }

    /// ### Creates a Transform2D representing only rotation.
    ///
    /// # Arguments
    /// * `rotation` - Rotation angle in radians
    ///
    /// # Returns
    /// A new Transform2D with only rotation applied.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_rotation(std::f32::consts::FRAC_PI_4);
    /// ```
    #[inline]
    pub fn from_rotation(rotation: f32) -> Self {
        let (sin, cos) = rotation.sin_cos();
        Self {
            x: Vector2::new(cos, sin),
            y: Vector2::new(-sin, cos),
            origin: Vector2::ZERO,
        }
    }

    /// ### Creates a Transform2D representing only scaling.
    ///
    /// # Arguments
    /// * `scale` - Scale vector
    ///
    /// # Returns
    /// A new Transform2D with only scaling applied.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_scale(Vector2::new(2.0, 3.0));
    /// ```
    #[inline]
    pub fn from_scale(scale: Vector2) -> Self {
        Self {
            x: Vector2::new(scale.x, 0.0),
            y: Vector2::new(0.0, scale.y),
            origin: Vector2::ZERO,
        }
    }

    /// ### Transforms a point by this transformation matrix.
    ///
    /// Applies the full transformation (rotation, scale, and translation) to a point.
    ///
    /// # Arguments
    /// * `point` - The point to transform
    ///
    /// # Returns
    /// The transformed point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_translation(Vector2::new(10.0, 20.0));
    /// let point = Vector2::new(1.0, 2.0);
    /// let transformed = transform.transform_point(point);
    /// assert_eq!(transformed, Vector2::new(11.0, 22.0));
    /// ```
    #[inline]
    pub fn transform_point(self, point: Vector2) -> Vector2 {
        Vector2::new(
            self.x.x * point.x + self.y.x * point.y + self.origin.x,
            self.x.y * point.x + self.y.y * point.y + self.origin.y,
        )
    }

    /// ### Transforms a vector by this transformation matrix.
    ///
    /// Applies only rotation and scale (no translation) to a vector.
    ///
    /// # Arguments
    /// * `vector` - The vector to transform
    ///
    /// # Returns
    /// The transformed vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_scale(Vector2::new(2.0, 3.0));
    /// let vector = Vector2::new(1.0, 1.0);
    /// let transformed = transform.transform_vector(vector);
    /// assert_eq!(transformed, Vector2::new(2.0, 3.0));
    /// ```
    #[inline]
    pub fn transform_vector(self, vector: Vector2) -> Vector2 {
        Vector2::new(
            self.x.x * vector.x + self.y.x * vector.y,
            self.x.y * vector.x + self.y.y * vector.y,
        )
    }

    /// ### Applies an inverse transformation to a point.
    ///
    /// Transforms a point from transformed space back to local space.
    ///
    /// # Arguments
    /// * `point` - The point to inverse transform
    ///
    /// # Returns
    /// The inverse transformed point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_translation(Vector2::new(10.0, 20.0));
    /// let point = Vector2::new(11.0, 22.0);
    /// let original = transform.inverse_transform_point(point);
    /// assert!((original - Vector2::new(1.0, 2.0)).length() < 0.001);
    /// ```
    #[inline]
    pub fn inverse_transform_point(self, point: Vector2) -> Vector2 {
        let relative = point - self.origin;
        let det = self.determinant();
        if det.abs() < 1e-10 {
            return Vector2::ZERO;
        }
        let inv_det = 1.0 / det;
        Vector2::new(
            (self.y.y * relative.x - self.y.x * relative.y) * inv_det,
            (-self.x.y * relative.x + self.x.x * relative.y) * inv_det,
        )
    }

    /// ### Calculates the determinant of the transformation matrix.
    ///
    /// The determinant represents the scaling factor of area transformations.
    /// A determinant of 0 indicates a degenerate transformation.
    ///
    /// # Returns
    /// The determinant of the 2x2 rotation/scale part of the matrix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_scale(Vector2::new(2.0, 3.0));
    /// assert_eq!(transform.determinant(), 6.0);
    /// ```
    #[inline]
    pub fn determinant(self) -> f32 {
        self.x.x * self.y.y - self.x.y * self.y.x
    }

    /// ### Returns the inverse of this transformation.
    ///
    /// Computes the mathematical inverse of the transformation matrix.
    /// Returns identity if the matrix is not invertible (determinant is zero).
    ///
    /// # Returns
    /// The inverse transformation, or identity if not invertible.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_translation(Vector2::new(10.0, 20.0));
    /// let inverse = transform.inverse();
    /// let point = Vector2::new(1.0, 2.0);
    /// let transformed = transform.transform_point(point);
    /// let original = inverse.transform_point(transformed);
    /// assert!((original - point).length() < 0.001);
    /// ```
    #[inline]
    pub fn inverse(self) -> Self {
        let det = self.determinant();
        if det.abs() < 1e-10 {
            return Self::IDENTITY;
        }

        let inv_det = 1.0 / det;
        let inv_x = Vector2::new(self.y.y * inv_det, -self.x.y * inv_det);
        let inv_y = Vector2::new(-self.y.x * inv_det, self.x.x * inv_det);
        let inv_origin = Vector2::new(
            -(inv_x.x * self.origin.x + inv_y.x * self.origin.y),
            -(inv_x.y * self.origin.x + inv_y.y * self.origin.y),
        );

        Self::new(inv_x, inv_y, inv_origin)
    }

    /// ### Multiplies this transformation with another transformation.
    ///
    /// Combines two transformations, applying this transformation first,
    /// then the other transformation.
    ///
    /// # Arguments
    /// * `other` - The transformation to multiply with
    ///
    /// # Returns
    /// The combined transformation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let translate = Transform2D::from_translation(Vector2::new(10.0, 0.0));
    /// let rotate = Transform2D::from_rotation(std::f32::consts::FRAC_PI_2);
    /// let combined = translate.multiply(rotate);
    /// ```
    #[inline]
    pub fn multiply(self, other: Self) -> Self {
        Self::new(
            Vector2::new(
                self.x.x * other.x.x + self.y.x * other.x.y,
                self.x.y * other.x.x + self.y.y * other.x.y,
            ),
            Vector2::new(
                self.x.x * other.y.x + self.y.x * other.y.y,
                self.x.y * other.y.x + self.y.y * other.y.y,
            ),
            Vector2::new(
                self.x.x * other.origin.x + self.y.x * other.origin.y + self.origin.x,
                self.x.y * other.origin.x + self.y.y * other.origin.y + self.origin.y,
            ),
        )
    }

    /// ### Extracts the rotation angle from the transformation.
    ///
    /// Returns the rotation component of the transformation in radians.
    /// This assumes the transformation contains uniform scaling.
    ///
    /// # Returns
    /// The rotation angle in radians.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_rotation(std::f32::consts::FRAC_PI_4);
    /// let rotation = transform.get_rotation();
    /// assert!((rotation - std::f32::consts::FRAC_PI_4).abs() < 0.001);
    /// ```
    #[inline]
    pub fn get_rotation(self) -> f32 {
        self.x.y.atan2(self.x.x)
    }

    /// ### Extracts the scale from the transformation.
    ///
    /// Returns the scale factors along each axis.
    ///
    /// # Returns
    /// A Vector2 containing the scale factors.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_scale(Vector2::new(2.0, 3.0));
    /// let scale = transform.get_scale();
    /// assert!((scale - Vector2::new(2.0, 3.0)).length() < 0.001);
    /// ```
    #[inline]
    pub fn get_scale(self) -> Vector2 {
        Vector2::new(self.x.length(), self.y.length())
    }

    /// ### Extracts the translation from the transformation.
    ///
    /// Returns the translation component of the transformation.
    ///
    /// # Returns
    /// The translation vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::from_translation(Vector2::new(10.0, 20.0));
    /// let translation = transform.get_translation();
    /// assert_eq!(translation, Vector2::new(10.0, 20.0));
    /// ```
    #[inline]
    pub fn get_translation(self) -> Vector2 {
        self.origin
    }

    /// ### Checks if this transformation is approximately equal to another.
    ///
    /// Uses epsilon comparison for floating-point components.
    ///
    /// # Arguments
    /// * `other` - The other transformation to compare with
    ///
    /// # Returns
    /// `true` if the transformations are approximately equal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let t1 = Transform2D::from_translation(Vector2::new(1.0, 2.0));
    /// let t2 = Transform2D::from_translation(Vector2::new(1.0000001, 2.0000001));
    /// assert!(t1.is_equal_approx(t2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Self) -> bool {
        self.x.is_equal_approx(other.x)
            && self.y.is_equal_approx(other.y)
            && self.origin.is_equal_approx(other.origin)
    }

    /// ### Checks if this transformation is finite.
    ///
    /// Returns `true` if all components are finite (not NaN or infinite).
    ///
    /// # Returns
    /// `true` if the transformation is finite.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let transform = Transform2D::IDENTITY;
    /// assert!(transform.is_finite());
    /// ```
    #[inline]
    pub fn is_finite(self) -> bool {
        self.x.is_finite() && self.y.is_finite() && self.origin.is_finite()
    }

    /// ### Interpolates between two transformations.
    ///
    /// Performs linear interpolation between this transformation and another.
    ///
    /// # Arguments
    /// * `other` - The target transformation
    /// * `t` - Interpolation factor (0.0 = this, 1.0 = other)
    ///
    /// # Returns
    /// The interpolated transformation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Transform2D, Vector2};
    /// let t1 = Transform2D::from_translation(Vector2::new(0.0, 0.0));
    /// let t2 = Transform2D::from_translation(Vector2::new(10.0, 20.0));
    /// let mid = t1.lerp(t2, 0.5);
    /// assert_eq!(mid.get_translation(), Vector2::new(5.0, 10.0));
    /// ```
    #[inline]
    pub fn lerp(self, other: Self, t: f32) -> Self {
        Self::new(
            self.x.lerp(other.x, t),
            self.y.lerp(other.y, t),
            self.origin.lerp(other.origin, t),
        )
    }
}

impl fmt::Display for Transform2D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "[{}, {}, {}]",
            self.x, self.y, self.origin
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_transform2d_creation() {
        let transform = Transform2D::new(
            Vector2::new(1.0, 0.0),
            Vector2::new(0.0, 1.0),
            Vector2::new(10.0, 20.0),
        );
        assert_eq!(transform.x, Vector2::new(1.0, 0.0));
        assert_eq!(transform.y, Vector2::new(0.0, 1.0));
        assert_eq!(transform.origin, Vector2::new(10.0, 20.0));
    }

    #[test]
    fn test_transform2d_constants() {
        assert_eq!(Transform2D::IDENTITY.x, Vector2::new(1.0, 0.0));
        assert_eq!(Transform2D::IDENTITY.y, Vector2::new(0.0, 1.0));
        assert_eq!(Transform2D::IDENTITY.origin, Vector2::ZERO);

        assert_eq!(Transform2D::FLIP_X.x, Vector2::new(-1.0, 0.0));
        assert_eq!(Transform2D::FLIP_Y.y, Vector2::new(0.0, -1.0));
    }

    #[test]
    fn test_transform2d_from_translation() {
        let transform = Transform2D::from_translation(Vector2::new(10.0, 20.0));
        assert_eq!(transform.origin, Vector2::new(10.0, 20.0));
        assert_eq!(transform.x, Vector2::new(1.0, 0.0));
        assert_eq!(transform.y, Vector2::new(0.0, 1.0));
    }

    #[test]
    fn test_transform2d_from_rotation() {
        let transform = Transform2D::from_rotation(std::f32::consts::FRAC_PI_2);
        let expected_x = Vector2::new(0.0, 1.0);
        let expected_y = Vector2::new(-1.0, 0.0);
        assert!((transform.x - expected_x).length() < 0.001);
        assert!((transform.y - expected_y).length() < 0.001);
        assert_eq!(transform.origin, Vector2::ZERO);
    }

    #[test]
    fn test_transform2d_from_scale() {
        let transform = Transform2D::from_scale(Vector2::new(2.0, 3.0));
        assert_eq!(transform.x, Vector2::new(2.0, 0.0));
        assert_eq!(transform.y, Vector2::new(0.0, 3.0));
        assert_eq!(transform.origin, Vector2::ZERO);
    }

    #[test]
    fn test_transform2d_transform_point() {
        let transform = Transform2D::from_translation(Vector2::new(10.0, 20.0));
        let point = Vector2::new(1.0, 2.0);
        let transformed = transform.transform_point(point);
        assert_eq!(transformed, Vector2::new(11.0, 22.0));
    }

    #[test]
    fn test_transform2d_transform_vector() {
        let transform = Transform2D::from_scale(Vector2::new(2.0, 3.0));
        let vector = Vector2::new(1.0, 1.0);
        let transformed = transform.transform_vector(vector);
        assert_eq!(transformed, Vector2::new(2.0, 3.0));
    }

    #[test]
    fn test_transform2d_determinant() {
        let transform = Transform2D::from_scale(Vector2::new(2.0, 3.0));
        assert_eq!(transform.determinant(), 6.0);

        let identity = Transform2D::IDENTITY;
        assert_eq!(identity.determinant(), 1.0);
    }

    #[test]
    fn test_transform2d_inverse() {
        let transform = Transform2D::from_translation(Vector2::new(10.0, 20.0));
        let inverse = transform.inverse();
        let point = Vector2::new(1.0, 2.0);
        let transformed = transform.transform_point(point);
        let original = inverse.transform_point(transformed);
        assert!((original - point).length() < 0.001);
    }

    #[test]
    fn test_transform2d_multiply() {
        let translate = Transform2D::from_translation(Vector2::new(10.0, 0.0));
        let scale = Transform2D::from_scale(Vector2::new(2.0, 2.0));
        let combined = translate.multiply(scale);

        let point = Vector2::new(1.0, 1.0);

        // Matrix multiplication A.multiply(B) applies B first, then A
        // So translate.multiply(scale) applies scale first, then translate
        let manual_result = translate.transform_point(scale.transform_point(point));
        let combined_result = combined.transform_point(point);

        assert!((manual_result - combined_result).length() < 0.001);
    }

    #[test]
    fn test_transform2d_property_extraction() {
        let rotation = std::f32::consts::FRAC_PI_4;
        let scale = Vector2::new(2.0, 3.0);
        let translation = Vector2::new(10.0, 20.0);

        let transform = Transform2D::from_rotation_scale_translation(rotation, scale, translation);

        assert!((transform.get_rotation() - rotation).abs() < 0.001);
        assert!((transform.get_scale() - scale).length() < 0.001);
        assert_eq!(transform.get_translation(), translation);
    }

    #[test]
    fn test_transform2d_utility_methods() {
        let t1 = Transform2D::from_translation(Vector2::new(1.0, 2.0));
        let t2 = Transform2D::from_translation(Vector2::new(1.0000001, 2.0000001));
        assert!(t1.is_equal_approx(t2));

        assert!(Transform2D::IDENTITY.is_finite());

        let t3 = Transform2D::from_translation(Vector2::new(0.0, 0.0));
        let t4 = Transform2D::from_translation(Vector2::new(10.0, 20.0));
        let mid = t3.lerp(t4, 0.5);
        assert_eq!(mid.get_translation(), Vector2::new(5.0, 10.0));
    }
}
