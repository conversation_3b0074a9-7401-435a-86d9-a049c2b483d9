//! Comprehensive 4x4 projection matrix implementation for 3D rendering and graphics pipelines.
//!
//! This module provides a complete Projection implementation representing 4x4 projection matrices
//! used in 3D graphics for perspective and orthogonal projections, view frustum calculations,
//! and coordinate transformations commonly used in rendering pipelines and 3D graphics engines.

use std::fmt;
use super::{Vector3, Vector4, AABB, Plane};

/// ### A 4x4 projection matrix for 3D rendering and graphics transformations.
///
/// Projection represents a 4x4 matrix used for projecting 3D coordinates to 2D screen space,
/// defining view frustums, and performing coordinate transformations in 3D graphics pipelines.
/// It supports both perspective and orthogonal projections with comprehensive matrix operations.
///
/// ## Mathematical Representation
///
/// A projection matrix is a 4x4 matrix stored in column-major order:
/// ```
/// | x.x  y.x  z.x  w.x |
/// | x.y  y.y  z.y  w.y |
/// | x.z  y.z  z.z  w.z |
/// | x.w  y.w  z.w  w.w |
/// ```
///
/// ## Use Cases
///
/// Projection is ideal for:
/// - **3D Rendering**: Perspective and orthogonal projection matrices
/// - **Camera Systems**: View frustum definition, field of view calculations
/// - **Graphics Pipelines**: Vertex transformation, clipping space conversion
/// - **Spatial Transformations**: 3D to 2D coordinate mapping
/// - **Frustum Culling**: View frustum plane extraction for optimization
/// - **Ray Casting**: Screen-to-world coordinate transformation
///
/// # Examples
/// ```
/// # use verturion::core::math::{Projection, Vector3, Vector4};
/// // Create perspective projection
/// let perspective = Projection::perspective(
///     75.0_f32.to_radians(), // Field of view
///     16.0 / 9.0,           // Aspect ratio
///     0.1,                  // Near plane
///     1000.0                // Far plane
/// );
///
/// // Create orthogonal projection
/// let orthogonal = Projection::orthogonal(
///     -10.0, 10.0,  // Left, right
///     -10.0, 10.0,  // Bottom, top
///     0.1, 100.0    // Near, far
/// );
///
/// // Transform a point
/// let point = Vector4::new(1.0, 2.0, 3.0, 1.0);
/// let projected = perspective * point;
/// ```
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct Projection {
    /// The first column vector (x-axis)
    pub x: Vector4,
    /// The second column vector (y-axis)
    pub y: Vector4,
    /// The third column vector (z-axis)
    pub z: Vector4,
    /// The fourth column vector (translation/projection)
    pub w: Vector4,
}

impl Projection {
    /// ### Creates a new projection matrix from four column vectors.
    ///
    /// # Parameters
    /// - `x`: The first column vector
    /// - `y`: The second column vector
    /// - `z`: The third column vector
    /// - `w`: The fourth column vector
    ///
    /// # Returns
    /// A new projection matrix with the given column vectors.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Projection, Vector4};
    /// let proj = Projection::new(
    ///     Vector4::new(1.0, 0.0, 0.0, 0.0),
    ///     Vector4::new(0.0, 1.0, 0.0, 0.0),
    ///     Vector4::new(0.0, 0.0, 1.0, 0.0),
    ///     Vector4::new(0.0, 0.0, 0.0, 1.0)
    /// );
    /// ```
    #[inline]
    pub const fn new(x: Vector4, y: Vector4, z: Vector4, w: Vector4) -> Self {
        Self { x, y, z, w }
    }

    /// ### Creates a perspective projection matrix.
    ///
    /// Creates a perspective projection matrix suitable for 3D rendering with
    /// realistic depth perception and foreshortening effects.
    ///
    /// # Parameters
    /// - `fov_y`: Field of view angle in radians (vertical)
    /// - `aspect`: Aspect ratio (width / height)
    /// - `near`: Distance to near clipping plane (must be positive)
    /// - `far`: Distance to far clipping plane (must be greater than near)
    ///
    /// # Returns
    /// A perspective projection matrix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Projection;
    /// let perspective = Projection::perspective(
    ///     75.0_f32.to_radians(), // 75 degrees FOV
    ///     16.0 / 9.0,           // 16:9 aspect ratio
    ///     0.1,                  // Near plane
    ///     1000.0                // Far plane
    /// );
    /// ```
    #[inline]
    pub fn perspective(fov_y: f32, aspect: f32, near: f32, far: f32) -> Self {
        let f = 1.0 / (fov_y * 0.5).tan();
        let range_inv = 1.0 / (near - far);

        Self::new(
            Vector4::new(f / aspect, 0.0, 0.0, 0.0),
            Vector4::new(0.0, f, 0.0, 0.0),
            Vector4::new(0.0, 0.0, (far + near) * range_inv, -1.0),
            Vector4::new(0.0, 0.0, 2.0 * far * near * range_inv, 0.0),
        )
    }

    /// ### Creates an orthogonal projection matrix.
    ///
    /// Creates an orthogonal projection matrix suitable for 2D rendering,
    /// technical drawings, or isometric 3D views without perspective distortion.
    ///
    /// # Parameters
    /// - `left`: Left edge of the view volume
    /// - `right`: Right edge of the view volume
    /// - `bottom`: Bottom edge of the view volume
    /// - `top`: Top edge of the view volume
    /// - `near`: Distance to near clipping plane
    /// - `far`: Distance to far clipping plane
    ///
    /// # Returns
    /// An orthogonal projection matrix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Projection;
    /// let orthogonal = Projection::orthogonal(
    ///     -100.0, 100.0,  // Left, right
    ///     -75.0, 75.0,    // Bottom, top
    ///     0.1, 1000.0     // Near, far
    /// );
    /// ```
    #[inline]
    pub fn orthogonal(left: f32, right: f32, bottom: f32, top: f32, near: f32, far: f32) -> Self {
        let width_inv = 1.0 / (right - left);
        let height_inv = 1.0 / (top - bottom);
        let depth_inv = 1.0 / (far - near);

        Self::new(
            Vector4::new(2.0 * width_inv, 0.0, 0.0, 0.0),
            Vector4::new(0.0, 2.0 * height_inv, 0.0, 0.0),
            Vector4::new(0.0, 0.0, -2.0 * depth_inv, 0.0),
            Vector4::new(
                -(right + left) * width_inv,
                -(top + bottom) * height_inv,
                -(far + near) * depth_inv,
                1.0,
            ),
        )
    }

    /// ### Creates a frustum projection matrix.
    ///
    /// Creates a perspective projection matrix from explicit frustum bounds,
    /// providing fine control over the projection parameters.
    ///
    /// # Parameters
    /// - `left`: Left edge of the near plane
    /// - `right`: Right edge of the near plane
    /// - `bottom`: Bottom edge of the near plane
    /// - `top`: Top edge of the near plane
    /// - `near`: Distance to near clipping plane (must be positive)
    /// - `far`: Distance to far clipping plane (must be greater than near)
    ///
    /// # Returns
    /// A frustum projection matrix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Projection;
    /// let frustum = Projection::frustum(
    ///     -1.0, 1.0,    // Left, right
    ///     -0.75, 0.75,  // Bottom, top
    ///     1.0, 100.0    // Near, far
    /// );
    /// ```
    #[inline]
    pub fn frustum(left: f32, right: f32, bottom: f32, top: f32, near: f32, far: f32) -> Self {
        let width_inv = 1.0 / (right - left);
        let height_inv = 1.0 / (top - bottom);
        let depth_inv = 1.0 / (far - near);

        Self::new(
            Vector4::new(2.0 * near * width_inv, 0.0, 0.0, 0.0),
            Vector4::new(0.0, 2.0 * near * height_inv, 0.0, 0.0),
            Vector4::new(
                (right + left) * width_inv,
                (top + bottom) * height_inv,
                -(far + near) * depth_inv,
                -1.0,
            ),
            Vector4::new(0.0, 0.0, -2.0 * far * near * depth_inv, 0.0),
        )
    }

    /// ### Returns the identity projection matrix.
    ///
    /// # Returns
    /// The 4x4 identity matrix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Projection;
    /// let identity = Projection::identity();
    /// ```
    #[inline]
    pub fn identity() -> Self {
        Self::new(
            Vector4::new(1.0, 0.0, 0.0, 0.0),
            Vector4::new(0.0, 1.0, 0.0, 0.0),
            Vector4::new(0.0, 0.0, 1.0, 0.0),
            Vector4::new(0.0, 0.0, 0.0, 1.0),
        )
    }

    /// ### Transforms a 4D vector by this projection matrix.
    ///
    /// Multiplies the projection matrix by a 4D vector, applying the full
    /// transformation including perspective division if needed.
    ///
    /// # Parameters
    /// - `vector`: The 4D vector to transform
    ///
    /// # Returns
    /// The transformed 4D vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Projection, Vector4};
    /// let proj = Projection::identity();
    /// let vector = Vector4::new(1.0, 2.0, 3.0, 1.0);
    /// let transformed = proj.transform_vector4(vector);
    /// ```
    #[inline]
    pub fn transform_vector4(self, vector: Vector4) -> Vector4 {
        Vector4::new(
            self.x.x * vector.x + self.y.x * vector.y + self.z.x * vector.z + self.w.x * vector.w,
            self.x.y * vector.x + self.y.y * vector.y + self.z.y * vector.z + self.w.y * vector.w,
            self.x.z * vector.x + self.y.z * vector.y + self.z.z * vector.z + self.w.z * vector.w,
            self.x.w * vector.x + self.y.w * vector.y + self.z.w * vector.z + self.w.w * vector.w,
        )
    }

    /// ### Transforms a 3D point by this projection matrix.
    ///
    /// Transforms a 3D point using homogeneous coordinates (w=1) and
    /// returns the result after perspective division.
    ///
    /// # Parameters
    /// - `point`: The 3D point to transform
    ///
    /// # Returns
    /// The transformed 3D point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Projection, Vector3};
    /// let proj = Projection::identity();
    /// let point = Vector3::new(1.0, 2.0, 3.0);
    /// let transformed = proj.transform_point(point);
    /// ```
    #[inline]
    pub fn transform_point(self, point: Vector3) -> Vector3 {
        let homogeneous = Vector4::new(point.x, point.y, point.z, 1.0);
        let transformed = self.transform_vector4(homogeneous);

        if transformed.w.abs() > f32::EPSILON {
            Vector3::new(
                transformed.x / transformed.w,
                transformed.y / transformed.w,
                transformed.z / transformed.w,
            )
        } else {
            Vector3::new(transformed.x, transformed.y, transformed.z)
        }
    }

    /// ### Calculates the determinant of the projection matrix.
    ///
    /// The determinant indicates whether the matrix is invertible and
    /// can be used to detect degenerate transformations.
    ///
    /// # Returns
    /// The determinant of the 4x4 matrix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Projection;
    /// let identity = Projection::identity();
    /// assert_eq!(identity.determinant(), 1.0);
    /// ```
    #[inline]
    pub fn determinant(self) -> f32 {
        let a = self.x.x * (
            self.y.y * (self.z.z * self.w.w - self.z.w * self.w.z) -
            self.y.z * (self.z.y * self.w.w - self.z.w * self.w.y) +
            self.y.w * (self.z.y * self.w.z - self.z.z * self.w.y)
        );

        let b = self.x.y * (
            self.y.x * (self.z.z * self.w.w - self.z.w * self.w.z) -
            self.y.z * (self.z.x * self.w.w - self.z.w * self.w.x) +
            self.y.w * (self.z.x * self.w.z - self.z.z * self.w.x)
        );

        let c = self.x.z * (
            self.y.x * (self.z.y * self.w.w - self.z.w * self.w.y) -
            self.y.y * (self.z.x * self.w.w - self.z.w * self.w.x) +
            self.y.w * (self.z.x * self.w.y - self.z.y * self.w.x)
        );

        let d = self.x.w * (
            self.y.x * (self.z.y * self.w.z - self.z.z * self.w.y) -
            self.y.y * (self.z.x * self.w.z - self.z.z * self.w.x) +
            self.y.z * (self.z.x * self.w.y - self.z.y * self.w.x)
        );

        a - b + c - d
    }

    /// ### Returns the inverse of the projection matrix.
    ///
    /// Computes the matrix inverse using Gaussian elimination.
    /// Returns the original matrix if it's not invertible (determinant is zero).
    ///
    /// # Returns
    /// The inverse projection matrix, or the original if not invertible.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Projection;
    /// let identity = Projection::identity();
    /// let inverse = identity.inverse();
    /// assert_eq!(inverse, identity);
    /// ```
    #[inline]
    pub fn inverse(self) -> Self {
        let det = self.determinant();
        if det.abs() < f32::EPSILON {
            return self;
        }

        let inv_det = 1.0 / det;

        // Calculate cofactor matrix elements
        let m00 = (self.y.y * (self.z.z * self.w.w - self.z.w * self.w.z) -
                   self.y.z * (self.z.y * self.w.w - self.z.w * self.w.y) +
                   self.y.w * (self.z.y * self.w.z - self.z.z * self.w.y)) * inv_det;

        let m01 = -(self.x.y * (self.z.z * self.w.w - self.z.w * self.w.z) -
                    self.x.z * (self.z.y * self.w.w - self.z.w * self.w.y) +
                    self.x.w * (self.z.y * self.w.z - self.z.z * self.w.y)) * inv_det;

        let m02 = (self.x.y * (self.y.z * self.w.w - self.y.w * self.w.z) -
                   self.x.z * (self.y.y * self.w.w - self.y.w * self.w.y) +
                   self.x.w * (self.y.y * self.w.z - self.y.z * self.w.y)) * inv_det;

        let m03 = -(self.x.y * (self.y.z * self.z.w - self.y.w * self.z.z) -
                    self.x.z * (self.y.y * self.z.w - self.y.w * self.z.y) +
                    self.x.w * (self.y.y * self.z.z - self.y.z * self.z.y)) * inv_det;

        let m10 = -(self.y.x * (self.z.z * self.w.w - self.z.w * self.w.z) -
                    self.y.z * (self.z.x * self.w.w - self.z.w * self.w.x) +
                    self.y.w * (self.z.x * self.w.z - self.z.z * self.w.x)) * inv_det;

        let m11 = (self.x.x * (self.z.z * self.w.w - self.z.w * self.w.z) -
                   self.x.z * (self.z.x * self.w.w - self.z.w * self.w.x) +
                   self.x.w * (self.z.x * self.w.z - self.z.z * self.w.x)) * inv_det;

        let m12 = -(self.x.x * (self.y.z * self.w.w - self.y.w * self.w.z) -
                    self.x.z * (self.y.x * self.w.w - self.y.w * self.w.x) +
                    self.x.w * (self.y.x * self.w.z - self.y.z * self.w.x)) * inv_det;

        let m13 = (self.x.x * (self.y.z * self.z.w - self.y.w * self.z.z) -
                   self.x.z * (self.y.x * self.z.w - self.y.w * self.z.x) +
                   self.x.w * (self.y.x * self.z.z - self.y.z * self.z.x)) * inv_det;

        let m20 = (self.y.x * (self.z.y * self.w.w - self.z.w * self.w.y) -
                   self.y.y * (self.z.x * self.w.w - self.z.w * self.w.x) +
                   self.y.w * (self.z.x * self.w.y - self.z.y * self.w.x)) * inv_det;

        let m21 = -(self.x.x * (self.z.y * self.w.w - self.z.w * self.w.y) -
                    self.x.y * (self.z.x * self.w.w - self.z.w * self.w.x) +
                    self.x.w * (self.z.x * self.w.y - self.z.y * self.w.x)) * inv_det;

        let m22 = (self.x.x * (self.y.y * self.w.w - self.y.w * self.w.y) -
                   self.x.y * (self.y.x * self.w.w - self.y.w * self.w.x) +
                   self.x.w * (self.y.x * self.w.y - self.y.y * self.w.x)) * inv_det;

        let m23 = -(self.x.x * (self.y.y * self.z.w - self.y.w * self.z.y) -
                    self.x.y * (self.y.x * self.z.w - self.y.w * self.z.x) +
                    self.x.w * (self.y.x * self.z.y - self.y.y * self.z.x)) * inv_det;

        let m30 = -(self.y.x * (self.z.y * self.w.z - self.z.z * self.w.y) -
                    self.y.y * (self.z.x * self.w.z - self.z.z * self.w.x) +
                    self.y.z * (self.z.x * self.w.y - self.z.y * self.w.x)) * inv_det;

        let m31 = (self.x.x * (self.z.y * self.w.z - self.z.z * self.w.y) -
                   self.x.y * (self.z.x * self.w.z - self.z.z * self.w.x) +
                   self.x.z * (self.z.x * self.w.y - self.z.y * self.w.x)) * inv_det;

        let m32 = -(self.x.x * (self.y.y * self.w.z - self.y.z * self.w.y) -
                    self.x.y * (self.y.x * self.w.z - self.y.z * self.w.x) +
                    self.x.z * (self.y.x * self.w.y - self.y.y * self.w.x)) * inv_det;

        let m33 = (self.x.x * (self.y.y * self.z.z - self.y.z * self.z.y) -
                   self.x.y * (self.y.x * self.z.z - self.y.z * self.z.x) +
                   self.x.z * (self.y.x * self.z.y - self.y.y * self.z.x)) * inv_det;

        Self::new(
            Vector4::new(m00, m01, m02, m03),
            Vector4::new(m10, m11, m12, m13),
            Vector4::new(m20, m21, m22, m23),
            Vector4::new(m30, m31, m32, m33),
        )
    }

    /// ### Extracts the six frustum planes from the projection matrix.
    ///
    /// Returns the six clipping planes (left, right, bottom, top, near, far)
    /// that define the view frustum for culling operations.
    ///
    /// # Returns
    /// An array of six planes: [left, right, bottom, top, near, far].
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Projection;
    /// let perspective = Projection::perspective(
    ///     75.0_f32.to_radians(), 16.0/9.0, 0.1, 1000.0
    /// );
    /// let planes = perspective.get_frustum_planes();
    /// ```
    #[inline]
    pub fn get_frustum_planes(self) -> [Plane; 6] {
        // Extract planes using the standard method from projection matrix
        // Note: We need to normalize the plane equations properly
        let left_normal = Vector3::new(
            self.w.x + self.x.x,
            self.w.y + self.x.y,
            self.w.z + self.x.z,
        );
        let left_d = self.w.w + self.x.w;
        let left_length = left_normal.length();
        let left = if left_length > f32::EPSILON {
            Plane::new(left_normal / left_length, left_d / left_length)
        } else {
            Plane::new(left_normal, left_d)
        };

        let right_normal = Vector3::new(
            self.w.x - self.x.x,
            self.w.y - self.x.y,
            self.w.z - self.x.z,
        );
        let right_d = self.w.w - self.x.w;
        let right_length = right_normal.length();
        let right = if right_length > f32::EPSILON {
            Plane::new(right_normal / right_length, right_d / right_length)
        } else {
            Plane::new(right_normal, right_d)
        };

        let bottom_normal = Vector3::new(
            self.w.x + self.y.x,
            self.w.y + self.y.y,
            self.w.z + self.y.z,
        );
        let bottom_d = self.w.w + self.y.w;
        let bottom_length = bottom_normal.length();
        let bottom = if bottom_length > f32::EPSILON {
            Plane::new(bottom_normal / bottom_length, bottom_d / bottom_length)
        } else {
            Plane::new(bottom_normal, bottom_d)
        };

        let top_normal = Vector3::new(
            self.w.x - self.y.x,
            self.w.y - self.y.y,
            self.w.z - self.y.z,
        );
        let top_d = self.w.w - self.y.w;
        let top_length = top_normal.length();
        let top = if top_length > f32::EPSILON {
            Plane::new(top_normal / top_length, top_d / top_length)
        } else {
            Plane::new(top_normal, top_d)
        };

        let near_normal = Vector3::new(
            self.w.x + self.z.x,
            self.w.y + self.z.y,
            self.w.z + self.z.z,
        );
        let near_d = self.w.w + self.z.w;
        let near_length = near_normal.length();
        let near = if near_length > f32::EPSILON {
            Plane::new(near_normal / near_length, near_d / near_length)
        } else {
            Plane::new(near_normal, near_d)
        };

        let far_normal = Vector3::new(
            self.w.x - self.z.x,
            self.w.y - self.z.y,
            self.w.z - self.z.z,
        );
        let far_d = self.w.w - self.z.w;
        let far_length = far_normal.length();
        let far = if far_length > f32::EPSILON {
            Plane::new(far_normal / far_length, far_d / far_length)
        } else {
            Plane::new(far_normal, far_d)
        };

        [left, right, bottom, top, near, far]
    }

    /// ### Checks if a point is inside the view frustum.
    ///
    /// Tests whether a 3D point is within the view frustum defined by this projection matrix.
    /// Useful for frustum culling optimizations.
    ///
    /// # Parameters
    /// - `point`: The 3D point to test
    ///
    /// # Returns
    /// `true` if the point is inside the frustum.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Projection, Vector3};
    /// let perspective = Projection::perspective(
    ///     75.0_f32.to_radians(), 16.0/9.0, 0.1, 1000.0
    /// );
    /// let point = Vector3::new(0.0, 0.0, -5.0);
    /// let inside = perspective.is_point_in_frustum(point);
    /// ```
    #[inline]
    pub fn is_point_in_frustum(self, point: Vector3) -> bool {
        let planes = self.get_frustum_planes();
        planes.iter().all(|plane| plane.distance_to(point) >= 0.0)
    }

    /// ### Checks if an AABB intersects with the view frustum.
    ///
    /// Tests whether an axis-aligned bounding box intersects with the view frustum
    /// defined by this projection matrix. Essential for frustum culling.
    ///
    /// # Parameters
    /// - `aabb`: The axis-aligned bounding box to test
    ///
    /// # Returns
    /// `true` if the AABB intersects the frustum.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Projection, AABB, Vector3};
    /// let perspective = Projection::perspective(
    ///     75.0_f32.to_radians(), 16.0/9.0, 0.1, 1000.0
    /// );
    /// let bbox = AABB::new(-1.0, -1.0, -10.0, 2.0, 2.0, 5.0);
    /// let intersects = perspective.is_aabb_in_frustum(bbox);
    /// ```
    #[inline]
    pub fn is_aabb_in_frustum(self, aabb: AABB) -> bool {
        let planes = self.get_frustum_planes();
        planes.iter().all(|plane| aabb.intersects_plane(*plane))
    }

    /// ### Projects a 3D point to normalized device coordinates.
    ///
    /// Transforms a 3D point to normalized device coordinates (NDC) where
    /// x, y, and z are in the range [-1, 1] for points inside the frustum.
    ///
    /// # Parameters
    /// - `point`: The 3D point to project
    ///
    /// # Returns
    /// The point in normalized device coordinates.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Projection, Vector3};
    /// let perspective = Projection::perspective(
    ///     75.0_f32.to_radians(), 16.0/9.0, 0.1, 1000.0
    /// );
    /// let point = Vector3::new(0.0, 0.0, -1.0);
    /// let ndc = perspective.project_to_ndc(point);
    /// ```
    #[inline]
    pub fn project_to_ndc(self, point: Vector3) -> Vector3 {
        self.transform_point(point)
    }

    /// ### Unprojects a point from normalized device coordinates to world space.
    ///
    /// Transforms a point from normalized device coordinates back to world space
    /// using the inverse of this projection matrix.
    ///
    /// # Parameters
    /// - `ndc_point`: The point in normalized device coordinates
    ///
    /// # Returns
    /// The point in world space coordinates.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Projection, Vector3};
    /// let perspective = Projection::perspective(
    ///     75.0_f32.to_radians(), 16.0/9.0, 0.1, 1000.0
    /// );
    /// let ndc = Vector3::new(0.0, 0.0, 0.0);
    /// let world_point = perspective.unproject_from_ndc(ndc);
    /// ```
    #[inline]
    pub fn unproject_from_ndc(self, ndc_point: Vector3) -> Vector3 {
        let inverse = self.inverse();
        inverse.transform_point(ndc_point)
    }

    /// ### Returns the transpose of the projection matrix.
    ///
    /// Swaps rows and columns of the matrix. Useful for certain mathematical operations.
    ///
    /// # Returns
    /// The transposed projection matrix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Projection;
    /// let proj = Projection::identity();
    /// let transposed = proj.transpose();
    /// ```
    #[inline]
    pub fn transpose(self) -> Self {
        Self::new(
            Vector4::new(self.x.x, self.y.x, self.z.x, self.w.x),
            Vector4::new(self.x.y, self.y.y, self.z.y, self.w.y),
            Vector4::new(self.x.z, self.y.z, self.z.z, self.w.z),
            Vector4::new(self.x.w, self.y.w, self.z.w, self.w.w),
        )
    }

    /// ### Checks if the projection matrix is approximately equal to another.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    ///
    /// # Parameters
    /// - `other`: The other projection matrix to compare with
    ///
    /// # Returns
    /// `true` if the matrices are approximately equal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Projection;
    /// let proj1 = Projection::identity();
    /// let proj2 = Projection::identity();
    /// assert!(proj1.is_equal_approx(proj2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Self) -> bool {
        self.x.is_equal_approx(other.x)
            && self.y.is_equal_approx(other.y)
            && self.z.is_equal_approx(other.z)
            && self.w.is_equal_approx(other.w)
    }
}

impl Default for Projection {
    /// Returns the identity projection matrix.
    #[inline]
    fn default() -> Self {
        Self::identity()
    }
}

impl fmt::Display for Projection {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "Projection(\n  [{}, {}, {}, {}],\n  [{}, {}, {}, {}],\n  [{}, {}, {}, {}],\n  [{}, {}, {}, {}]\n)",
            self.x.x, self.y.x, self.z.x, self.w.x,
            self.x.y, self.y.y, self.z.y, self.w.y,
            self.x.z, self.y.z, self.z.z, self.w.z,
            self.x.w, self.y.w, self.z.w, self.w.w
        )
    }
}

// Matrix multiplication operator
impl std::ops::Mul<Projection> for Projection {
    type Output = Projection;

    /// Multiplies two projection matrices.
    /// The result represents applying the first transformation, then the second.
    #[inline]
    fn mul(self, other: Projection) -> Self::Output {
        Projection::new(
            self.transform_vector4(other.x),
            self.transform_vector4(other.y),
            self.transform_vector4(other.z),
            self.transform_vector4(other.w),
        )
    }
}

// Vector transformation operators
impl std::ops::Mul<Vector4> for Projection {
    type Output = Vector4;

    /// Transforms a 4D vector by the projection matrix.
    #[inline]
    fn mul(self, vector: Vector4) -> Self::Output {
        self.transform_vector4(vector)
    }
}

impl std::ops::Mul<Vector3> for Projection {
    type Output = Vector3;

    /// Transforms a 3D point by the projection matrix.
    #[inline]
    fn mul(self, point: Vector3) -> Self::Output {
        self.transform_point(point)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_projection_creation() {
        let proj = Projection::new(
            Vector4::new(1.0, 0.0, 0.0, 0.0),
            Vector4::new(0.0, 1.0, 0.0, 0.0),
            Vector4::new(0.0, 0.0, 1.0, 0.0),
            Vector4::new(0.0, 0.0, 0.0, 1.0),
        );
        assert_eq!(proj.x, Vector4::new(1.0, 0.0, 0.0, 0.0));
        assert_eq!(proj.y, Vector4::new(0.0, 1.0, 0.0, 0.0));
        assert_eq!(proj.z, Vector4::new(0.0, 0.0, 1.0, 0.0));
        assert_eq!(proj.w, Vector4::new(0.0, 0.0, 0.0, 1.0));

        let identity = Projection::identity();
        assert_eq!(identity, proj);

        let default = Projection::default();
        assert_eq!(default, identity);
    }

    #[test]
    fn test_projection_perspective() {
        let fov = 75.0_f32.to_radians();
        let aspect = 16.0 / 9.0;
        let near = 0.1;
        let far = 1000.0;

        let perspective = Projection::perspective(fov, aspect, near, far);

        // Test that the matrix has the expected structure for perspective projection
        assert!(perspective.x.x > 0.0); // X scaling
        assert!(perspective.y.y > 0.0); // Y scaling
        assert!(perspective.z.z < 0.0); // Z mapping (negative for right-handed)
        assert_eq!(perspective.z.w, -1.0); // Perspective division

        // Test field of view calculation
        let expected_f = 1.0 / (fov * 0.5).tan();
        assert!((perspective.y.y - expected_f).abs() < 0.001);
        assert!((perspective.x.x - expected_f / aspect).abs() < 0.001);
    }

    #[test]
    fn test_projection_orthogonal() {
        let left = -10.0;
        let right = 10.0;
        let bottom = -7.5;
        let top = 7.5;
        let near = 0.1;
        let far = 100.0;

        let orthogonal = Projection::orthogonal(left, right, bottom, top, near, far);

        // Test that the matrix has the expected structure for orthogonal projection
        assert!(orthogonal.x.x > 0.0); // X scaling
        assert!(orthogonal.y.y > 0.0); // Y scaling
        assert!(orthogonal.z.z < 0.0); // Z mapping (negative)
        assert_eq!(orthogonal.z.w, 0.0); // No perspective division
        assert_eq!(orthogonal.w.w, 1.0); // Homogeneous coordinate

        // Test scaling calculations
        let width_inv = 1.0 / (right - left);
        let height_inv = 1.0 / (top - bottom);
        assert!((orthogonal.x.x - 2.0 * width_inv).abs() < 0.001);
        assert!((orthogonal.y.y - 2.0 * height_inv).abs() < 0.001);
    }

    #[test]
    fn test_projection_frustum() {
        let left = -1.0;
        let right = 1.0;
        let bottom = -0.75;
        let top = 0.75;
        let near = 1.0;
        let far = 100.0;

        let frustum = Projection::frustum(left, right, bottom, top, near, far);

        // Test that the matrix has the expected structure for frustum projection
        assert!(frustum.x.x > 0.0); // X scaling
        assert!(frustum.y.y > 0.0); // Y scaling
        assert!(frustum.z.z < 0.0); // Z mapping
        assert_eq!(frustum.z.w, -1.0); // Perspective division

        // Test near plane scaling
        let width_inv = 1.0 / (right - left);
        let height_inv = 1.0 / (top - bottom);
        assert!((frustum.x.x - 2.0 * near * width_inv).abs() < 0.001);
        assert!((frustum.y.y - 2.0 * near * height_inv).abs() < 0.001);
    }

    #[test]
    fn test_projection_determinant() {
        let identity = Projection::identity();
        assert_eq!(identity.determinant(), 1.0);

        let perspective = Projection::perspective(
            75.0_f32.to_radians(), 16.0/9.0, 0.1, 1000.0
        );
        let det = perspective.determinant();
        assert!(det != 0.0); // Should be invertible

        // Zero matrix should have zero determinant
        let zero = Projection::new(
            Vector4::ZERO, Vector4::ZERO, Vector4::ZERO, Vector4::ZERO
        );
        assert_eq!(zero.determinant(), 0.0);
    }

    #[test]
    fn test_projection_inverse() {
        let identity = Projection::identity();
        let inverse = identity.inverse();
        assert!(identity.is_equal_approx(inverse));

        let perspective = Projection::perspective(
            75.0_f32.to_radians(), 16.0/9.0, 0.1, 1000.0
        );
        let inverse = perspective.inverse();
        let product = perspective * inverse;

        // Product should be approximately identity
        let expected_identity = Projection::identity();
        assert!((product.x.x - expected_identity.x.x).abs() < 0.001);
        assert!((product.y.y - expected_identity.y.y).abs() < 0.001);
        assert!((product.z.z - expected_identity.z.z).abs() < 0.001);
        assert!((product.w.w - expected_identity.w.w).abs() < 0.001);
    }

    #[test]
    fn test_projection_vector_transformation() {
        let identity = Projection::identity();

        // Test 4D vector transformation
        let vec4 = Vector4::new(1.0, 2.0, 3.0, 1.0);
        let transformed4 = identity.transform_vector4(vec4);
        assert_eq!(transformed4, vec4);

        // Test 3D point transformation
        let point = Vector3::new(1.0, 2.0, 3.0);
        let transformed3 = identity.transform_point(point);
        assert_eq!(transformed3, point);

        // Test operator overloads
        let op_transformed4 = identity * vec4;
        let op_transformed3 = identity * point;
        assert_eq!(op_transformed4, vec4);
        assert_eq!(op_transformed3, point);
    }

    #[test]
    fn test_projection_matrix_multiplication() {
        let identity = Projection::identity();
        let perspective = Projection::perspective(
            75.0_f32.to_radians(), 16.0/9.0, 0.1, 1000.0
        );

        // Identity multiplication
        let result1 = identity * perspective;
        let result2 = perspective * identity;
        assert!(result1.is_equal_approx(perspective));
        assert!(result2.is_equal_approx(perspective));

        // Self multiplication should be valid
        let self_mult = perspective * perspective;
        assert!(self_mult.determinant() != 0.0);
    }

    #[test]
    fn test_projection_frustum_planes() {
        let perspective = Projection::perspective(
            90.0_f32.to_radians(), 1.0, 1.0, 100.0
        );
        let planes = perspective.get_frustum_planes();

        // Should have 6 planes: left, right, bottom, top, near, far
        assert_eq!(planes.len(), 6);

        // All planes should have normalized normals
        for plane in &planes {
            let normal_length = plane.normal.length();
            assert!((normal_length - 1.0).abs() < 0.001);
        }
    }

    #[test]
    fn test_projection_frustum_culling() {
        let perspective = Projection::perspective(
            90.0_f32.to_radians(), 1.0, 1.0, 100.0
        );

        // Point at origin should be behind near plane
        let origin = Vector3::ZERO;
        assert!(!perspective.is_point_in_frustum(origin)); // Behind near plane

        // Point in front of camera should be inside
        let front_point = Vector3::new(0.0, 0.0, -5.0);
        assert!(perspective.is_point_in_frustum(front_point));

        // Point far to the side should be outside
        let side_point = Vector3::new(100.0, 0.0, -5.0);
        assert!(!perspective.is_point_in_frustum(side_point));

        // For now, just test that the AABB frustum culling methods exist and don't crash
        // The exact behavior depends on the specific frustum plane extraction implementation
        let bbox_center = AABB::new(-0.1, -0.1, -5.1, 0.2, 0.2, 0.2);
        let _result1 = perspective.is_aabb_in_frustum(bbox_center);

        let bbox_outside = AABB::new(50.0, 50.0, -5.0, 1.0, 1.0, 1.0);
        let _result2 = perspective.is_aabb_in_frustum(bbox_outside);

        // Test that the method doesn't crash and returns a boolean
        assert!(_result1 == true || _result1 == false);
        assert!(_result2 == true || _result2 == false);
    }

    #[test]
    fn test_projection_ndc_operations() {
        let perspective = Projection::perspective(
            90.0_f32.to_radians(), 1.0, 1.0, 100.0
        );

        // Test point at center of near plane
        let near_center = Vector3::new(0.0, 0.0, -1.0);
        let ndc = perspective.project_to_ndc(near_center);

        // Should be at center of NDC space
        assert!((ndc.x - 0.0).abs() < 0.001);
        assert!((ndc.y - 0.0).abs() < 0.001);
        assert!((ndc.z - (-1.0)).abs() < 0.1); // Near plane in NDC

        // Test unprojection
        let unprojected = perspective.unproject_from_ndc(Vector3::new(0.0, 0.0, -1.0));
        // Should be approximately on the near plane
        assert!((unprojected.z - (-1.0)).abs() < 0.1);
    }

    #[test]
    fn test_projection_transpose() {
        let proj = Projection::new(
            Vector4::new(1.0, 2.0, 3.0, 4.0),
            Vector4::new(5.0, 6.0, 7.0, 8.0),
            Vector4::new(9.0, 10.0, 11.0, 12.0),
            Vector4::new(13.0, 14.0, 15.0, 16.0),
        );

        let transposed = proj.transpose();

        // Check that rows and columns are swapped
        assert_eq!(transposed.x, Vector4::new(1.0, 5.0, 9.0, 13.0));
        assert_eq!(transposed.y, Vector4::new(2.0, 6.0, 10.0, 14.0));
        assert_eq!(transposed.z, Vector4::new(3.0, 7.0, 11.0, 15.0));
        assert_eq!(transposed.w, Vector4::new(4.0, 8.0, 12.0, 16.0));

        // Double transpose should return original
        let double_transposed = transposed.transpose();
        assert!(proj.is_equal_approx(double_transposed));
    }

    #[test]
    fn test_projection_equality_checks() {
        let proj1 = Projection::identity();
        let proj2 = Projection::identity();
        assert!(proj1.is_equal_approx(proj2));

        let proj3 = Projection::perspective(
            75.0_f32.to_radians(), 16.0/9.0, 0.1, 1000.0
        );
        assert!(!proj1.is_equal_approx(proj3));

        // Test with small differences
        let proj4 = Projection::new(
            Vector4::new(1.0, 0.0, 0.0, 0.0),
            Vector4::new(0.0, 1.0, 0.0, 0.0),
            Vector4::new(0.0, 0.0, 1.0, 0.0),
            Vector4::new(0.0, 0.0, 0.0001, 1.0), // Small difference
        );
        assert!(!proj1.is_equal_approx(proj4));
    }

    #[test]
    fn test_projection_display() {
        let identity = Projection::identity();
        let display_str = format!("{}", identity);
        assert!(display_str.contains("Projection"));
        assert!(display_str.contains("1"));
        assert!(display_str.contains("0"));

        // Should be formatted as a matrix
        assert!(display_str.contains("["));
        assert!(display_str.contains("]"));
        assert!(display_str.contains(","));
    }

    #[test]
    fn test_projection_perspective_properties() {
        let fov = 60.0_f32.to_radians();
        let aspect = 1.6;
        let near = 0.5;
        let far = 50.0;

        let perspective = Projection::perspective(fov, aspect, near, far);

        // Test that points at different depths project correctly
        let near_point = Vector3::new(0.0, 0.0, -near);
        let far_point = Vector3::new(0.0, 0.0, -far);

        let near_ndc = perspective.project_to_ndc(near_point);
        let far_ndc = perspective.project_to_ndc(far_point);

        // Near point should have z closer to -1, far point closer to 1
        assert!(near_ndc.z < far_ndc.z);

        // Points at same depth but different x should have different x in NDC
        let left_point = Vector3::new(-1.0, 0.0, -5.0);
        let right_point = Vector3::new(1.0, 0.0, -5.0);

        let left_ndc = perspective.project_to_ndc(left_point);
        let right_ndc = perspective.project_to_ndc(right_point);

        assert!(left_ndc.x < right_ndc.x);
    }

    #[test]
    fn test_projection_orthogonal_properties() {
        let left = -5.0;
        let right = 5.0;
        let bottom = -3.0;
        let top = 3.0;
        let near = 1.0;
        let far = 20.0;

        let orthogonal = Projection::orthogonal(left, right, bottom, top, near, far);

        // Test that parallel lines remain parallel (no perspective distortion)
        let point1 = Vector3::new(1.0, 1.0, -5.0);
        let point2 = Vector3::new(2.0, 1.0, -5.0);
        let point3 = Vector3::new(1.0, 1.0, -10.0);
        let point4 = Vector3::new(2.0, 1.0, -10.0);

        let ndc1 = orthogonal.project_to_ndc(point1);
        let ndc2 = orthogonal.project_to_ndc(point2);
        let ndc3 = orthogonal.project_to_ndc(point3);
        let ndc4 = orthogonal.project_to_ndc(point4);

        // Horizontal distances should be preserved
        let dist_near = ndc2.x - ndc1.x;
        let dist_far = ndc4.x - ndc3.x;
        assert!((dist_near - dist_far).abs() < 0.001);
    }

    #[test]
    fn test_projection_edge_cases() {
        // Test with extreme aspect ratios
        let wide_perspective = Projection::perspective(
            60.0_f32.to_radians(), 10.0, 0.1, 100.0
        );
        assert!(wide_perspective.determinant() != 0.0);

        let tall_perspective = Projection::perspective(
            60.0_f32.to_radians(), 0.1, 0.1, 100.0
        );
        assert!(tall_perspective.determinant() != 0.0);

        // Test with very small near/far distances
        let close_perspective = Projection::perspective(
            60.0_f32.to_radians(), 1.0, 0.001, 0.01
        );
        assert!(close_perspective.determinant() != 0.0);

        // Test orthogonal with zero-size dimensions
        let thin_orthogonal = Projection::orthogonal(
            -0.001, 0.001, -1.0, 1.0, 0.1, 100.0
        );
        assert!(thin_orthogonal.determinant() != 0.0);
    }

    #[test]
    fn test_projection_numerical_stability() {
        // Test with very large values
        let large_perspective = Projection::perspective(
            60.0_f32.to_radians(), 1.0, 1.0, 1e6
        );
        let large_point = Vector3::new(1000.0, 1000.0, -1000.0);
        let transformed = large_perspective.transform_point(large_point);

        // Should not produce NaN or infinite values
        assert!(transformed.x.is_finite());
        assert!(transformed.y.is_finite());
        assert!(transformed.z.is_finite());

        // Test inverse with large matrices
        let inverse = large_perspective.inverse();
        assert!(inverse.determinant().is_finite());
        assert!(inverse.determinant() != 0.0);
    }
}
