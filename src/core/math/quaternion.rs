//! Comprehensive quaternion implementation for 3D rotations and orientations.
//!
//! This module provides a complete quaternion implementation optimized for 3D rotation
//! operations, orientation calculations, and smooth interpolation commonly used in
//! 3D graphics, game development, and robotics applications.

use std::fmt;
use super::Vector3;
use super::fast_inv_sqrt::fast_inv_sqrt;

/// ### A quaternion for representing 3D rotations and orientations.
///
/// Quaternions provide a mathematically robust way to represent rotations in 3D space
/// without the gimbal lock issues of Euler angles. They consist of four components:
/// a scalar part (w) and a vector part (x, y, z), representing rotations as
/// unit quaternions on the 4D unit sphere.
///
/// ## Mathematical Representation
///
/// A quaternion q = w + xi + yj + zk where:
/// - `w` is the scalar (real) component
/// - `x`, `y`, `z` are the vector (imaginary) components
/// - For rotations, quaternions are normalized (unit quaternions)
///
/// ## Use Cases
///
/// Quaternion is ideal for:
/// - **3D Rotations**: Smooth, gimbal-lock-free rotation representation
/// - **Animation**: Spherical linear interpolation (SLERP) for smooth transitions
/// - **Physics**: Orientation tracking, angular velocity integration
/// - **Graphics**: Camera orientation, object transformations
///
/// # Examples
/// ```
/// # use verturion::core::math::{Quaternion, Vector3};
/// // Create identity quaternion (no rotation)
/// let identity = Quaternion::IDENTITY;
///
/// // Create rotation around Y axis (90 degrees)
/// let rotation = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
///
/// // Rotate a vector
/// let vector = Vector3::new(1.0, 0.0, 0.0);
/// let rotated = rotation * vector;
///
/// // Combine rotations
/// let combined = rotation * identity;
/// ```
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct Quaternion {
    /// The x component of the quaternion
    pub x: f32,
    /// The y component of the quaternion
    pub y: f32,
    /// The z component of the quaternion
    pub z: f32,
    /// The w (scalar) component of the quaternion
    pub w: f32,
}

impl Quaternion {
    /// ### Identity quaternion representing no rotation.
    ///
    /// The identity quaternion (0, 0, 0, 1) represents no rotation,
    /// equivalent to a 0-degree rotation around any axis.
    pub const IDENTITY: Quaternion = Quaternion {
        x: 0.0,
        y: 0.0,
        z: 0.0,
        w: 1.0,
    };

    /// ### Creates a new quaternion with the specified components.
    ///
    /// # Parameters
    /// - `x`: The x component
    /// - `y`: The y component
    /// - `z`: The z component
    /// - `w`: The w (scalar) component
    ///
    /// # Returns
    /// A new quaternion with the given components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Quaternion;
    /// let q = Quaternion::new(0.0, 0.0, 0.0, 1.0);
    /// assert_eq!(q, Quaternion::IDENTITY);
    /// ```
    #[inline]
    pub const fn new(x: f32, y: f32, z: f32, w: f32) -> Self {
        Self { x, y, z, w }
    }

    /// ### Creates a quaternion from an axis and angle of rotation.
    ///
    /// This is the most common way to create a rotation quaternion.
    /// The axis should be normalized for correct results.
    ///
    /// # Parameters
    /// - `axis`: The rotation axis (should be normalized)
    /// - `angle`: The rotation angle in radians
    ///
    /// # Returns
    /// A unit quaternion representing the rotation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Quaternion, Vector3};
    /// // 90-degree rotation around Y axis
    /// let q = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
    /// ```
    #[inline]
    pub fn from_axis_angle(axis: Vector3, angle: f32) -> Self {
        let half_angle = angle * 0.5;
        let sin_half = half_angle.sin();
        let cos_half = half_angle.cos();

        Self {
            x: axis.x * sin_half,
            y: axis.y * sin_half,
            z: axis.z * sin_half,
            w: cos_half,
        }
    }

    /// ### Creates a quaternion from Euler angles (pitch, yaw, roll).
    ///
    /// Converts Euler angles to a quaternion using the ZYX rotation order.
    /// This is equivalent to applying rotations in the order: Z (roll), Y (yaw), X (pitch).
    ///
    /// # Parameters
    /// - `euler`: Vector3 containing (pitch, yaw, roll) in radians
    ///
    /// # Returns
    /// A quaternion representing the combined rotation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Quaternion, Vector3};
    /// // 45-degree rotation around each axis
    /// let euler = Vector3::new(
    ///     std::f32::consts::PI / 4.0,  // pitch
    ///     std::f32::consts::PI / 4.0,  // yaw
    ///     std::f32::consts::PI / 4.0   // roll
    /// );
    /// let q = Quaternion::from_euler(euler);
    /// ```
    #[inline]
    pub fn from_euler(euler: Vector3) -> Self {
        let half_x = euler.x * 0.5;
        let half_y = euler.y * 0.5;
        let half_z = euler.z * 0.5;

        let cos_x = half_x.cos();
        let sin_x = half_x.sin();
        let cos_y = half_y.cos();
        let sin_y = half_y.sin();
        let cos_z = half_z.cos();
        let sin_z = half_z.sin();

        Self {
            x: sin_x * cos_y * cos_z - cos_x * sin_y * sin_z,
            y: cos_x * sin_y * cos_z + sin_x * cos_y * sin_z,
            z: cos_x * cos_y * sin_z - sin_x * sin_y * cos_z,
            w: cos_x * cos_y * cos_z + sin_x * sin_y * sin_z,
        }
    }

    /// ### Calculates the length (magnitude) of the quaternion.
    ///
    /// For unit quaternions (representing rotations), this should be 1.0.
    /// Non-unit quaternions may need normalization.
    ///
    /// # Returns
    /// The length of the quaternion.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Quaternion;
    /// let q = Quaternion::IDENTITY;
    /// assert!((q.length() - 1.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn length(self) -> f32 {
        (self.x * self.x + self.y * self.y + self.z * self.z + self.w * self.w).sqrt()
    }

    /// ### Calculates the squared length of the quaternion.
    ///
    /// This is more efficient than `length()` when you only need to compare
    /// lengths or check if the quaternion is normalized.
    ///
    /// # Returns
    /// The squared length of the quaternion.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Quaternion;
    /// let q = Quaternion::IDENTITY;
    /// assert!((q.length_squared() - 1.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn length_squared(self) -> f32 {
        self.x * self.x + self.y * self.y + self.z * self.z + self.w * self.w
    }

    /// ### Returns a normalized (unit) quaternion.
    ///
    /// Normalizing a quaternion ensures it represents a valid rotation.
    /// This operation is essential for maintaining numerical stability.
    ///
    /// # Returns
    /// A normalized quaternion, or the original if it has zero length.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Quaternion;
    /// let q = Quaternion::new(1.0, 1.0, 1.0, 1.0);
    /// let normalized = q.normalized();
    /// assert!((normalized.length() - 1.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn normalized(self) -> Self {
        let length_sq = self.length_squared();
        if length_sq < f32::EPSILON {
            return self;
        }

        let inv_length = fast_inv_sqrt(length_sq);
        Self {
            x: self.x * inv_length,
            y: self.y * inv_length,
            z: self.z * inv_length,
            w: self.w * inv_length,
        }
    }

    /// ### Returns the conjugate of the quaternion.
    ///
    /// The conjugate of a quaternion q = (x, y, z, w) is q* = (-x, -y, -z, w).
    /// For unit quaternions, the conjugate represents the inverse rotation.
    ///
    /// # Returns
    /// The conjugate quaternion.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Quaternion;
    /// let q = Quaternion::new(1.0, 2.0, 3.0, 4.0);
    /// let conj = q.conjugate();
    /// assert_eq!(conj, Quaternion::new(-1.0, -2.0, -3.0, 4.0));
    /// ```
    #[inline]
    pub fn conjugate(self) -> Self {
        Self {
            x: -self.x,
            y: -self.y,
            z: -self.z,
            w: self.w,
        }
    }

    /// ### Returns the inverse of the quaternion.
    ///
    /// For unit quaternions, the inverse is equal to the conjugate.
    /// For non-unit quaternions, the inverse is conjugate divided by length squared.
    ///
    /// # Returns
    /// The inverse quaternion, or zero quaternion if length is zero.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Quaternion;
    /// let q = Quaternion::IDENTITY;
    /// let inv = q.inverse();
    /// assert_eq!(inv, Quaternion::IDENTITY);
    /// ```
    #[inline]
    pub fn inverse(self) -> Self {
        let length_sq = self.length_squared();
        if length_sq < f32::EPSILON {
            return Self::new(0.0, 0.0, 0.0, 0.0);
        }

        let inv_length_sq = 1.0 / length_sq;
        Self {
            x: -self.x * inv_length_sq,
            y: -self.y * inv_length_sq,
            z: -self.z * inv_length_sq,
            w: self.w * inv_length_sq,
        }
    }

    /// ### Calculates the dot product with another quaternion.
    ///
    /// The dot product is useful for measuring the angular distance between
    /// two rotations and for spherical interpolation calculations.
    ///
    /// # Parameters
    /// - `other`: The other quaternion
    ///
    /// # Returns
    /// The dot product as a scalar value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Quaternion;
    /// let q1 = Quaternion::IDENTITY;
    /// let q2 = Quaternion::IDENTITY;
    /// assert!((q1.dot(q2) - 1.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn dot(self, other: Self) -> f32 {
        self.x * other.x + self.y * other.y + self.z * other.z + self.w * other.w
    }

    /// ### Performs spherical linear interpolation (SLERP) between two quaternions.
    ///
    /// SLERP provides smooth interpolation between rotations, maintaining constant
    /// angular velocity. This is the preferred method for animating rotations.
    ///
    /// # Parameters
    /// - `other`: The target quaternion to interpolate towards
    /// - `t`: Interpolation factor (0.0 = self, 1.0 = other)
    ///
    /// # Returns
    /// The interpolated quaternion.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Quaternion, Vector3};
    /// let q1 = Quaternion::IDENTITY;
    /// let q2 = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
    /// let interpolated = q1.slerp(q2, 0.5); // 45-degree rotation
    /// ```
    #[inline]
    pub fn slerp(self, other: Self, t: f32) -> Self {
        let mut dot = self.dot(other);

        // Choose the shorter path by flipping one quaternion if needed
        let mut other = other;
        if dot < 0.0 {
            other = Self::new(-other.x, -other.y, -other.z, -other.w);
            dot = -dot;
        }

        // If quaternions are very close, use linear interpolation
        if dot > 0.9995 {
            return Self {
                x: self.x + t * (other.x - self.x),
                y: self.y + t * (other.y - self.y),
                z: self.z + t * (other.z - self.z),
                w: self.w + t * (other.w - self.w),
            }.normalized();
        }

        // Calculate spherical interpolation
        let theta = dot.acos();
        let sin_theta = theta.sin();
        let factor1 = ((1.0 - t) * theta).sin() / sin_theta;
        let factor2 = (t * theta).sin() / sin_theta;

        Self {
            x: factor1 * self.x + factor2 * other.x,
            y: factor1 * self.y + factor2 * other.y,
            z: factor1 * self.z + factor2 * other.z,
            w: factor1 * self.w + factor2 * other.w,
        }
    }

    /// ### Rotates a 3D vector by this quaternion.
    ///
    /// This applies the rotation represented by the quaternion to a 3D vector.
    /// The quaternion should be normalized for correct results.
    ///
    /// # Parameters
    /// - `vector`: The vector to rotate
    ///
    /// # Returns
    /// The rotated vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Quaternion, Vector3};
    /// let rotation = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
    /// let vector = Vector3::new(1.0, 0.0, 0.0);
    /// let rotated = rotation.rotate_vector(vector);
    /// ```
    #[inline]
    pub fn rotate_vector(self, vector: Vector3) -> Vector3 {
        // Using the formula: v' = q * v * q^-1
        // Optimized version: v' = v + 2 * cross(q.xyz, cross(q.xyz, v) + q.w * v)
        let qvec = Vector3::new(self.x, self.y, self.z);
        let cross1 = qvec.cross(vector);
        let cross2 = qvec.cross(cross1 + vector * self.w);
        vector + cross2 * 2.0
    }

    /// ### Converts the quaternion to Euler angles (pitch, yaw, roll).
    ///
    /// Returns the Euler angles in radians using the ZYX rotation order.
    /// Note that this conversion can suffer from gimbal lock at certain orientations.
    ///
    /// # Returns
    /// Vector3 containing (pitch, yaw, roll) in radians.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Quaternion, Vector3};
    /// let q = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
    /// let euler = q.to_euler();
    /// ```
    #[inline]
    pub fn to_euler(self) -> Vector3 {
        // Roll (x-axis rotation)
        let sin_r_cos_p = 2.0 * (self.w * self.x + self.y * self.z);
        let cos_r_cos_p = 1.0 - 2.0 * (self.x * self.x + self.y * self.y);
        let roll = sin_r_cos_p.atan2(cos_r_cos_p);

        // Pitch (y-axis rotation)
        let sin_p = 2.0 * (self.w * self.y - self.z * self.x);
        let pitch = if sin_p.abs() >= 1.0 {
            std::f32::consts::PI / 2.0 * sin_p.signum()
        } else {
            sin_p.asin()
        };

        // Yaw (z-axis rotation)
        let sin_y_cos_p = 2.0 * (self.w * self.z + self.x * self.y);
        let cos_y_cos_p = 1.0 - 2.0 * (self.y * self.y + self.z * self.z);
        let yaw = sin_y_cos_p.atan2(cos_y_cos_p);

        Vector3::new(pitch, yaw, roll)
    }

    /// ### Checks if the quaternion is approximately normalized.
    ///
    /// A normalized quaternion has a length of 1.0, which is required for
    /// representing valid rotations.
    ///
    /// # Returns
    /// `true` if the quaternion is approximately normalized.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Quaternion;
    /// let q = Quaternion::IDENTITY;
    /// assert!(q.is_normalized());
    /// ```
    #[inline]
    pub fn is_normalized(self) -> bool {
        (self.length_squared() - 1.0).abs() < 0.001
    }

    /// ### Checks if the quaternion is approximately equal to another.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    ///
    /// # Parameters
    /// - `other`: The other quaternion to compare with
    ///
    /// # Returns
    /// `true` if the quaternions are approximately equal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Quaternion;
    /// let q1 = Quaternion::IDENTITY;
    /// let q2 = Quaternion::new(0.0, 0.0, 0.0, 1.0);
    /// assert!(q1.is_equal_approx(q2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Self) -> bool {
        (self.x - other.x).abs() < f32::EPSILON
            && (self.y - other.y).abs() < f32::EPSILON
            && (self.z - other.z).abs() < f32::EPSILON
            && (self.w - other.w).abs() < f32::EPSILON
    }
}

impl Default for Quaternion {
    /// Returns the identity quaternion.
    #[inline]
    fn default() -> Self {
        Self::IDENTITY
    }
}

impl fmt::Display for Quaternion {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "({}, {}, {}, {})", self.x, self.y, self.z, self.w)
    }
}

// Quaternion multiplication (composition of rotations)
impl std::ops::Mul for Quaternion {
    type Output = Self;

    /// Multiplies two quaternions (composes rotations).
    /// The result represents applying the first rotation, then the second.
    #[inline]
    fn mul(self, other: Self) -> Self::Output {
        Self {
            x: self.w * other.x + self.x * other.w + self.y * other.z - self.z * other.y,
            y: self.w * other.y - self.x * other.z + self.y * other.w + self.z * other.x,
            z: self.w * other.z + self.x * other.y - self.y * other.x + self.z * other.w,
            w: self.w * other.w - self.x * other.x - self.y * other.y - self.z * other.z,
        }
    }
}

// Quaternion-Vector3 multiplication (rotation)
impl std::ops::Mul<Vector3> for Quaternion {
    type Output = Vector3;

    /// Rotates a Vector3 by this quaternion.
    #[inline]
    fn mul(self, vector: Vector3) -> Self::Output {
        self.rotate_vector(vector)
    }
}

// Scalar multiplication
impl std::ops::Mul<f32> for Quaternion {
    type Output = Self;

    #[inline]
    fn mul(self, scalar: f32) -> Self::Output {
        Self {
            x: self.x * scalar,
            y: self.y * scalar,
            z: self.z * scalar,
            w: self.w * scalar,
        }
    }
}

impl std::ops::Mul<Quaternion> for f32 {
    type Output = Quaternion;

    #[inline]
    fn mul(self, quaternion: Quaternion) -> Self::Output {
        quaternion * self
    }
}

// Addition
impl std::ops::Add for Quaternion {
    type Output = Self;

    #[inline]
    fn add(self, other: Self) -> Self::Output {
        Self {
            x: self.x + other.x,
            y: self.y + other.y,
            z: self.z + other.z,
            w: self.w + other.w,
        }
    }
}

// Subtraction
impl std::ops::Sub for Quaternion {
    type Output = Self;

    #[inline]
    fn sub(self, other: Self) -> Self::Output {
        Self {
            x: self.x - other.x,
            y: self.y - other.y,
            z: self.z - other.z,
            w: self.w - other.w,
        }
    }
}

// Negation
impl std::ops::Neg for Quaternion {
    type Output = Self;

    #[inline]
    fn neg(self) -> Self::Output {
        Self {
            x: -self.x,
            y: -self.y,
            z: -self.z,
            w: -self.w,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_quaternion_creation() {
        let q = Quaternion::new(1.0, 2.0, 3.0, 4.0);
        assert_eq!(q.x, 1.0);
        assert_eq!(q.y, 2.0);
        assert_eq!(q.z, 3.0);
        assert_eq!(q.w, 4.0);

        let identity = Quaternion::IDENTITY;
        assert_eq!(identity, Quaternion::new(0.0, 0.0, 0.0, 1.0));

        let default = Quaternion::default();
        assert_eq!(default, Quaternion::IDENTITY);
    }

    #[test]
    fn test_quaternion_from_axis_angle() {
        // 90-degree rotation around Y axis
        let q = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
        assert!(q.is_normalized());

        // Test that it rotates X axis to -Z axis
        let rotated = q * Vector3::RIGHT;
        assert!((rotated.x - 0.0).abs() < 0.001);
        assert!((rotated.y - 0.0).abs() < 0.001);
        assert!((rotated.z - (-1.0)).abs() < 0.001);
    }

    #[test]
    fn test_quaternion_from_euler() {
        let euler = Vector3::new(0.0, std::f32::consts::PI / 2.0, 0.0);
        let q = Quaternion::from_euler(euler);
        assert!(q.is_normalized());

        // Test that the quaternion rotates vectors correctly
        // A 90-degree Y rotation should rotate X axis to -Z axis
        let rotated = q * Vector3::RIGHT;
        assert!((rotated.x - 0.0).abs() < 0.001);
        assert!((rotated.y - 0.0).abs() < 0.001);
        assert!((rotated.z - (-1.0)).abs() < 0.001);
    }

    #[test]
    fn test_quaternion_length_operations() {
        let q = Quaternion::new(1.0, 2.0, 3.0, 4.0);
        let expected_length_sq = 1.0 + 4.0 + 9.0 + 16.0;
        assert!((q.length_squared() - expected_length_sq).abs() < f32::EPSILON);
        assert!((q.length() - expected_length_sq.sqrt()).abs() < 0.001);

        let normalized = q.normalized();
        assert!((normalized.length() - 1.0).abs() < 0.001);
        assert!(normalized.is_normalized());
    }

    #[test]
    fn test_quaternion_conjugate_inverse() {
        let q = Quaternion::new(1.0, 2.0, 3.0, 4.0);
        let conj = q.conjugate();
        assert_eq!(conj, Quaternion::new(-1.0, -2.0, -3.0, 4.0));

        // For unit quaternions, inverse should equal conjugate
        let unit_q = q.normalized();
        let inv = unit_q.inverse();
        let unit_conj = unit_q.conjugate();
        // Use a more lenient epsilon for floating point comparison
        assert!((inv.x - unit_conj.x).abs() < 0.001);
        assert!((inv.y - unit_conj.y).abs() < 0.001);
        assert!((inv.z - unit_conj.z).abs() < 0.001);
        assert!((inv.w - unit_conj.w).abs() < 0.001);

        // q * q^-1 should equal identity
        let product = unit_q * inv;
        assert!(product.is_equal_approx(Quaternion::IDENTITY));
    }

    #[test]
    fn test_quaternion_dot_product() {
        let q1 = Quaternion::IDENTITY;
        let q2 = Quaternion::IDENTITY;
        assert!((q1.dot(q2) - 1.0).abs() < f32::EPSILON);

        let q3 = Quaternion::new(1.0, 0.0, 0.0, 0.0);
        let q4 = Quaternion::new(0.0, 1.0, 0.0, 0.0);
        assert!((q3.dot(q4) - 0.0).abs() < f32::EPSILON);
    }

    #[test]
    fn test_quaternion_slerp() {
        let q1 = Quaternion::IDENTITY;
        let q2 = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);

        // Test endpoints
        let slerp_start = q1.slerp(q2, 0.0);
        assert!(slerp_start.is_equal_approx(q1));

        let slerp_end = q1.slerp(q2, 1.0);
        assert!(slerp_end.is_equal_approx(q2));

        // Test midpoint
        let slerp_mid = q1.slerp(q2, 0.5);
        assert!(slerp_mid.is_normalized());
    }

    #[test]
    fn test_quaternion_vector_rotation() {
        // 90-degree rotation around Y axis
        let rotation = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);

        // Rotate X axis to -Z axis
        let rotated_x = rotation.rotate_vector(Vector3::RIGHT);
        assert!((rotated_x.x - 0.0).abs() < 0.001);
        assert!((rotated_x.y - 0.0).abs() < 0.001);
        assert!((rotated_x.z - (-1.0)).abs() < 0.001);

        // Y axis should remain unchanged
        let rotated_y = rotation.rotate_vector(Vector3::UP);
        assert!((rotated_y - Vector3::UP).length() < 0.001);
    }

    #[test]
    fn test_quaternion_multiplication() {
        let q1 = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 4.0);
        let q2 = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 4.0);

        // Two 45-degree rotations should equal one 90-degree rotation
        let combined = q1 * q2;
        let expected = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);

        // Test by rotating a vector
        let test_vector = Vector3::RIGHT;
        let combined_result = combined * test_vector;
        let expected_result = expected * test_vector;
        assert!((combined_result - expected_result).length() < 0.001);
    }

    #[test]
    fn test_quaternion_arithmetic_operations() {
        let q1 = Quaternion::new(1.0, 2.0, 3.0, 4.0);
        let q2 = Quaternion::new(5.0, 6.0, 7.0, 8.0);

        let sum = q1 + q2;
        assert_eq!(sum, Quaternion::new(6.0, 8.0, 10.0, 12.0));

        let diff = q2 - q1;
        assert_eq!(diff, Quaternion::new(4.0, 4.0, 4.0, 4.0));

        let neg = -q1;
        assert_eq!(neg, Quaternion::new(-1.0, -2.0, -3.0, -4.0));

        let scaled = q1 * 2.0;
        assert_eq!(scaled, Quaternion::new(2.0, 4.0, 6.0, 8.0));

        let scaled2 = 3.0 * q1;
        assert_eq!(scaled2, Quaternion::new(3.0, 6.0, 9.0, 12.0));
    }

    #[test]
    fn test_quaternion_display() {
        let q = Quaternion::new(1.0, 2.0, 3.0, 4.0);
        let display_str = format!("{}", q);
        assert_eq!(display_str, "(1, 2, 3, 4)");
    }

    #[test]
    fn test_quaternion_equality_checks() {
        let q1 = Quaternion::IDENTITY;
        let q2 = Quaternion::new(0.0, 0.0, 0.0, 1.0);
        assert!(q1.is_equal_approx(q2));

        let q3 = Quaternion::new(0.0001, 0.0, 0.0, 1.0);
        assert!(!q1.is_equal_approx(q3));
    }

    #[test]
    fn test_quaternion_edge_cases() {
        // Zero quaternion
        let zero = Quaternion::new(0.0, 0.0, 0.0, 0.0);
        let normalized_zero = zero.normalized();
        assert_eq!(normalized_zero, zero); // Should remain zero

        let inverse_zero = zero.inverse();
        assert_eq!(inverse_zero, zero); // Should remain zero

        // Very small quaternion
        let tiny = Quaternion::new(1e-10, 1e-10, 1e-10, 1e-10);
        let normalized_tiny = tiny.normalized();
        assert!(normalized_tiny.length() < 0.001 || (normalized_tiny.length() - 1.0).abs() < 0.001);
    }
}
