//! Comprehensive 3x3 rotation matrix implementation for 3D transformations.
//!
//! This module provides a complete Basis implementation representing 3x3 rotation matrices
//! used for 3D transformations, rotations, and coordinate system conversions in 3D graphics
//! and game development.

use std::fmt;
use super::{Vector3, Quaternion};

/// ### A 3x3 rotation matrix for representing 3D transformations.
///
/// Basis represents a 3x3 orthogonal matrix used for 3D rotations, scaling, and
/// coordinate system transformations. It consists of three column vectors that
/// define the local coordinate system axes.
///
/// ## Mathematical Representation
///
/// A Basis matrix is represented as:
/// ```text
/// | x.x  y.x  z.x |
/// | x.y  y.y  z.y |
/// | x.z  y.z  z.z |
/// ```
/// Where x, y, z are the column vectors representing the local axes.
///
/// ## Use Cases
///
/// Basis is ideal for:
/// - **3D Rotations**: Matrix-based rotation representation
/// - **Coordinate Systems**: Local-to-world transformations
/// - **Scaling**: Non-uniform scaling operations
/// - **Orientation**: Object orientation in 3D space
/// - **Camera Systems**: View matrix calculations
///
/// # Examples
/// ```
/// # use verturion::core::math::{Basis, Vector3, Quaternion};
/// // Create identity basis (no transformation)
/// let identity = Basis::IDENTITY;
///
/// // Create basis from quaternion
/// let rotation = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
/// let basis = Basis::from_quaternion(rotation);
///
/// // Transform a vector
/// let vector = Vector3::new(1.0, 0.0, 0.0);
/// let transformed = basis * vector;
/// ```
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct Basis {
    /// The x-axis column vector
    pub x: Vector3,
    /// The y-axis column vector
    pub y: Vector3,
    /// The z-axis column vector
    pub z: Vector3,
}

impl Basis {
    /// ### Identity basis representing no transformation.
    ///
    /// The identity basis represents no rotation, scaling, or transformation.
    /// It consists of the standard orthonormal basis vectors.
    pub const IDENTITY: Basis = Basis {
        x: Vector3::RIGHT,
        y: Vector3::UP,
        z: Vector3::BACK,
    };

    /// ### Creates a new basis with the specified column vectors.
    ///
    /// # Parameters
    /// - `x`: The x-axis column vector
    /// - `y`: The y-axis column vector
    /// - `z`: The z-axis column vector
    ///
    /// # Returns
    /// A new basis with the given column vectors.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Vector3};
    /// let basis = Basis::new(
    ///     Vector3::new(1.0, 0.0, 0.0),
    ///     Vector3::new(0.0, 1.0, 0.0),
    ///     Vector3::new(0.0, 0.0, 1.0)
    /// );
    /// assert_eq!(basis, Basis::IDENTITY);
    /// ```
    #[inline]
    pub const fn new(x: Vector3, y: Vector3, z: Vector3) -> Self {
        Self { x, y, z }
    }

    /// ### Creates a basis from a quaternion rotation.
    ///
    /// Converts a quaternion to its equivalent 3x3 rotation matrix representation.
    /// The quaternion should be normalized for correct results.
    ///
    /// # Parameters
    /// - `quaternion`: The quaternion to convert
    ///
    /// # Returns
    /// A basis representing the same rotation as the quaternion.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Quaternion, Vector3};
    /// let rotation = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
    /// let basis = Basis::from_quaternion(rotation);
    /// ```
    #[inline]
    pub fn from_quaternion(quaternion: Quaternion) -> Self {
        let x2 = quaternion.x * 2.0;
        let y2 = quaternion.y * 2.0;
        let z2 = quaternion.z * 2.0;
        let xx = quaternion.x * x2;
        let xy = quaternion.x * y2;
        let xz = quaternion.x * z2;
        let yy = quaternion.y * y2;
        let yz = quaternion.y * z2;
        let zz = quaternion.z * z2;
        let wx = quaternion.w * x2;
        let wy = quaternion.w * y2;
        let wz = quaternion.w * z2;

        Self {
            x: Vector3::new(1.0 - (yy + zz), xy + wz, xz - wy),
            y: Vector3::new(xy - wz, 1.0 - (xx + zz), yz + wx),
            z: Vector3::new(xz + wy, yz - wx, 1.0 - (xx + yy)),
        }
    }

    /// ### Creates a basis from Euler angles (pitch, yaw, roll).
    ///
    /// Converts Euler angles to a 3x3 rotation matrix using the ZYX rotation order.
    ///
    /// # Parameters
    /// - `euler`: Vector3 containing (pitch, yaw, roll) in radians
    ///
    /// # Returns
    /// A basis representing the combined rotation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Vector3};
    /// let euler = Vector3::new(0.0, std::f32::consts::PI / 2.0, 0.0);
    /// let basis = Basis::from_euler(euler);
    /// ```
    #[inline]
    pub fn from_euler(euler: Vector3) -> Self {
        let quaternion = Quaternion::from_euler(euler);
        Self::from_quaternion(quaternion)
    }

    /// ### Creates a basis that looks at a target from a given position.
    ///
    /// Creates a basis where the -Z axis points towards the target,
    /// Y axis points up, and X axis is perpendicular to both.
    ///
    /// # Parameters
    /// - `target`: The target direction to look at (normalized)
    /// - `up`: The up direction (normalized, default: Vector3::UP)
    ///
    /// # Returns
    /// A basis oriented to look at the target.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Vector3};
    /// let target = Vector3::new(1.0, 0.0, 0.0);
    /// let basis = Basis::looking_at(target, Vector3::UP);
    /// ```
    #[inline]
    pub fn looking_at(target: Vector3, up: Vector3) -> Self {
        let z = -target.normalized();
        let x = up.cross(z).normalized();
        let y = z.cross(x);
        Self::new(x, y, z)
    }

    /// ### Creates a basis from a rotation around an axis.
    ///
    /// Creates a rotation matrix for rotating around the specified axis by the given angle.
    ///
    /// # Parameters
    /// - `axis`: The rotation axis (should be normalized)
    /// - `angle`: The rotation angle in radians
    ///
    /// # Returns
    /// A basis representing the rotation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Vector3};
    /// let basis = Basis::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
    /// ```
    #[inline]
    pub fn from_axis_angle(axis: Vector3, angle: f32) -> Self {
        let quaternion = Quaternion::from_axis_angle(axis, angle);
        Self::from_quaternion(quaternion)
    }

    /// ### Creates a scaling basis.
    ///
    /// Creates a basis that scales along each axis by the specified factors.
    ///
    /// # Parameters
    /// - `scale`: The scaling factors for each axis
    ///
    /// # Returns
    /// A basis representing the scaling transformation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Vector3};
    /// let scale_basis = Basis::from_scale(Vector3::new(2.0, 1.0, 0.5));
    /// ```
    #[inline]
    pub fn from_scale(scale: Vector3) -> Self {
        Self {
            x: Vector3::new(scale.x, 0.0, 0.0),
            y: Vector3::new(0.0, scale.y, 0.0),
            z: Vector3::new(0.0, 0.0, scale.z),
        }
    }

    /// ### Returns the determinant of the basis matrix.
    ///
    /// The determinant indicates the scaling factor and orientation of the transformation.
    /// A determinant of 1 indicates a pure rotation, negative values indicate reflection.
    ///
    /// # Returns
    /// The determinant of the 3x3 matrix.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Basis;
    /// let basis = Basis::IDENTITY;
    /// assert!((basis.determinant() - 1.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn determinant(self) -> f32 {
        self.x.x * (self.y.y * self.z.z - self.y.z * self.z.y)
            - self.x.y * (self.y.x * self.z.z - self.y.z * self.z.x)
            + self.x.z * (self.y.x * self.z.y - self.y.y * self.z.x)
    }

    /// ### Returns the transpose of the basis matrix.
    ///
    /// For orthogonal matrices (pure rotations), the transpose equals the inverse.
    ///
    /// # Returns
    /// The transposed basis.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Basis;
    /// let basis = Basis::IDENTITY;
    /// let transposed = basis.transposed();
    /// assert_eq!(transposed, Basis::IDENTITY);
    /// ```
    #[inline]
    pub fn transposed(self) -> Self {
        Self {
            x: Vector3::new(self.x.x, self.y.x, self.z.x),
            y: Vector3::new(self.x.y, self.y.y, self.z.y),
            z: Vector3::new(self.x.z, self.y.z, self.z.z),
        }
    }

    /// ### Returns the inverse of the basis matrix.
    ///
    /// For orthogonal matrices, this is equivalent to the transpose.
    /// For non-orthogonal matrices, this computes the full matrix inverse.
    ///
    /// # Returns
    /// The inverse basis, or the original if determinant is zero.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Basis;
    /// let basis = Basis::IDENTITY;
    /// let inverse = basis.inverse();
    /// assert_eq!(inverse, Basis::IDENTITY);
    /// ```
    #[inline]
    pub fn inverse(self) -> Self {
        let det = self.determinant();
        if det.abs() < f32::EPSILON {
            return self;
        }

        let inv_det = 1.0 / det;
        Self {
            x: Vector3::new(
                (self.y.y * self.z.z - self.y.z * self.z.y) * inv_det,
                (self.x.z * self.z.y - self.x.y * self.z.z) * inv_det,
                (self.x.y * self.y.z - self.x.z * self.y.y) * inv_det,
            ),
            y: Vector3::new(
                (self.y.z * self.z.x - self.y.x * self.z.z) * inv_det,
                (self.x.x * self.z.z - self.x.z * self.z.x) * inv_det,
                (self.x.z * self.y.x - self.x.x * self.y.z) * inv_det,
            ),
            z: Vector3::new(
                (self.y.x * self.z.y - self.y.y * self.z.x) * inv_det,
                (self.x.y * self.z.x - self.x.x * self.z.y) * inv_det,
                (self.x.x * self.y.y - self.x.y * self.y.x) * inv_det,
            ),
        }
    }

    /// ### Returns an orthonormalized version of the basis.
    ///
    /// Ensures that all column vectors are orthogonal and normalized.
    /// Uses the Gram-Schmidt process to maintain the original orientation.
    ///
    /// # Returns
    /// An orthonormalized basis.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Vector3};
    /// let basis = Basis::new(
    ///     Vector3::new(2.0, 0.0, 0.0),
    ///     Vector3::new(0.0, 3.0, 0.0),
    ///     Vector3::new(0.0, 0.0, 4.0)
    /// );
    /// let ortho = basis.orthonormalized();
    /// ```
    #[inline]
    pub fn orthonormalized(self) -> Self {
        let x = self.x.normalized();
        let y = (self.y - x * x.dot(self.y)).normalized();
        let z = (self.z - x * x.dot(self.z) - y * y.dot(self.z)).normalized();
        Self::new(x, y, z)
    }

    /// ### Converts the basis to a quaternion.
    ///
    /// Extracts the rotation component of the basis as a quaternion.
    /// The basis should be orthonormalized for accurate results.
    ///
    /// # Returns
    /// A quaternion representing the rotation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Quaternion, Vector3};
    /// let rotation = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
    /// let basis = Basis::from_quaternion(rotation);
    /// let back_to_quat = basis.to_quaternion();
    /// ```
    #[inline]
    pub fn to_quaternion(self) -> Quaternion {
        let trace = self.x.x + self.y.y + self.z.z;

        if trace > 0.0 {
            let s = (trace + 1.0).sqrt() * 2.0;
            Quaternion::new(
                (self.y.z - self.z.y) / s,
                (self.z.x - self.x.z) / s,
                (self.x.y - self.y.x) / s,
                0.25 * s,
            )
        } else if self.x.x > self.y.y && self.x.x > self.z.z {
            let s = (1.0 + self.x.x - self.y.y - self.z.z).sqrt() * 2.0;
            Quaternion::new(
                0.25 * s,
                (self.y.x + self.x.y) / s,
                (self.z.x + self.x.z) / s,
                (self.y.z - self.z.y) / s,
            )
        } else if self.y.y > self.z.z {
            let s = (1.0 + self.y.y - self.x.x - self.z.z).sqrt() * 2.0;
            Quaternion::new(
                (self.y.x + self.x.y) / s,
                0.25 * s,
                (self.z.y + self.y.z) / s,
                (self.z.x - self.x.z) / s,
            )
        } else {
            let s = (1.0 + self.z.z - self.x.x - self.y.y).sqrt() * 2.0;
            Quaternion::new(
                (self.z.x + self.x.z) / s,
                (self.z.y + self.y.z) / s,
                0.25 * s,
                (self.x.y - self.y.x) / s,
            )
        }
    }

    /// ### Converts the basis to Euler angles.
    ///
    /// Extracts the Euler angles (pitch, yaw, roll) from the basis matrix.
    ///
    /// # Returns
    /// Vector3 containing (pitch, yaw, roll) in radians.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Vector3};
    /// let basis = Basis::from_euler(Vector3::new(0.0, std::f32::consts::PI / 2.0, 0.0));
    /// let euler = basis.to_euler();
    /// ```
    #[inline]
    pub fn to_euler(self) -> Vector3 {
        self.to_quaternion().to_euler()
    }

    /// ### Extracts the scale factors from the basis.
    ///
    /// Returns the scaling factors along each axis by computing the length
    /// of each column vector.
    ///
    /// # Returns
    /// Vector3 containing the scale factors for each axis.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Basis, Vector3};
    /// let scale_basis = Basis::from_scale(Vector3::new(2.0, 3.0, 4.0));
    /// let scale = scale_basis.get_scale();
    /// ```
    #[inline]
    pub fn get_scale(self) -> Vector3 {
        Vector3::new(self.x.length(), self.y.length(), self.z.length())
    }

    /// ### Checks if the basis is approximately orthogonal.
    ///
    /// An orthogonal basis has perpendicular column vectors.
    ///
    /// # Returns
    /// `true` if the basis is approximately orthogonal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Basis;
    /// let basis = Basis::IDENTITY;
    /// assert!(basis.is_orthogonal());
    /// ```
    #[inline]
    pub fn is_orthogonal(self) -> bool {
        let dot_xy = self.x.dot(self.y).abs();
        let dot_xz = self.x.dot(self.z).abs();
        let dot_yz = self.y.dot(self.z).abs();
        dot_xy < 0.001 && dot_xz < 0.001 && dot_yz < 0.001
    }

    /// ### Checks if the basis is approximately equal to another.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    ///
    /// # Parameters
    /// - `other`: The other basis to compare with
    ///
    /// # Returns
    /// `true` if the bases are approximately equal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Basis;
    /// let b1 = Basis::IDENTITY;
    /// let b2 = Basis::IDENTITY;
    /// assert!(b1.is_equal_approx(b2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Self) -> bool {
        self.x.is_equal_approx(other.x)
            && self.y.is_equal_approx(other.y)
            && self.z.is_equal_approx(other.z)
    }
}

impl Default for Basis {
    /// Returns the identity basis.
    #[inline]
    fn default() -> Self {
        Self::IDENTITY
    }
}

impl fmt::Display for Basis {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "[{}, {}, {}]",
            self.x, self.y, self.z
        )
    }
}

// Basis multiplication (composition of transformations)
impl std::ops::Mul for Basis {
    type Output = Self;

    /// Multiplies two bases (composes transformations).
    /// The result represents applying the first transformation, then the second.
    #[inline]
    fn mul(self, other: Self) -> Self::Output {
        Self {
            x: Vector3::new(
                self.x.x * other.x.x + self.y.x * other.x.y + self.z.x * other.x.z,
                self.x.y * other.x.x + self.y.y * other.x.y + self.z.y * other.x.z,
                self.x.z * other.x.x + self.y.z * other.x.y + self.z.z * other.x.z,
            ),
            y: Vector3::new(
                self.x.x * other.y.x + self.y.x * other.y.y + self.z.x * other.y.z,
                self.x.y * other.y.x + self.y.y * other.y.y + self.z.y * other.y.z,
                self.x.z * other.y.x + self.y.z * other.y.y + self.z.z * other.y.z,
            ),
            z: Vector3::new(
                self.x.x * other.z.x + self.y.x * other.z.y + self.z.x * other.z.z,
                self.x.y * other.z.x + self.y.y * other.z.y + self.z.y * other.z.z,
                self.x.z * other.z.x + self.y.z * other.z.y + self.z.z * other.z.z,
            ),
        }
    }
}

// Basis-Vector3 multiplication (transformation)
impl std::ops::Mul<Vector3> for Basis {
    type Output = Vector3;

    /// Transforms a Vector3 by this basis.
    #[inline]
    fn mul(self, vector: Vector3) -> Self::Output {
        Vector3::new(
            self.x.x * vector.x + self.y.x * vector.y + self.z.x * vector.z,
            self.x.y * vector.x + self.y.y * vector.y + self.z.y * vector.z,
            self.x.z * vector.x + self.y.z * vector.y + self.z.z * vector.z,
        )
    }
}

// Scalar multiplication
impl std::ops::Mul<f32> for Basis {
    type Output = Self;

    #[inline]
    fn mul(self, scalar: f32) -> Self::Output {
        Self {
            x: self.x * scalar,
            y: self.y * scalar,
            z: self.z * scalar,
        }
    }
}

impl std::ops::Mul<Basis> for f32 {
    type Output = Basis;

    #[inline]
    fn mul(self, basis: Basis) -> Self::Output {
        basis * self
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_basis_creation() {
        let basis = Basis::new(
            Vector3::new(1.0, 0.0, 0.0),
            Vector3::new(0.0, 1.0, 0.0),
            Vector3::new(0.0, 0.0, 1.0),
        );
        assert_eq!(basis, Basis::IDENTITY);

        let default = Basis::default();
        assert_eq!(default, Basis::IDENTITY);
    }

    #[test]
    fn test_basis_from_quaternion() {
        let rotation = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);
        let basis = Basis::from_quaternion(rotation);

        // Test that it rotates X axis to -Z axis
        let rotated = basis * Vector3::RIGHT;
        assert!((rotated.x - 0.0).abs() < 0.001);
        assert!((rotated.y - 0.0).abs() < 0.001);
        assert!((rotated.z - (-1.0)).abs() < 0.001);
    }

    #[test]
    fn test_basis_from_euler() {
        let euler = Vector3::new(0.0, std::f32::consts::PI / 2.0, 0.0);
        let basis = Basis::from_euler(euler);

        // Test that it rotates X axis to -Z axis
        let rotated = basis * Vector3::RIGHT;
        assert!((rotated.x - 0.0).abs() < 0.001);
        assert!((rotated.y - 0.0).abs() < 0.001);
        assert!((rotated.z - (-1.0)).abs() < 0.001);
    }

    #[test]
    fn test_basis_from_axis_angle() {
        let basis = Basis::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);

        // Test that it rotates X axis to -Z axis
        let rotated = basis * Vector3::RIGHT;
        assert!((rotated.x - 0.0).abs() < 0.001);
        assert!((rotated.y - 0.0).abs() < 0.001);
        assert!((rotated.z - (-1.0)).abs() < 0.001);
    }

    #[test]
    fn test_basis_from_scale() {
        let scale_basis = Basis::from_scale(Vector3::new(2.0, 3.0, 4.0));
        let vector = Vector3::new(1.0, 1.0, 1.0);
        let scaled = scale_basis * vector;

        assert!((scaled.x - 2.0).abs() < f32::EPSILON);
        assert!((scaled.y - 3.0).abs() < f32::EPSILON);
        assert!((scaled.z - 4.0).abs() < f32::EPSILON);

        let scale = scale_basis.get_scale();
        assert!((scale.x - 2.0).abs() < 0.001);
        assert!((scale.y - 3.0).abs() < 0.001);
        assert!((scale.z - 4.0).abs() < 0.001);
    }

    #[test]
    fn test_basis_looking_at() {
        let target = Vector3::new(1.0, 0.0, 0.0);
        let basis = Basis::looking_at(target, Vector3::UP);

        // The -Z axis should point towards the target
        let forward = -basis.z;
        assert!((forward.x - 1.0).abs() < 0.001);
        assert!((forward.y - 0.0).abs() < 0.001);
        assert!((forward.z - 0.0).abs() < 0.001);
    }

    #[test]
    fn test_basis_determinant() {
        let identity = Basis::IDENTITY;
        assert!((identity.determinant() - 1.0).abs() < f32::EPSILON);

        let scale_basis = Basis::from_scale(Vector3::new(2.0, 3.0, 4.0));
        assert!((scale_basis.determinant() - 24.0).abs() < 0.001); // 2 * 3 * 4 = 24
    }

    #[test]
    fn test_basis_transpose_inverse() {
        let rotation = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 4.0);
        let basis = Basis::from_quaternion(rotation);

        let transposed = basis.transposed();
        let inverse = basis.inverse();

        // For orthogonal matrices, transpose should equal inverse
        assert!(transposed.is_equal_approx(inverse));

        // basis * inverse should equal identity
        let product = basis * inverse;
        assert!(product.is_equal_approx(Basis::IDENTITY));
    }

    #[test]
    fn test_basis_orthonormalized() {
        let non_ortho = Basis::new(
            Vector3::new(2.0, 0.1, 0.0),
            Vector3::new(0.1, 3.0, 0.0),
            Vector3::new(0.0, 0.0, 4.0),
        );

        let ortho = non_ortho.orthonormalized();
        assert!(ortho.is_orthogonal());

        // Check that vectors are normalized
        assert!((ortho.x.length() - 1.0).abs() < 0.001);
        assert!((ortho.y.length() - 1.0).abs() < 0.001);
        assert!((ortho.z.length() - 1.0).abs() < 0.001);
    }

    #[test]
    fn test_basis_quaternion_conversion() {
        let original_quat = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 3.0);
        let basis = Basis::from_quaternion(original_quat);
        let converted_quat = basis.to_quaternion();

        // Test by rotating a vector with both quaternions
        let test_vector = Vector3::RIGHT;
        let result1 = original_quat * test_vector;
        let result2 = converted_quat * test_vector;

        assert!((result1 - result2).length() < 0.001);
    }

    #[test]
    fn test_basis_multiplication() {
        let rotation1 = Basis::from_axis_angle(Vector3::UP, std::f32::consts::PI / 4.0);
        let rotation2 = Basis::from_axis_angle(Vector3::UP, std::f32::consts::PI / 4.0);

        // Two 45-degree rotations should equal one 90-degree rotation
        let combined = rotation1 * rotation2;
        let expected = Basis::from_axis_angle(Vector3::UP, std::f32::consts::PI / 2.0);

        // Test by rotating a vector
        let test_vector = Vector3::RIGHT;
        let combined_result = combined * test_vector;
        let expected_result = expected * test_vector;
        assert!((combined_result - expected_result).length() < 0.001);
    }

    #[test]
    fn test_basis_vector_transformation() {
        let scale_basis = Basis::from_scale(Vector3::new(2.0, 3.0, 4.0));
        let vector = Vector3::new(1.0, 1.0, 1.0);
        let transformed = scale_basis * vector;

        assert!((transformed.x - 2.0).abs() < f32::EPSILON);
        assert!((transformed.y - 3.0).abs() < f32::EPSILON);
        assert!((transformed.z - 4.0).abs() < f32::EPSILON);
    }

    #[test]
    fn test_basis_scalar_multiplication() {
        let basis = Basis::IDENTITY;
        let scaled = basis * 2.0;
        let scaled2 = 3.0 * basis;

        assert_eq!(scaled.x, Vector3::new(2.0, 0.0, 0.0));
        assert_eq!(scaled.y, Vector3::new(0.0, 2.0, 0.0));
        assert_eq!(scaled.z, Vector3::new(0.0, 0.0, 2.0));

        assert_eq!(scaled2.x, Vector3::new(3.0, 0.0, 0.0));
        assert_eq!(scaled2.y, Vector3::new(0.0, 3.0, 0.0));
        assert_eq!(scaled2.z, Vector3::new(0.0, 0.0, 3.0));
    }

    #[test]
    fn test_basis_display() {
        let basis = Basis::IDENTITY;
        let display_str = format!("{}", basis);
        assert!(display_str.contains("(1, 0, 0)"));
        assert!(display_str.contains("(0, 1, 0)"));
        assert!(display_str.contains("(0, 0, 1)"));
    }

    #[test]
    fn test_basis_equality_checks() {
        let b1 = Basis::IDENTITY;
        let b2 = Basis::IDENTITY;
        assert!(b1.is_equal_approx(b2));

        let b3 = Basis::new(
            Vector3::new(1.0001, 0.0, 0.0),
            Vector3::new(0.0, 1.0, 0.0),
            Vector3::new(0.0, 0.0, 1.0),
        );
        assert!(!b1.is_equal_approx(b3));
    }

    #[test]
    fn test_basis_orthogonal_check() {
        let identity = Basis::IDENTITY;
        assert!(identity.is_orthogonal());

        let non_ortho = Basis::new(
            Vector3::new(1.0, 0.5, 0.0),
            Vector3::new(0.5, 1.0, 0.0),
            Vector3::new(0.0, 0.0, 1.0),
        );
        assert!(!non_ortho.is_orthogonal());
    }
}
