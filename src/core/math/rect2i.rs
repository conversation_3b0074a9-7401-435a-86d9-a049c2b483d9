use std::fmt;
use super::Vector2i;

/// ### 2D Integer Rectangle
///
/// Rect2i represents a 2D rectangle with integer precision, defined by
/// a position (top-left corner) and size (width and height). This structure
/// is ideal for pixel-perfect operations, UI layout with integer coordinates,
/// and grid-based calculations where floating-point precision is not needed.
///
/// The rectangle is defined as:
/// - `position`: The top-left corner coordinates (integer)
/// - `size`: The width and height dimensions (integer)
///
/// # Examples
/// ```
/// # use verturion::core::math::{Rect2i, Vector2i};
/// // Create a rectangle at (10, 20) with size 100x50
/// let rect = Rect2i::new(10, 20, 100, 50);
///
/// // Check if a point is inside the rectangle
/// let point = Vector2i::new(50, 40);
/// assert!(rect.contains_point(point));
///
/// // Get the center of the rectangle
/// let center = rect.get_center();
/// assert_eq!(center, Vector2i::new(60, 45));
/// ```
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub struct Rect2i {
    /// The position of the rectangle (top-left corner)
    pub position: Vector2i,
    /// The size of the rectangle (width and height)
    pub size: Vector2i,
}

impl Rect2i {
    /// Empty rectangle at origin with zero size
    pub const ZERO: Rect2i = Rect2i {
        position: Vector2i::ZERO,
        size: Vector2i::ZERO,
    };

    /// ### Creates a new Rect2i from position and size components.
    ///
    /// # Arguments
    /// * `x` - X coordinate of the top-left corner
    /// * `y` - Y coordinate of the top-left corner
    /// * `width` - Width of the rectangle
    /// * `height` - Height of the rectangle
    ///
    /// # Returns
    /// A new Rect2i with the specified position and size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2i;
    /// let rect = Rect2i::new(10, 20, 100, 50);
    /// assert_eq!(rect.position.x, 10);
    /// assert_eq!(rect.position.y, 20);
    /// assert_eq!(rect.size.x, 100);
    /// assert_eq!(rect.size.y, 50);
    /// ```
    #[inline]
    pub const fn new(x: i32, y: i32, width: i32, height: i32) -> Self {
        Self {
            position: Vector2i::new(x, y),
            size: Vector2i::new(width, height),
        }
    }

    /// ### Creates a new Rect2i from position and size vectors.
    ///
    /// # Arguments
    /// * `position` - The position vector (top-left corner)
    /// * `size` - The size vector (width and height)
    ///
    /// # Returns
    /// A new Rect2i with the specified position and size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let position = Vector2i::new(10, 20);
    /// let size = Vector2i::new(100, 50);
    /// let rect = Rect2i::from_position_size(position, size);
    /// ```
    #[inline]
    pub const fn from_position_size(position: Vector2i, size: Vector2i) -> Self {
        Self { position, size }
    }

    /// ### Creates a new Rect2i from two corner points.
    ///
    /// Automatically determines the correct position and size from any two
    /// corner points, handling cases where points are not top-left and bottom-right.
    ///
    /// # Arguments
    /// * `point1` - First corner point
    /// * `point2` - Second corner point (diagonal to first)
    ///
    /// # Returns
    /// A new Rect2i encompassing both points.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::from_corners(
    ///     Vector2i::new(50, 70),  // bottom-right
    ///     Vector2i::new(10, 20)   // top-left
    /// );
    /// assert_eq!(rect.position, Vector2i::new(10, 20));
    /// assert_eq!(rect.size, Vector2i::new(40, 50));
    /// ```
    #[inline]
    pub fn from_corners(point1: Vector2i, point2: Vector2i) -> Self {
        let min_x = point1.x.min(point2.x);
        let min_y = point1.y.min(point2.y);
        let max_x = point1.x.max(point2.x);
        let max_y = point1.y.max(point2.y);

        Self {
            position: Vector2i::new(min_x, min_y),
            size: Vector2i::new(max_x - min_x, max_y - min_y),
        }
    }

    /// ### Creates a Rect2i centered at a specific point.
    ///
    /// # Arguments
    /// * `center` - The center point of the rectangle
    /// * `size` - The size of the rectangle
    ///
    /// # Returns
    /// A new Rect2i centered at the specified point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::from_center_size(
    ///     Vector2i::new(50, 50),
    ///     Vector2i::new(20, 10)
    /// );
    /// assert_eq!(rect.position, Vector2i::new(40, 45));
    /// ```
    #[inline]
    pub fn from_center_size(center: Vector2i, size: Vector2i) -> Self {
        Self {
            position: Vector2i::new(center.x - size.x / 2, center.y - size.y / 2),
            size,
        }
    }

    /// ### Gets the center point of the rectangle.
    ///
    /// # Returns
    /// The center point as a Vector2i.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 20, 100, 50);
    /// let center = rect.get_center();
    /// assert_eq!(center, Vector2i::new(60, 45));
    /// ```
    #[inline]
    pub fn get_center(self) -> Vector2i {
        Vector2i::new(
            self.position.x + self.size.x / 2,
            self.position.y + self.size.y / 2,
        )
    }

    /// ### Gets the area of the rectangle.
    ///
    /// # Returns
    /// The area (width × height) of the rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2i;
    /// let rect = Rect2i::new(0, 0, 10, 5);
    /// assert_eq!(rect.get_area(), 50);
    /// ```
    #[inline]
    pub fn get_area(self) -> i32 {
        self.size.x * self.size.y
    }

    /// ### Gets the perimeter of the rectangle.
    ///
    /// # Returns
    /// The perimeter (2 × width + 2 × height) of the rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2i;
    /// let rect = Rect2i::new(0, 0, 10, 5);
    /// assert_eq!(rect.get_perimeter(), 30);
    /// ```
    #[inline]
    pub fn get_perimeter(self) -> i32 {
        2 * (self.size.x + self.size.y)
    }

    /// ### Gets the top-left corner of the rectangle.
    ///
    /// # Returns
    /// The top-left corner point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 20, 100, 50);
    /// assert_eq!(rect.get_top_left(), Vector2i::new(10, 20));
    /// ```
    #[inline]
    pub fn get_top_left(self) -> Vector2i {
        self.position
    }

    /// ### Gets the top-right corner of the rectangle.
    ///
    /// # Returns
    /// The top-right corner point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 20, 100, 50);
    /// assert_eq!(rect.get_top_right(), Vector2i::new(110, 20));
    /// ```
    #[inline]
    pub fn get_top_right(self) -> Vector2i {
        Vector2i::new(self.position.x + self.size.x, self.position.y)
    }

    /// ### Gets the bottom-left corner of the rectangle.
    ///
    /// # Returns
    /// The bottom-left corner point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 20, 100, 50);
    /// assert_eq!(rect.get_bottom_left(), Vector2i::new(10, 70));
    /// ```
    #[inline]
    pub fn get_bottom_left(self) -> Vector2i {
        Vector2i::new(self.position.x, self.position.y + self.size.y)
    }

    /// ### Gets the bottom-right corner of the rectangle.
    ///
    /// # Returns
    /// The bottom-right corner point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 20, 100, 50);
    /// assert_eq!(rect.get_bottom_right(), Vector2i::new(110, 70));
    /// ```
    #[inline]
    pub fn get_bottom_right(self) -> Vector2i {
        Vector2i::new(self.position.x + self.size.x, self.position.y + self.size.y)
    }

    /// ### Gets the end point of the rectangle (bottom-right corner).
    ///
    /// This is an alias for `get_bottom_right()` for Godot compatibility.
    ///
    /// # Returns
    /// The end point (bottom-right corner).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 20, 100, 50);
    /// assert_eq!(rect.get_end(), Vector2i::new(110, 70));
    /// ```
    #[inline]
    pub fn get_end(self) -> Vector2i {
        self.get_bottom_right()
    }

    /// ### Checks if a point is inside the rectangle.
    ///
    /// # Arguments
    /// * `point` - The point to test
    ///
    /// # Returns
    /// `true` if the point is inside or on the boundary of the rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 20, 100, 50);
    /// assert!(rect.contains_point(Vector2i::new(50, 40)));
    /// assert!(!rect.contains_point(Vector2i::new(5, 40)));
    /// ```
    #[inline]
    pub fn contains_point(self, point: Vector2i) -> bool {
        point.x >= self.position.x
            && point.y >= self.position.y
            && point.x <= self.position.x + self.size.x
            && point.y <= self.position.y + self.size.y
    }

    /// ### Checks if this rectangle completely contains another rectangle.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to test
    ///
    /// # Returns
    /// `true` if this rectangle completely contains the other rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2i;
    /// let outer = Rect2i::new(0, 0, 100, 100);
    /// let inner = Rect2i::new(10, 10, 50, 50);
    /// assert!(outer.contains_rect(inner));
    /// assert!(!inner.contains_rect(outer));
    /// ```
    #[inline]
    pub fn contains_rect(self, other: Self) -> bool {
        other.position.x >= self.position.x
            && other.position.y >= self.position.y
            && other.get_end().x <= self.get_end().x
            && other.get_end().y <= self.get_end().y
    }

    /// ### Checks if this rectangle intersects with another rectangle.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to test
    ///
    /// # Returns
    /// `true` if the rectangles intersect or touch.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2i;
    /// let rect1 = Rect2i::new(0, 0, 50, 50);
    /// let rect2 = Rect2i::new(25, 25, 50, 50);
    /// assert!(rect1.intersects(rect2));
    /// ```
    #[inline]
    pub fn intersects(self, other: Self) -> bool {
        !(self.position.x > other.get_end().x
            || self.get_end().x < other.position.x
            || self.position.y > other.get_end().y
            || self.get_end().y < other.position.y)
    }

    /// ### Computes the intersection of this rectangle with another.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to intersect with
    ///
    /// # Returns
    /// The intersection rectangle, or a zero-sized rectangle if no intersection.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect1 = Rect2i::new(0, 0, 50, 50);
    /// let rect2 = Rect2i::new(25, 25, 50, 50);
    /// let intersection = rect1.intersection(rect2);
    /// assert_eq!(intersection.position, Vector2i::new(25, 25));
    /// assert_eq!(intersection.size, Vector2i::new(25, 25));
    /// ```
    #[inline]
    pub fn intersection(self, other: Self) -> Self {
        let x1 = self.position.x.max(other.position.x);
        let y1 = self.position.y.max(other.position.y);
        let x2 = self.get_end().x.min(other.get_end().x);
        let y2 = self.get_end().y.min(other.get_end().y);

        if x1 <= x2 && y1 <= y2 {
            Self::new(x1, y1, x2 - x1, y2 - y1)
        } else {
            Self::ZERO
        }
    }

    /// ### Computes the union of this rectangle with another.
    ///
    /// Creates the smallest rectangle that contains both rectangles.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to union with
    ///
    /// # Returns
    /// The union rectangle containing both rectangles.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect1 = Rect2i::new(0, 0, 50, 50);
    /// let rect2 = Rect2i::new(25, 25, 50, 50);
    /// let union = rect1.union(rect2);
    /// assert_eq!(union.position, Vector2i::new(0, 0));
    /// assert_eq!(union.size, Vector2i::new(75, 75));
    /// ```
    #[inline]
    pub fn union(self, other: Self) -> Self {
        let x1 = self.position.x.min(other.position.x);
        let y1 = self.position.y.min(other.position.y);
        let x2 = self.get_end().x.max(other.get_end().x);
        let y2 = self.get_end().y.max(other.get_end().y);

        Self::new(x1, y1, x2 - x1, y2 - y1)
    }

    /// ### Expands the rectangle by a specified amount in all directions.
    ///
    /// # Arguments
    /// * `amount` - The amount to expand by (positive values expand, negative shrink)
    ///
    /// # Returns
    /// A new expanded rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 10, 20, 20);
    /// let expanded = rect.expand(5);
    /// assert_eq!(expanded.position, Vector2i::new(5, 5));
    /// assert_eq!(expanded.size, Vector2i::new(30, 30));
    /// ```
    #[inline]
    pub fn expand(self, amount: i32) -> Self {
        Self::new(
            self.position.x - amount,
            self.position.y - amount,
            self.size.x + 2 * amount,
            self.size.y + 2 * amount,
        )
    }

    /// ### Expands the rectangle by different amounts on each side.
    ///
    /// # Arguments
    /// * `left` - Amount to expand leftward
    /// * `top` - Amount to expand upward
    /// * `right` - Amount to expand rightward
    /// * `bottom` - Amount to expand downward
    ///
    /// # Returns
    /// A new expanded rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 10, 20, 20);
    /// let expanded = rect.expand_individual(5, 3, 2, 4);
    /// assert_eq!(expanded.position, Vector2i::new(5, 7));
    /// assert_eq!(expanded.size, Vector2i::new(27, 27));
    /// ```
    #[inline]
    pub fn expand_individual(self, left: i32, top: i32, right: i32, bottom: i32) -> Self {
        Self::new(
            self.position.x - left,
            self.position.y - top,
            self.size.x + left + right,
            self.size.y + top + bottom,
        )
    }

    /// ### Expands the rectangle to include a point.
    ///
    /// # Arguments
    /// * `point` - The point to include
    ///
    /// # Returns
    /// A new rectangle that includes the original rectangle and the point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 10, 20, 20);
    /// let expanded = rect.expand_to_point(Vector2i::new(5, 35));
    /// assert_eq!(expanded.position, Vector2i::new(5, 10));
    /// assert_eq!(expanded.size, Vector2i::new(25, 25));
    /// ```
    #[inline]
    pub fn expand_to_point(self, point: Vector2i) -> Self {
        let min_x = self.position.x.min(point.x);
        let min_y = self.position.y.min(point.y);
        let max_x = self.get_end().x.max(point.x);
        let max_y = self.get_end().y.max(point.y);

        Self::new(min_x, min_y, max_x - min_x, max_y - min_y)
    }

    /// ### Checks if this rectangle is approximately equal to another.
    ///
    /// For integer rectangles, this is equivalent to exact equality.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to compare with
    ///
    /// # Returns
    /// `true` if the rectangles are equal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2i;
    /// let rect1 = Rect2i::new(1, 2, 3, 4);
    /// let rect2 = Rect2i::new(1, 2, 3, 4);
    /// assert!(rect1.is_equal_approx(rect2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Self) -> bool {
        self == other
    }

    /// ### Checks if this rectangle has a valid (non-negative) size.
    ///
    /// # Returns
    /// `true` if both width and height are non-negative.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2i;
    /// let valid = Rect2i::new(0, 0, 10, 20);
    /// let invalid = Rect2i::new(0, 0, -10, 20);
    /// assert!(valid.has_valid_size());
    /// assert!(!invalid.has_valid_size());
    /// ```
    #[inline]
    pub fn has_valid_size(self) -> bool {
        self.size.x >= 0 && self.size.y >= 0
    }

    /// ### Returns the absolute rectangle (ensures positive size).
    ///
    /// Adjusts position and size to ensure the size is positive.
    ///
    /// # Returns
    /// A rectangle with positive size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2i};
    /// let rect = Rect2i::new(10, 10, -5, -3);
    /// let abs_rect = rect.abs();
    /// assert_eq!(abs_rect.position, Vector2i::new(5, 7));
    /// assert_eq!(abs_rect.size, Vector2i::new(5, 3));
    /// ```
    #[inline]
    pub fn abs(self) -> Self {
        let mut result = self;
        if result.size.x < 0 {
            result.position.x += result.size.x;
            result.size.x = -result.size.x;
        }
        if result.size.y < 0 {
            result.position.y += result.size.y;
            result.size.y = -result.size.y;
        }
        result
    }

    /// ### Converts this Rect2i to a Rect2 with floating-point components.
    ///
    /// # Returns
    /// A new Rect2 with floating-point components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2i, Vector2};
    /// let rect_i = Rect2i::new(10, 20, 100, 50);
    /// let rect_f = rect_i.to_rect2();
    /// assert_eq!(rect_f.position, Vector2::new(10.0, 20.0));
    /// assert_eq!(rect_f.size, Vector2::new(100.0, 50.0));
    /// ```
    #[inline]
    pub fn to_rect2(self) -> super::Rect2 {
        super::Rect2::new(
            self.position.x as f32,
            self.position.y as f32,
            self.size.x as f32,
            self.size.y as f32,
        )
    }
}

impl fmt::Display for Rect2i {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[P: {}, S: {}]", self.position, self.size)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use super::super::Vector2;

    #[test]
    fn test_rect2i_creation() {
        let rect = Rect2i::new(10, 20, 100, 50);
        assert_eq!(rect.position, Vector2i::new(10, 20));
        assert_eq!(rect.size, Vector2i::new(100, 50));
    }

    #[test]
    fn test_rect2i_from_position_size() {
        let position = Vector2i::new(10, 20);
        let size = Vector2i::new(100, 50);
        let rect = Rect2i::from_position_size(position, size);
        assert_eq!(rect.position, position);
        assert_eq!(rect.size, size);
    }

    #[test]
    fn test_rect2i_from_corners() {
        let rect = Rect2i::from_corners(
            Vector2i::new(50, 70),  // bottom-right
            Vector2i::new(10, 20)   // top-left
        );
        assert_eq!(rect.position, Vector2i::new(10, 20));
        assert_eq!(rect.size, Vector2i::new(40, 50));
    }

    #[test]
    fn test_rect2i_from_center_size() {
        let rect = Rect2i::from_center_size(
            Vector2i::new(50, 50),
            Vector2i::new(20, 10)
        );
        assert_eq!(rect.position, Vector2i::new(40, 45));
        assert_eq!(rect.size, Vector2i::new(20, 10));
    }

    #[test]
    fn test_rect2i_property_accessors() {
        let rect = Rect2i::new(10, 20, 100, 50);

        assert_eq!(rect.get_center(), Vector2i::new(60, 45));
        assert_eq!(rect.get_area(), 5000);
        assert_eq!(rect.get_perimeter(), 300);

        assert_eq!(rect.get_top_left(), Vector2i::new(10, 20));
        assert_eq!(rect.get_top_right(), Vector2i::new(110, 20));
        assert_eq!(rect.get_bottom_left(), Vector2i::new(10, 70));
        assert_eq!(rect.get_bottom_right(), Vector2i::new(110, 70));
        assert_eq!(rect.get_end(), Vector2i::new(110, 70));
    }

    #[test]
    fn test_rect2i_contains_point() {
        let rect = Rect2i::new(10, 20, 100, 50);

        assert!(rect.contains_point(Vector2i::new(50, 40))); // inside
        assert!(rect.contains_point(Vector2i::new(10, 20))); // top-left corner
        assert!(rect.contains_point(Vector2i::new(110, 70))); // bottom-right corner
        assert!(!rect.contains_point(Vector2i::new(5, 40))); // outside left
        assert!(!rect.contains_point(Vector2i::new(50, 15))); // outside top
    }

    #[test]
    fn test_rect2i_contains_rect() {
        let outer = Rect2i::new(0, 0, 100, 100);
        let inner = Rect2i::new(10, 10, 50, 50);
        let overlapping = Rect2i::new(50, 50, 100, 100);

        assert!(outer.contains_rect(inner));
        assert!(!inner.contains_rect(outer));
        assert!(!outer.contains_rect(overlapping));
    }

    #[test]
    fn test_rect2i_intersects() {
        let rect1 = Rect2i::new(0, 0, 50, 50);
        let rect2 = Rect2i::new(25, 25, 50, 50); // overlapping
        let rect3 = Rect2i::new(100, 100, 50, 50); // separate

        assert!(rect1.intersects(rect2));
        assert!(!rect1.intersects(rect3));
    }

    #[test]
    fn test_rect2i_intersection() {
        let rect1 = Rect2i::new(0, 0, 50, 50);
        let rect2 = Rect2i::new(25, 25, 50, 50);
        let intersection = rect1.intersection(rect2);

        assert_eq!(intersection.position, Vector2i::new(25, 25));
        assert_eq!(intersection.size, Vector2i::new(25, 25));

        // No intersection case
        let rect3 = Rect2i::new(100, 100, 50, 50);
        let no_intersection = rect1.intersection(rect3);
        assert_eq!(no_intersection, Rect2i::ZERO);
    }

    #[test]
    fn test_rect2i_union() {
        let rect1 = Rect2i::new(0, 0, 50, 50);
        let rect2 = Rect2i::new(25, 25, 50, 50);
        let union = rect1.union(rect2);

        assert_eq!(union.position, Vector2i::new(0, 0));
        assert_eq!(union.size, Vector2i::new(75, 75));
    }

    #[test]
    fn test_rect2i_expand() {
        let rect = Rect2i::new(10, 10, 20, 20);
        let expanded = rect.expand(5);

        assert_eq!(expanded.position, Vector2i::new(5, 5));
        assert_eq!(expanded.size, Vector2i::new(30, 30));
    }

    #[test]
    fn test_rect2i_expand_individual() {
        let rect = Rect2i::new(10, 10, 20, 20);
        let expanded = rect.expand_individual(5, 3, 2, 4);

        assert_eq!(expanded.position, Vector2i::new(5, 7));
        assert_eq!(expanded.size, Vector2i::new(27, 27));
    }

    #[test]
    fn test_rect2i_expand_to_point() {
        let rect = Rect2i::new(10, 10, 20, 20);
        let expanded = rect.expand_to_point(Vector2i::new(5, 35));

        assert_eq!(expanded.position, Vector2i::new(5, 10));
        assert_eq!(expanded.size, Vector2i::new(25, 25));
    }

    #[test]
    fn test_rect2i_utility_methods() {
        let rect1 = Rect2i::new(1, 2, 3, 4);
        let rect2 = Rect2i::new(1, 2, 3, 4);
        assert!(rect1.is_equal_approx(rect2));

        assert!(rect1.has_valid_size());

        let invalid = Rect2i::new(0, 0, -10, 20);
        assert!(!invalid.has_valid_size());
    }

    #[test]
    fn test_rect2i_abs() {
        let rect = Rect2i::new(10, 10, -5, -3);
        let abs_rect = rect.abs();

        assert_eq!(abs_rect.position, Vector2i::new(5, 7));
        assert_eq!(abs_rect.size, Vector2i::new(5, 3));
    }

    #[test]
    fn test_rect2i_conversion() {
        let rect_i = Rect2i::new(10, 20, 100, 50);
        let rect_f = rect_i.to_rect2();

        assert_eq!(rect_f.position, Vector2::new(10.0, 20.0));
        assert_eq!(rect_f.size, Vector2::new(100.0, 50.0));

        // Test round-trip conversion
        let rect_i_back = rect_f.to_rect2i();
        assert_eq!(rect_i_back, rect_i);
    }

    #[test]
    fn test_rect2i_constants() {
        assert_eq!(Rect2i::ZERO.position, Vector2i::ZERO);
        assert_eq!(Rect2i::ZERO.size, Vector2i::ZERO);
    }
}
