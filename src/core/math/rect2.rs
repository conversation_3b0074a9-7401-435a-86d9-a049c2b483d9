use std::fmt;
use super::Vector2;

/// ### 2D Floating-Point Rectangle
///
/// Rect2 represents a 2D rectangle with floating-point precision, defined by
/// a position (top-left corner) and size (width and height). This structure
/// is compatible with <PERSON><PERSON>'s Rect2 and provides comprehensive rectangle
/// operations for collision detection, UI layout, and geometric calculations.
///
/// The rectangle is defined as:
/// - `position`: The top-left corner coordinates
/// - `size`: The width and height dimensions
///
/// # Examples
/// ```
/// # use verturion::core::math::{Rect2, Vector2};
/// // Create a rectangle at (10, 20) with size 100x50
/// let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
///
/// // Check if a point is inside the rectangle
/// let point = Vector2::new(50.0, 40.0);
/// assert!(rect.contains_point(point));
///
/// // Get the center of the rectangle
/// let center = rect.get_center();
/// assert_eq!(center, Vector2::new(60.0, 45.0));
/// ```
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct Rect2 {
    /// The position of the rectangle (top-left corner)
    pub position: Vector2,
    /// The size of the rectangle (width and height)
    pub size: Vector2,
}

impl Rect2 {
    /// ### Empty rectangle at origin with zero size.
    /// 
    /// # Returns
    /// An empty rectangle at the origin **(0.0, 0.0)** with zero size **(0.0, 0.0)**.
    pub const ZERO: Rect2 = Rect2 {
        position: Vector2::ZERO,
        size: Vector2::ZERO,
    };

    /// ### Creates a new Rect2 from position and size components.
    ///
    /// # Arguments
    /// * `x` - X coordinate of the top-left corner
    /// * `y` - Y coordinate of the top-left corner
    /// * `width` - Width of the rectangle
    /// * `height` - Height of the rectangle
    ///
    /// # Returns
    /// A new Rect2 with the specified position and size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2;
    /// let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
    /// assert_eq!(rect.position.x, 10.0);
    /// assert_eq!(rect.position.y, 20.0);
    /// assert_eq!(rect.size.x, 100.0);
    /// assert_eq!(rect.size.y, 50.0);
    /// ```
    #[inline]
    pub const fn new(x: f32, y: f32, width: f32, height: f32) -> Self {
        Self {
            position: Vector2::new(x, y),
            size: Vector2::new(width, height),
        }
    }

    /// ### Creates a new Rect2 from position and size vectors.
    ///
    /// # Arguments
    /// * `position` - The position vector (top-left corner)
    /// * `size` - The size vector (width and height)
    ///
    /// # Returns
    /// A new Rect2 with the specified position and size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let position = Vector2::new(10.0, 20.0);
    /// let size = Vector2::new(100.0, 50.0);
    /// let rect = Rect2::from_position_size(position, size);
    /// ```
    #[inline]
    pub const fn from_position_size(position: Vector2, size: Vector2) -> Self {
        Self { position, size }
    }

    /// ### Creates a new Rect2 from two corner points.
    ///
    /// Automatically determines the correct position and size from any two
    /// corner points, handling cases where points are not top-left and bottom-right.
    ///
    /// # Arguments
    /// * `point1` - First corner point
    /// * `point2` - Second corner point (diagonal to first)
    ///
    /// # Returns
    /// A new Rect2 encompassing both points.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::from_corners(
    ///     Vector2::new(50.0, 70.0),  // bottom-right
    ///     Vector2::new(10.0, 20.0)   // top-left
    /// );
    /// assert_eq!(rect.position, Vector2::new(10.0, 20.0));
    /// assert_eq!(rect.size, Vector2::new(40.0, 50.0));
    /// ```
    #[inline]
    pub fn from_corners(point1: Vector2, point2: Vector2) -> Self {
        let min_x = point1.x.min(point2.x);
        let min_y = point1.y.min(point2.y);
        let max_x = point1.x.max(point2.x);
        let max_y = point1.y.max(point2.y);

        Self {
            position: Vector2::new(min_x, min_y),
            size: Vector2::new(max_x - min_x, max_y - min_y),
        }
    }
    /// ### Creates a Rect2 centered at a specific point.
    ///
    /// # Arguments
    /// * `center` - The center point of the rectangle
    /// * `size` - The size of the rectangle
    ///
    /// # Returns
    /// A new Rect2 centered at the specified point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::from_center_size(
    ///     Vector2::new(50.0, 50.0),
    ///     Vector2::new(20.0, 10.0)
    /// );
    /// assert_eq!(rect.position, Vector2::new(40.0, 45.0));
    /// ```
    #[inline]
    pub fn from_center_size(center: Vector2, size: Vector2) -> Self {
        Self {
            position: center - size * 0.5,
            size,
        }
    }
    /// ### Gets the center point of the rectangle.
    ///
    /// # Returns
    /// The center point as a Vector2.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
    /// let center = rect.get_center();
    /// assert_eq!(center, Vector2::new(60.0, 45.0));
    /// ```
    #[inline]
    pub fn get_center(self) -> Vector2 {
        self.position + self.size * 0.5
    }

    /// ### Gets the area of the rectangle.
    ///
    /// # Returns
    /// The area (width × height) of the rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2;
    /// let rect = Rect2::new(0.0, 0.0, 10.0, 5.0);
    /// assert_eq!(rect.get_area(), 50.0);
    /// ```
    #[inline]
    pub fn get_area(self) -> f32 {
        self.size.x * self.size.y
    }

    /// ### Gets the perimeter of the rectangle.
    ///
    /// # Returns
    /// The perimeter (2 × width + 2 × height) of the rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2;
    /// let rect = Rect2::new(0.0, 0.0, 10.0, 5.0);
    /// assert_eq!(rect.get_perimeter(), 30.0);
    /// ```
    #[inline]
    pub fn get_perimeter(self) -> f32 {
        2.0 * (self.size.x + self.size.y)
    }

    /// ### Gets the top-left corner of the rectangle.
    ///
    /// # Returns
    /// The top-left corner point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
    /// assert_eq!(rect.get_top_left(), Vector2::new(10.0, 20.0));
    /// ```
    #[inline]
    pub fn get_top_left(self) -> Vector2 {
        self.position
    }

    /// ### Gets the top-right corner of the rectangle.
    ///
    /// # Returns
    /// The top-right corner point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
    /// assert_eq!(rect.get_top_right(), Vector2::new(110.0, 20.0));
    /// ```
    #[inline]
    pub fn get_top_right(self) -> Vector2 {
        Vector2::new(self.position.x + self.size.x, self.position.y)
    }

    /// ### Gets the bottom-left corner of the rectangle.
    ///
    /// # Returns
    /// The bottom-left corner point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
    /// assert_eq!(rect.get_bottom_left(), Vector2::new(10.0, 70.0));
    /// ```
    #[inline]
    pub fn get_bottom_left(self) -> Vector2 {
        Vector2::new(self.position.x, self.position.y + self.size.y)
    }

    /// ### Gets the bottom-right corner of the rectangle.
    ///
    /// # Returns
    /// The bottom-right corner point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
    /// assert_eq!(rect.get_bottom_right(), Vector2::new(110.0, 70.0));
    /// ```
    #[inline]
    pub fn get_bottom_right(self) -> Vector2 {
        self.position + self.size
    }

    /// ### Gets the end point of the rectangle (bottom-right corner).
    ///
    /// This is an alias for `get_bottom_right()` for Godot compatibility.
    ///
    /// # Returns
    /// The end point (bottom-right corner).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
    /// assert_eq!(rect.get_end(), Vector2::new(110.0, 70.0));
    /// ```
    #[inline]
    pub fn get_end(self) -> Vector2 {
        self.get_bottom_right()
    }
    /// ### Checks if a point is inside the rectangle.
    ///
    /// # Arguments
    /// * `point` - The point to test
    ///
    /// # Returns
    /// `true` if the point is inside or on the boundary of the rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
    /// assert!(rect.contains_point(Vector2::new(50.0, 40.0)));
    /// assert!(!rect.contains_point(Vector2::new(5.0, 40.0)));
    /// ```
    #[inline]
    pub fn contains_point(self, point: Vector2) -> bool {
        point.x >= self.position.x
            && point.y >= self.position.y
            && point.x <= self.position.x + self.size.x
            && point.y <= self.position.y + self.size.y
    }

    /// ### Checks if this rectangle completely contains another rectangle.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to test
    ///
    /// # Returns
    /// `true` if this rectangle completely contains the other rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2;
    /// let outer = Rect2::new(0.0, 0.0, 100.0, 100.0);
    /// let inner = Rect2::new(10.0, 10.0, 50.0, 50.0);
    /// assert!(outer.contains_rect(inner));
    /// assert!(!inner.contains_rect(outer));
    /// ```
    #[inline]
    pub fn contains_rect(self, other: Self) -> bool {
        other.position.x >= self.position.x
            && other.position.y >= self.position.y
            && other.get_end().x <= self.get_end().x
            && other.get_end().y <= self.get_end().y
    }

    /// ### Checks if this rectangle intersects with another rectangle.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to test
    ///
    /// # Returns
    /// `true` if the rectangles intersect or touch.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2;
    /// let rect1 = Rect2::new(0.0, 0.0, 50.0, 50.0);
    /// let rect2 = Rect2::new(25.0, 25.0, 50.0, 50.0);
    /// assert!(rect1.intersects(rect2));
    /// ```
    #[inline]
    pub fn intersects(self, other: Self) -> bool {
        !(self.position.x > other.get_end().x
            || self.get_end().x < other.position.x
            || self.position.y > other.get_end().y
            || self.get_end().y < other.position.y)
    }

    /// ### Computes the intersection of this rectangle with another.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to intersect with
    ///
    /// # Returns
    /// The intersection rectangle, or a zero-sized rectangle if no intersection.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect1 = Rect2::new(0.0, 0.0, 50.0, 50.0);
    /// let rect2 = Rect2::new(25.0, 25.0, 50.0, 50.0);
    /// let intersection = rect1.intersection(rect2);
    /// assert_eq!(intersection.position, Vector2::new(25.0, 25.0));
    /// assert_eq!(intersection.size, Vector2::new(25.0, 25.0));
    /// ```
    #[inline]
    pub fn intersection(self, other: Self) -> Self {
        let x1 = self.position.x.max(other.position.x);
        let y1 = self.position.y.max(other.position.y);
        let x2 = self.get_end().x.min(other.get_end().x);
        let y2 = self.get_end().y.min(other.get_end().y);

        if x1 <= x2 && y1 <= y2 {
            Self::new(x1, y1, x2 - x1, y2 - y1)
        } else {
            Self::ZERO
        }
    }

    /// ### Computes the union of this rectangle with another.
    ///
    /// Creates the smallest rectangle that contains both rectangles.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to union with
    ///
    /// # Returns
    /// The union rectangle containing both rectangles.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect1 = Rect2::new(0.0, 0.0, 50.0, 50.0);
    /// let rect2 = Rect2::new(25.0, 25.0, 50.0, 50.0);
    /// let union = rect1.union(rect2);
    /// assert_eq!(union.position, Vector2::new(0.0, 0.0));
    /// assert_eq!(union.size, Vector2::new(75.0, 75.0));
    /// ```
    #[inline]
    pub fn union(self, other: Self) -> Self {
        let x1 = self.position.x.min(other.position.x);
        let y1 = self.position.y.min(other.position.y);
        let x2 = self.get_end().x.max(other.get_end().x);
        let y2 = self.get_end().y.max(other.get_end().y);

        Self::new(x1, y1, x2 - x1, y2 - y1)
    }

    /// ### Expands the rectangle by a specified amount in all directions.
    ///
    /// # Arguments
    /// * `amount` - The amount to expand by (positive values expand, negative shrink)
    ///
    /// # Returns
    /// A new expanded rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 10.0, 20.0, 20.0);
    /// let expanded = rect.expand(5.0);
    /// assert_eq!(expanded.position, Vector2::new(5.0, 5.0));
    /// assert_eq!(expanded.size, Vector2::new(30.0, 30.0));
    /// ```
    #[inline]
    pub fn expand(self, amount: f32) -> Self {
        Self::new(
            self.position.x - amount,
            self.position.y - amount,
            self.size.x + 2.0 * amount,
            self.size.y + 2.0 * amount,
        )
    }

    /// ### Expands the rectangle by different amounts on each side.
    ///
    /// # Arguments
    /// * `left` - Amount to expand leftward
    /// * `top` - Amount to expand upward
    /// * `right` - Amount to expand rightward
    /// * `bottom` - Amount to expand downward
    ///
    /// # Returns
    /// A new expanded rectangle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 10.0, 20.0, 20.0);
    /// let expanded = rect.expand_individual(5.0, 3.0, 2.0, 4.0);
    /// assert_eq!(expanded.position, Vector2::new(5.0, 7.0));
    /// assert_eq!(expanded.size, Vector2::new(27.0, 27.0));
    /// ```
    #[inline]
    pub fn expand_individual(self, left: f32, top: f32, right: f32, bottom: f32) -> Self {
        Self::new(
            self.position.x - left,
            self.position.y - top,
            self.size.x + left + right,
            self.size.y + top + bottom,
        )
    }

    /// ### Expands the rectangle to include a point.
    ///
    /// # Arguments
    /// * `point` - The point to include
    ///
    /// # Returns
    /// A new rectangle that includes the original rectangle and the point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 10.0, 20.0, 20.0);
    /// let expanded = rect.expand_to_point(Vector2::new(5.0, 35.0));
    /// assert_eq!(expanded.position, Vector2::new(5.0, 10.0));
    /// assert_eq!(expanded.size, Vector2::new(25.0, 25.0));
    /// ```
    #[inline]
    pub fn expand_to_point(self, point: Vector2) -> Self {
        let min_x = self.position.x.min(point.x);
        let min_y = self.position.y.min(point.y);
        let max_x = self.get_end().x.max(point.x);
        let max_y = self.get_end().y.max(point.y);

        Self::new(min_x, min_y, max_x - min_x, max_y - min_y)
    }

    /// ### Checks if this rectangle is approximately equal to another.
    ///
    /// Uses epsilon comparison for floating-point components.
    ///
    /// # Arguments
    /// * `other` - The other rectangle to compare with
    ///
    /// # Returns
    /// `true` if the rectangles are approximately equal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect1 = Rect2::new(1.0, 2.0, 3.0, 4.0);
    /// let rect2 = Rect2::new(1.0000001, 2.0000001, 3.0000001, 4.0000001);
    /// assert!(rect1.is_equal_approx(rect2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Self) -> bool {
        self.position.is_equal_approx(other.position) && self.size.is_equal_approx(other.size)
    }

    /// ### Checks if this rectangle is finite.
    ///
    /// Returns `true` if all components are finite (not NaN or infinite).
    ///
    /// # Returns
    /// `true` if the rectangle is finite.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2;
    /// let rect = Rect2::new(1.0, 2.0, 3.0, 4.0);
    /// assert!(rect.is_finite());
    /// ```
    #[inline]
    pub fn is_finite(self) -> bool {
        self.position.is_finite() && self.size.is_finite()
    }

    /// ### Checks if this rectangle has a valid (non-negative) size.
    ///
    /// # Returns
    /// `true` if both width and height are non-negative.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Rect2;
    /// let valid = Rect2::new(0.0, 0.0, 10.0, 20.0);
    /// let invalid = Rect2::new(0.0, 0.0, -10.0, 20.0);
    /// assert!(valid.has_valid_size());
    /// assert!(!invalid.has_valid_size());
    /// ```
    #[inline]
    pub fn has_valid_size(self) -> bool {
        self.size.x >= 0.0 && self.size.y >= 0.0
    }

    /// ### Returns the absolute rectangle (ensures positive size).
    ///
    /// Adjusts position and size to ensure the size is positive.
    ///
    /// # Returns
    /// A rectangle with positive size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.0, 10.0, -5.0, -3.0);
    /// let abs_rect = rect.abs();
    /// assert_eq!(abs_rect.position, Vector2::new(5.0, 7.0));
    /// assert_eq!(abs_rect.size, Vector2::new(5.0, 3.0));
    /// ```
    #[inline]
    pub fn abs(self) -> Self {
        let mut result = self;
        if result.size.x < 0.0 {
            result.position.x += result.size.x;
            result.size.x = -result.size.x;
        }
        if result.size.y < 0.0 {
            result.position.y += result.size.y;
            result.size.y = -result.size.y;
        }
        result
    }

    /// ### Converts this Rect2 to a Rect2i by truncating components.
    ///
    /// # Returns
    /// A new Rect2i with integer components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Rect2, Vector2};
    /// let rect = Rect2::new(10.7, 20.3, 100.9, 50.1);
    /// let rect_i = rect.to_rect2i();
    /// // Note: This would require Rect2i to be implemented
    /// ```
    #[inline]
    pub fn to_rect2i(self) -> super::Rect2i {
        super::Rect2i::new(
            self.position.x as i32,
            self.position.y as i32,
            self.size.x as i32,
            self.size.y as i32,
        )
    }
}

impl fmt::Display for Rect2 {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[P: {}, S: {}]", self.position, self.size)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rect2_creation() {
        let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);
        assert_eq!(rect.position, Vector2::new(10.0, 20.0));
        assert_eq!(rect.size, Vector2::new(100.0, 50.0));
    }

    #[test]
    fn test_rect2_from_position_size() {
        let position = Vector2::new(10.0, 20.0);
        let size = Vector2::new(100.0, 50.0);
        let rect = Rect2::from_position_size(position, size);
        assert_eq!(rect.position, position);
        assert_eq!(rect.size, size);
    }

    #[test]
    fn test_rect2_from_corners() {
        let rect = Rect2::from_corners(
            Vector2::new(50.0, 70.0),  // bottom-right
            Vector2::new(10.0, 20.0)   // top-left
        );
        assert_eq!(rect.position, Vector2::new(10.0, 20.0));
        assert_eq!(rect.size, Vector2::new(40.0, 50.0));
    }

    #[test]
    fn test_rect2_from_center_size() {
        let rect = Rect2::from_center_size(
            Vector2::new(50.0, 50.0),
            Vector2::new(20.0, 10.0)
        );
        assert_eq!(rect.position, Vector2::new(40.0, 45.0));
        assert_eq!(rect.size, Vector2::new(20.0, 10.0));
    }

    #[test]
    fn test_rect2_property_accessors() {
        let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);

        assert_eq!(rect.get_center(), Vector2::new(60.0, 45.0));
        assert_eq!(rect.get_area(), 5000.0);
        assert_eq!(rect.get_perimeter(), 300.0);

        assert_eq!(rect.get_top_left(), Vector2::new(10.0, 20.0));
        assert_eq!(rect.get_top_right(), Vector2::new(110.0, 20.0));
        assert_eq!(rect.get_bottom_left(), Vector2::new(10.0, 70.0));
        assert_eq!(rect.get_bottom_right(), Vector2::new(110.0, 70.0));
        assert_eq!(rect.get_end(), Vector2::new(110.0, 70.0));
    }

    #[test]
    fn test_rect2_contains_point() {
        let rect = Rect2::new(10.0, 20.0, 100.0, 50.0);

        assert!(rect.contains_point(Vector2::new(50.0, 40.0))); // inside
        assert!(rect.contains_point(Vector2::new(10.0, 20.0))); // top-left corner
        assert!(rect.contains_point(Vector2::new(110.0, 70.0))); // bottom-right corner
        assert!(!rect.contains_point(Vector2::new(5.0, 40.0))); // outside left
        assert!(!rect.contains_point(Vector2::new(50.0, 15.0))); // outside top
    }

    #[test]
    fn test_rect2_contains_rect() {
        let outer = Rect2::new(0.0, 0.0, 100.0, 100.0);
        let inner = Rect2::new(10.0, 10.0, 50.0, 50.0);
        let overlapping = Rect2::new(50.0, 50.0, 100.0, 100.0);

        assert!(outer.contains_rect(inner));
        assert!(!inner.contains_rect(outer));
        assert!(!outer.contains_rect(overlapping));
    }

    #[test]
    fn test_rect2_intersects() {
        let rect1 = Rect2::new(0.0, 0.0, 50.0, 50.0);
        let rect2 = Rect2::new(25.0, 25.0, 50.0, 50.0); // overlapping
        let rect3 = Rect2::new(100.0, 100.0, 50.0, 50.0); // separate

        assert!(rect1.intersects(rect2));
        assert!(!rect1.intersects(rect3));
    }

    #[test]
    fn test_rect2_intersection() {
        let rect1 = Rect2::new(0.0, 0.0, 50.0, 50.0);
        let rect2 = Rect2::new(25.0, 25.0, 50.0, 50.0);
        let intersection = rect1.intersection(rect2);

        assert_eq!(intersection.position, Vector2::new(25.0, 25.0));
        assert_eq!(intersection.size, Vector2::new(25.0, 25.0));

        // No intersection case
        let rect3 = Rect2::new(100.0, 100.0, 50.0, 50.0);
        let no_intersection = rect1.intersection(rect3);
        assert_eq!(no_intersection, Rect2::ZERO);
    }

    #[test]
    fn test_rect2_union() {
        let rect1 = Rect2::new(0.0, 0.0, 50.0, 50.0);
        let rect2 = Rect2::new(25.0, 25.0, 50.0, 50.0);
        let union = rect1.union(rect2);

        assert_eq!(union.position, Vector2::new(0.0, 0.0));
        assert_eq!(union.size, Vector2::new(75.0, 75.0));
    }

    #[test]
    fn test_rect2_expand() {
        let rect = Rect2::new(10.0, 10.0, 20.0, 20.0);
        let expanded = rect.expand(5.0);

        assert_eq!(expanded.position, Vector2::new(5.0, 5.0));
        assert_eq!(expanded.size, Vector2::new(30.0, 30.0));
    }

    #[test]
    fn test_rect2_expand_individual() {
        let rect = Rect2::new(10.0, 10.0, 20.0, 20.0);
        let expanded = rect.expand_individual(5.0, 3.0, 2.0, 4.0);

        assert_eq!(expanded.position, Vector2::new(5.0, 7.0));
        assert_eq!(expanded.size, Vector2::new(27.0, 27.0));
    }

    #[test]
    fn test_rect2_expand_to_point() {
        let rect = Rect2::new(10.0, 10.0, 20.0, 20.0);
        let expanded = rect.expand_to_point(Vector2::new(5.0, 35.0));

        assert_eq!(expanded.position, Vector2::new(5.0, 10.0));
        assert_eq!(expanded.size, Vector2::new(25.0, 25.0));
    }

    #[test]
    fn test_rect2_utility_methods() {
        let rect1 = Rect2::new(1.0, 2.0, 3.0, 4.0);
        let rect2 = Rect2::new(1.0000001, 2.0000001, 3.0000001, 4.0000001);
        assert!(rect1.is_equal_approx(rect2));

        assert!(rect1.is_finite());
        assert!(rect1.has_valid_size());

        let invalid = Rect2::new(0.0, 0.0, -10.0, 20.0);
        assert!(!invalid.has_valid_size());
    }

    #[test]
    fn test_rect2_abs() {
        let rect = Rect2::new(10.0, 10.0, -5.0, -3.0);
        let abs_rect = rect.abs();

        assert_eq!(abs_rect.position, Vector2::new(5.0, 7.0));
        assert_eq!(abs_rect.size, Vector2::new(5.0, 3.0));
    }

    #[test]
    fn test_rect2_constants() {
        assert_eq!(Rect2::ZERO.position, Vector2::ZERO);
        assert_eq!(Rect2::ZERO.size, Vector2::ZERO);
    }
}
