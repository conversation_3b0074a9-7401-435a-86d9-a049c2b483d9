//! Comprehensive 3D plane implementation for spatial calculations and collision detection.
//!
//! This module provides a complete Plane implementation representing infinite planes in 3D space
//! using the standard plane equation ax + by + cz + d = 0, commonly used in 3D graphics,
//! collision detection, and spatial partitioning algorithms.

use std::fmt;
use super::Vector3;

/// ### A 3D plane for spatial calculations and collision detection.
///
/// Plane represents an infinite plane in 3D space using the standard plane equation:
/// ax + by + cz + d = 0, where (a, b, c) is the normal vector and d is the distance
/// from the origin to the plane along the normal.
///
/// ## Mathematical Representation
///
/// A plane is defined by:
/// - **Normal vector**: (a, b, c) - perpendicular to the plane surface
/// - **Distance**: d - signed distance from origin to plane
/// - **Plane equation**: normal · point + d = 0
///
/// ## Use Cases
///
/// Plane is ideal for:
/// - **Collision Detection**: Point-plane, ray-plane intersections
/// - **Spatial Partitioning**: BSP trees, frustum culling
/// - **Clipping**: 3D rendering pipeline clipping operations
/// - **Physics**: Ground planes, wall collision detection
/// - **Level Design**: Floor, wall, and ceiling definitions
///
/// # Examples
/// ```
/// # use verturion::core::math::{Plane, Vector3};
/// // Create a horizontal plane at y = 0 (ground plane)
/// let ground = Plane::new(Vector3::UP, 0.0);
///
/// // Create plane from three points
/// let p1 = Vector3::new(0.0, 0.0, 0.0);
/// let p2 = Vector3::new(1.0, 0.0, 0.0);
/// let p3 = Vector3::new(0.0, 1.0, 0.0);
/// let plane = Plane::from_points(p1, p2, p3);
///
/// // Test point distance to plane
/// let point = Vector3::new(0.0, 5.0, 0.0);
/// let distance = ground.distance_to(point);
/// ```
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct Plane {
    /// The normal vector of the plane (should be normalized)
    pub normal: Vector3,
    /// The signed distance from the origin to the plane
    pub d: f32,
}

impl Plane {
    /// ### Creates a new plane with the specified normal and distance.
    ///
    /// # Parameters
    /// - `normal`: The normal vector of the plane (should be normalized)
    /// - `d`: The signed distance from the origin to the plane
    ///
    /// # Returns
    /// A new plane with the given normal and distance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// // Create a horizontal plane at y = 5
    /// let plane = Plane::new(Vector3::UP, -5.0);
    /// ```
    #[inline]
    pub const fn new(normal: Vector3, d: f32) -> Self {
        Self { normal, d }
    }

    /// ### Creates a plane from three points.
    ///
    /// Calculates the plane that passes through the three given points.
    /// The normal is computed using the cross product of two edge vectors.
    ///
    /// # Parameters
    /// - `p1`: First point on the plane
    /// - `p2`: Second point on the plane
    /// - `p3`: Third point on the plane
    ///
    /// # Returns
    /// A plane passing through the three points, or a degenerate plane if points are collinear.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// let p1 = Vector3::new(0.0, 0.0, 0.0);
    /// let p2 = Vector3::new(1.0, 0.0, 0.0);
    /// let p3 = Vector3::new(0.0, 1.0, 0.0);
    /// let plane = Plane::from_points(p1, p2, p3);
    /// ```
    #[inline]
    pub fn from_points(p1: Vector3, p2: Vector3, p3: Vector3) -> Self {
        let edge1 = p2 - p1;
        let edge2 = p3 - p1;
        let normal = edge1.cross(edge2).normalized();
        let d = -normal.dot(p1);
        Self::new(normal, d)
    }

    /// ### Creates a plane from a point and normal vector.
    ///
    /// Creates a plane that passes through the given point with the specified normal.
    ///
    /// # Parameters
    /// - `point`: A point on the plane
    /// - `normal`: The normal vector of the plane (will be normalized)
    ///
    /// # Returns
    /// A plane passing through the point with the given normal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// let point = Vector3::new(0.0, 5.0, 0.0);
    /// let normal = Vector3::UP;
    /// let plane = Plane::from_point_normal(point, normal);
    /// ```
    #[inline]
    pub fn from_point_normal(point: Vector3, normal: Vector3) -> Self {
        let normalized_normal = normal.normalized();
        let d = -normalized_normal.dot(point);
        Self::new(normalized_normal, d)
    }

    /// ### Calculates the signed distance from a point to the plane.
    ///
    /// Returns the perpendicular distance from the point to the plane.
    /// Positive values indicate the point is on the side of the normal,
    /// negative values indicate the opposite side.
    ///
    /// # Parameters
    /// - `point`: The point to test
    ///
    /// # Returns
    /// The signed distance from the point to the plane.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// let plane = Plane::new(Vector3::UP, 0.0);
    /// let point = Vector3::new(0.0, 5.0, 0.0);
    /// let distance = plane.distance_to(point);
    /// assert!((distance - 5.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn distance_to(self, point: Vector3) -> f32 {
        self.normal.dot(point) + self.d
    }

    /// ### Checks if a point is on the plane.
    ///
    /// Returns true if the point lies on the plane within a small epsilon tolerance.
    ///
    /// # Parameters
    /// - `point`: The point to test
    ///
    /// # Returns
    /// `true` if the point is on the plane.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// let plane = Plane::new(Vector3::UP, 0.0);
    /// let point = Vector3::new(1.0, 0.0, 1.0);
    /// assert!(plane.has_point(point));
    /// ```
    #[inline]
    pub fn has_point(self, point: Vector3) -> bool {
        self.distance_to(point).abs() < 0.001
    }

    /// ### Projects a point onto the plane.
    ///
    /// Returns the closest point on the plane to the given point.
    ///
    /// # Parameters
    /// - `point`: The point to project
    ///
    /// # Returns
    /// The projected point on the plane.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// let plane = Plane::new(Vector3::UP, 0.0);
    /// let point = Vector3::new(1.0, 5.0, 1.0);
    /// let projected = plane.project(point);
    /// assert!((projected.y - 0.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn project(self, point: Vector3) -> Vector3 {
        point - self.normal * self.distance_to(point)
    }

    /// ### Calculates the intersection point of a ray with the plane.
    ///
    /// Returns the intersection point if the ray intersects the plane,
    /// or None if the ray is parallel to the plane.
    ///
    /// # Parameters
    /// - `ray_origin`: The origin point of the ray
    /// - `ray_direction`: The direction vector of the ray (should be normalized)
    ///
    /// # Returns
    /// The intersection point, or None if no intersection.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// let plane = Plane::new(Vector3::UP, 0.0);
    /// let ray_origin = Vector3::new(0.0, 5.0, 0.0);
    /// let ray_direction = Vector3::DOWN;
    /// let intersection = plane.intersect_ray(ray_origin, ray_direction);
    /// ```
    #[inline]
    pub fn intersect_ray(self, ray_origin: Vector3, ray_direction: Vector3) -> Option<Vector3> {
        let denominator = self.normal.dot(ray_direction);

        // Check if ray is parallel to plane
        if denominator.abs() < f32::EPSILON {
            return None;
        }

        let t = -(self.normal.dot(ray_origin) + self.d) / denominator;

        // Check if intersection is behind the ray origin
        if t < 0.0 {
            return None;
        }

        Some(ray_origin + ray_direction * t)
    }

    /// ### Calculates the intersection point of a line segment with the plane.
    ///
    /// Returns the intersection point if the line segment intersects the plane,
    /// or None if no intersection occurs within the segment bounds.
    ///
    /// # Parameters
    /// - `start`: The start point of the line segment
    /// - `end`: The end point of the line segment
    ///
    /// # Returns
    /// The intersection point, or None if no intersection within segment.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// let plane = Plane::new(Vector3::UP, 0.0);
    /// let start = Vector3::new(0.0, 5.0, 0.0);
    /// let end = Vector3::new(0.0, -5.0, 0.0);
    /// let intersection = plane.intersect_segment(start, end);
    /// ```
    #[inline]
    pub fn intersect_segment(self, start: Vector3, end: Vector3) -> Option<Vector3> {
        let direction = end - start;
        let length = direction.length();

        if length < f32::EPSILON {
            return None;
        }

        let normalized_direction = direction / length;
        let denominator = self.normal.dot(normalized_direction);

        // Check if segment is parallel to plane
        if denominator.abs() < f32::EPSILON {
            return None;
        }

        let t = -(self.normal.dot(start) + self.d) / denominator;

        // Check if intersection is within segment bounds
        if t < 0.0 || t > length {
            return None;
        }

        Some(start + normalized_direction * t)
    }

    /// ### Returns the normalized version of the plane.
    ///
    /// Ensures the normal vector has unit length and adjusts the distance accordingly.
    ///
    /// # Returns
    /// A normalized plane.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// let plane = Plane::new(Vector3::new(2.0, 0.0, 0.0), 4.0);
    /// let normalized = plane.normalized();
    /// ```
    #[inline]
    pub fn normalized(self) -> Self {
        let length = self.normal.length();
        if length < f32::EPSILON {
            return self;
        }

        Self {
            normal: self.normal / length,
            d: self.d / length,
        }
    }

    /// ### Checks if the plane is approximately equal to another.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    ///
    /// # Parameters
    /// - `other`: The other plane to compare with
    ///
    /// # Returns
    /// `true` if the planes are approximately equal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Plane, Vector3};
    /// let p1 = Plane::new(Vector3::UP, 0.0);
    /// let p2 = Plane::new(Vector3::UP, 0.0);
    /// assert!(p1.is_equal_approx(p2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Self) -> bool {
        self.normal.is_equal_approx(other.normal) && (self.d - other.d).abs() < f32::EPSILON
    }
}

impl Default for Plane {
    /// Returns a plane at the origin with normal pointing up.
    #[inline]
    fn default() -> Self {
        Self::new(Vector3::UP, 0.0)
    }
}

impl fmt::Display for Plane {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Plane(normal: {}, d: {})", self.normal, self.d)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_plane_creation() {
        let plane = Plane::new(Vector3::UP, 5.0);
        assert_eq!(plane.normal, Vector3::UP);
        assert_eq!(plane.d, 5.0);

        let default = Plane::default();
        assert_eq!(default.normal, Vector3::UP);
        assert_eq!(default.d, 0.0);
    }

    #[test]
    fn test_plane_from_points() {
        // Create a plane from three points on the XY plane
        let p1 = Vector3::new(0.0, 0.0, 0.0);
        let p2 = Vector3::new(1.0, 0.0, 0.0);
        let p3 = Vector3::new(0.0, 1.0, 0.0);
        let plane = Plane::from_points(p1, p2, p3);

        // The normal should point in the Z direction
        assert!((plane.normal.z.abs() - 1.0).abs() < 0.001);
        assert!(plane.has_point(p1));
        assert!(plane.has_point(p2));
        assert!(plane.has_point(p3));
    }

    #[test]
    fn test_plane_from_point_normal() {
        let point = Vector3::new(0.0, 5.0, 0.0);
        let normal = Vector3::UP;
        let plane = Plane::from_point_normal(point, normal);

        assert!(plane.has_point(point));
        assert!((plane.normal - Vector3::UP).length() < 0.001);
    }

    #[test]
    fn test_plane_distance_to() {
        let plane = Plane::new(Vector3::UP, 0.0); // XZ plane at y=0

        let point_above = Vector3::new(0.0, 5.0, 0.0);
        let distance_above = plane.distance_to(point_above);
        assert!((distance_above - 5.0).abs() < 0.001);

        let point_below = Vector3::new(0.0, -3.0, 0.0);
        let distance_below = plane.distance_to(point_below);
        assert!((distance_below - (-3.0)).abs() < 0.001);

        let point_on_plane = Vector3::new(1.0, 0.0, 1.0);
        let distance_on = plane.distance_to(point_on_plane);
        assert!(distance_on.abs() < 0.001);
    }

    #[test]
    fn test_plane_has_point() {
        let plane = Plane::new(Vector3::UP, 0.0);

        assert!(plane.has_point(Vector3::new(0.0, 0.0, 0.0)));
        assert!(plane.has_point(Vector3::new(5.0, 0.0, 5.0)));
        assert!(!plane.has_point(Vector3::new(0.0, 1.0, 0.0)));
    }

    #[test]
    fn test_plane_project() {
        let plane = Plane::new(Vector3::UP, 0.0);

        let point = Vector3::new(1.0, 5.0, 2.0);
        let projected = plane.project(point);

        assert!((projected.x - 1.0).abs() < 0.001);
        assert!((projected.y - 0.0).abs() < 0.001);
        assert!((projected.z - 2.0).abs() < 0.001);
        assert!(plane.has_point(projected));
    }

    #[test]
    fn test_plane_intersect_ray() {
        let plane = Plane::new(Vector3::UP, 0.0);

        // Ray pointing down from above
        let ray_origin = Vector3::new(1.0, 5.0, 2.0);
        let ray_direction = Vector3::DOWN;
        let intersection = plane.intersect_ray(ray_origin, ray_direction);

        assert!(intersection.is_some());
        let point = intersection.unwrap();
        assert!((point.x - 1.0).abs() < 0.001);
        assert!((point.y - 0.0).abs() < 0.001);
        assert!((point.z - 2.0).abs() < 0.001);

        // Ray parallel to plane
        let parallel_ray = plane.intersect_ray(Vector3::ZERO, Vector3::RIGHT);
        assert!(parallel_ray.is_none());

        // Ray pointing away from plane
        let away_ray = plane.intersect_ray(Vector3::new(0.0, 5.0, 0.0), Vector3::UP);
        assert!(away_ray.is_none());
    }

    #[test]
    fn test_plane_intersect_segment() {
        let plane = Plane::new(Vector3::UP, 0.0);

        // Segment crossing the plane
        let start = Vector3::new(0.0, 5.0, 0.0);
        let end = Vector3::new(0.0, -5.0, 0.0);
        let intersection = plane.intersect_segment(start, end);

        assert!(intersection.is_some());
        let point = intersection.unwrap();
        assert!((point.y - 0.0).abs() < 0.001);

        // Segment not crossing the plane
        let no_cross = plane.intersect_segment(
            Vector3::new(0.0, 1.0, 0.0),
            Vector3::new(0.0, 2.0, 0.0)
        );
        assert!(no_cross.is_none());

        // Segment parallel to plane
        let parallel = plane.intersect_segment(
            Vector3::new(0.0, 0.0, 0.0),
            Vector3::new(1.0, 0.0, 0.0)
        );
        assert!(parallel.is_none());
    }

    #[test]
    fn test_plane_normalized() {
        let plane = Plane::new(Vector3::new(2.0, 0.0, 0.0), 4.0);
        let normalized = plane.normalized();

        assert!((normalized.normal.length() - 1.0).abs() < 0.001);
        assert!((normalized.d - 2.0).abs() < 0.001); // 4.0 / 2.0 = 2.0
    }

    #[test]
    fn test_plane_equality_checks() {
        let p1 = Plane::new(Vector3::UP, 0.0);
        let p2 = Plane::new(Vector3::UP, 0.0);
        assert!(p1.is_equal_approx(p2));

        let p3 = Plane::new(Vector3::UP, 0.001);
        assert!(!p1.is_equal_approx(p3));
    }

    #[test]
    fn test_plane_display() {
        let plane = Plane::new(Vector3::UP, 5.0);
        let display_str = format!("{}", plane);
        assert!(display_str.contains("Plane"));
        assert!(display_str.contains("normal"));
        assert!(display_str.contains("5"));
    }

    #[test]
    fn test_plane_edge_cases() {
        // Zero normal vector
        let zero_plane = Plane::new(Vector3::ZERO, 1.0);
        let normalized_zero = zero_plane.normalized();
        assert_eq!(normalized_zero.normal, Vector3::ZERO);

        // Very small segment
        let plane = Plane::new(Vector3::UP, 0.0);
        let tiny_segment = plane.intersect_segment(
            Vector3::ZERO,
            Vector3::new(1e-10, 0.0, 0.0)
        );
        assert!(tiny_segment.is_none());
    }
}
