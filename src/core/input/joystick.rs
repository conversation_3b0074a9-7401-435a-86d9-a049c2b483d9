/// ### Godot-compatible joystick and gamepad input handling.
///
/// This module provides comprehensive gamepad input support following <PERSON><PERSON>'s
/// joystick handling patterns. It includes support for Xbox and PlayStation
/// controllers, analog sticks, triggers, and D-pad input with multi-device support.
///
/// ## Gamepad Features
///
/// - **Universal Button Mapping**: Xbox and PlayStation controller support
/// - **Analog Input**: Sticks and triggers with deadzone handling
/// - **Multi-Device Support**: Handle multiple connected controllers
/// - **D-Pad Support**: Directional pad input as both buttons and axes
/// - **Vibration Support**: Rumble and haptic feedback capabilities
/// - **Performance Optimized**: Efficient gamepad state tracking and polling
///
/// ## Examples
///
/// ```rust
/// use verturion::core::input::{JoypadButton, JoypadAxis, JoypadMotion};
///
/// // Button handling
/// let jump_button = JoypadButton::A;  // Xbox A / PlayStation Cross
/// let menu_button = JoypadButton::Start;
///
/// // Analog stick input
/// let left_stick_x = JoypadAxis::LeftStickX;
/// let right_trigger = JoypadAxis::TriggerRight;
///
/// // Motion event with deadzone
/// let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.75, 0);
/// if motion.value().abs() > 0.1 { // Apply deadzone
///     println!("Left stick moved: {}", motion.value());
/// }
/// ```

/// ### Gamepad button enumeration with Xbox and PlayStation mapping.
///
/// Represents all standard gamepad buttons following Godot's joypad button
/// constants. Uses Xbox naming as primary with PlayStation equivalents noted.
/// Supports both digital buttons and analog triggers as buttons.
///
/// ## Controller Mapping
///
/// The button values match Godot's JOY_BUTTON_* constants for compatibility.
/// Xbox controller layout is used as the standard with PlayStation mappings.
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::input::JoypadButton;
/// // Face buttons (Xbox / PlayStation)
/// let confirm = JoypadButton::A;        // A / Cross
/// let cancel = JoypadButton::B;         // B / Circle
/// let action = JoypadButton::X;         // X / Square
/// let menu = JoypadButton::Y;           // Y / Triangle
///
/// // Shoulder buttons
/// let left_bumper = JoypadButton::LeftShoulder;   // LB / L1
/// let right_bumper = JoypadButton::RightShoulder; // RB / R1
/// ```
#[derive(Copy, Clone, Debug, PartialEq, Eq, Hash)]
#[repr(u8)]
pub enum JoypadButton {
    /// A button (Xbox) / Cross button (PlayStation)
    A = 0,
    /// B button (Xbox) / Circle button (PlayStation)
    B = 1,
    /// X button (Xbox) / Square button (PlayStation)
    X = 2,
    /// Y button (Xbox) / Triangle button (PlayStation)
    Y = 3,
    /// Back/Select button
    Back = 4,
    /// Guide/Home button (Xbox logo / PlayStation button)
    Guide = 5,
    /// Start/Options button
    Start = 6,
    /// Left stick click (L3)
    LeftStick = 7,
    /// Right stick click (R3)
    RightStick = 8,
    /// Left shoulder button (LB / L1)
    LeftShoulder = 9,
    /// Right shoulder button (RB / R1)
    RightShoulder = 10,
    /// D-pad up
    DPadUp = 11,
    /// D-pad down
    DPadDown = 12,
    /// D-pad left
    DPadLeft = 13,
    /// D-pad right
    DPadRight = 14,
    /// Miscellaneous button 1 (Share button on PlayStation)
    Misc1 = 15,
    /// Paddle button 1 (Elite controller)
    Paddle1 = 16,
    /// Paddle button 2 (Elite controller)
    Paddle2 = 17,
    /// Paddle button 3 (Elite controller)
    Paddle3 = 18,
    /// Paddle button 4 (Elite controller)
    Paddle4 = 19,
    /// Touchpad button (PlayStation)
    Touchpad = 20,
}

impl JoypadButton {
    /// ### Checks if this is a face button.
    ///
    /// Returns true for A, B, X, Y buttons (the main action buttons).
    /// These are the primary buttons used for game actions.
    ///
    /// # Returns
    /// True if this is a face button, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::JoypadButton;
    /// assert!(JoypadButton::A.is_face_button());
    /// assert!(JoypadButton::B.is_face_button());
    /// assert!(!JoypadButton::Start.is_face_button());
    /// assert!(!JoypadButton::LeftShoulder.is_face_button());
    /// ```
    #[inline]
    pub const fn is_face_button(self) -> bool {
        matches!(self, 
            JoypadButton::A | 
            JoypadButton::B | 
            JoypadButton::X | 
            JoypadButton::Y
        )
    }

    /// ### Checks if this is a D-pad button.
    ///
    /// Returns true for D-pad directional buttons (up, down, left, right).
    /// These buttons provide digital directional input.
    ///
    /// # Returns
    /// True if this is a D-pad button, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::JoypadButton;
    /// assert!(JoypadButton::DPadUp.is_dpad_button());
    /// assert!(JoypadButton::DPadLeft.is_dpad_button());
    /// assert!(!JoypadButton::A.is_dpad_button());
    /// assert!(!JoypadButton::LeftShoulder.is_dpad_button());
    /// ```
    #[inline]
    pub const fn is_dpad_button(self) -> bool {
        matches!(self,
            JoypadButton::DPadUp |
            JoypadButton::DPadDown |
            JoypadButton::DPadLeft |
            JoypadButton::DPadRight
        )
    }

    /// ### Checks if this is a shoulder button.
    ///
    /// Returns true for shoulder buttons (LB/L1, RB/R1) and stick clicks.
    /// These are the buttons on the top and sides of the controller.
    ///
    /// # Returns
    /// True if this is a shoulder button, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::JoypadButton;
    /// assert!(JoypadButton::LeftShoulder.is_shoulder_button());
    /// assert!(JoypadButton::RightShoulder.is_shoulder_button());
    /// assert!(JoypadButton::LeftStick.is_shoulder_button());
    /// assert!(!JoypadButton::A.is_shoulder_button());
    /// ```
    #[inline]
    pub const fn is_shoulder_button(self) -> bool {
        matches!(self,
            JoypadButton::LeftShoulder |
            JoypadButton::RightShoulder |
            JoypadButton::LeftStick |
            JoypadButton::RightStick
        )
    }

    /// ### Gets the button index for array-based storage.
    ///
    /// Returns a zero-based index suitable for storing button states
    /// in arrays or bit fields. Useful for efficient button state tracking.
    ///
    /// # Returns
    /// Zero-based button index.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::JoypadButton;
    /// assert_eq!(JoypadButton::A.index(), 0);
    /// assert_eq!(JoypadButton::B.index(), 1);
    /// assert_eq!(JoypadButton::Start.index(), 6);
    /// ```
    #[inline]
    pub const fn index(self) -> usize {
        self as usize
    }
}

impl std::fmt::Display for JoypadButton {
    /// ### Formats the JoypadButton for display.
    ///
    /// Provides human-readable names for gamepad buttons suitable
    /// for user interfaces and control configuration screens.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::JoypadButton;
    /// assert_eq!(format!("{}", JoypadButton::A), "A Button");
    /// assert_eq!(format!("{}", JoypadButton::LeftShoulder), "Left Shoulder");
    /// assert_eq!(format!("{}", JoypadButton::DPadUp), "D-Pad Up");
    /// ```
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let name = match self {
            JoypadButton::A => "A Button",
            JoypadButton::B => "B Button",
            JoypadButton::X => "X Button",
            JoypadButton::Y => "Y Button",
            JoypadButton::Back => "Back Button",
            JoypadButton::Guide => "Guide Button",
            JoypadButton::Start => "Start Button",
            JoypadButton::LeftStick => "Left Stick",
            JoypadButton::RightStick => "Right Stick",
            JoypadButton::LeftShoulder => "Left Shoulder",
            JoypadButton::RightShoulder => "Right Shoulder",
            JoypadButton::DPadUp => "D-Pad Up",
            JoypadButton::DPadDown => "D-Pad Down",
            JoypadButton::DPadLeft => "D-Pad Left",
            JoypadButton::DPadRight => "D-Pad Right",
            JoypadButton::Misc1 => "Misc Button 1",
            JoypadButton::Paddle1 => "Paddle 1",
            JoypadButton::Paddle2 => "Paddle 2",
            JoypadButton::Paddle3 => "Paddle 3",
            JoypadButton::Paddle4 => "Paddle 4",
            JoypadButton::Touchpad => "Touchpad",
        };
        write!(f, "{}", name)
    }
}

/// ### Gamepad axis enumeration for analog inputs.
///
/// Represents all analog input axes on a gamepad including stick axes,
/// triggers, and D-pad axes. Values typically range from -1.0 to 1.0
/// for sticks and 0.0 to 1.0 for triggers.
///
/// ## Axis Mapping
///
/// The axis values match Godot's JOY_AXIS_* constants for compatibility.
/// Stick axes use standard conventions: X-axis for horizontal, Y-axis for vertical.
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::input::JoypadAxis;
/// // Analog stick axes
/// let move_horizontal = JoypadAxis::LeftStickX;
/// let move_vertical = JoypadAxis::LeftStickY;
/// let look_horizontal = JoypadAxis::RightStickX;
/// let look_vertical = JoypadAxis::RightStickY;
///
/// // Trigger axes (0.0 to 1.0)
/// let brake = JoypadAxis::TriggerLeft;    // LT / L2
/// let accelerate = JoypadAxis::TriggerRight; // RT / R2
/// ```
#[derive(Copy, Clone, Debug, PartialEq, Eq, Hash)]
#[repr(u8)]
pub enum JoypadAxis {
    /// Left stick horizontal axis (-1.0 = left, 1.0 = right)
    LeftStickX = 0,
    /// Left stick vertical axis (-1.0 = up, 1.0 = down)
    LeftStickY = 1,
    /// Right stick horizontal axis (-1.0 = left, 1.0 = right)
    RightStickX = 2,
    /// Right stick vertical axis (-1.0 = up, 1.0 = down)
    RightStickY = 3,
    /// Left trigger axis (0.0 = not pressed, 1.0 = fully pressed)
    TriggerLeft = 4,
    /// Right trigger axis (0.0 = not pressed, 1.0 = fully pressed)
    TriggerRight = 5,
}

impl JoypadAxis {
    /// ### Checks if this is a stick axis.
    ///
    /// Returns true for left and right stick X and Y axes.
    /// These axes typically range from -1.0 to 1.0.
    ///
    /// # Returns
    /// True if this is a stick axis, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::JoypadAxis;
    /// assert!(JoypadAxis::LeftStickX.is_stick_axis());
    /// assert!(JoypadAxis::RightStickY.is_stick_axis());
    /// assert!(!JoypadAxis::TriggerLeft.is_stick_axis());
    /// assert!(!JoypadAxis::TriggerRight.is_stick_axis());
    /// ```
    #[inline]
    pub const fn is_stick_axis(self) -> bool {
        matches!(self,
            JoypadAxis::LeftStickX |
            JoypadAxis::LeftStickY |
            JoypadAxis::RightStickX |
            JoypadAxis::RightStickY
        )
    }

    /// ### Checks if this is a trigger axis.
    ///
    /// Returns true for left and right trigger axes.
    /// These axes typically range from 0.0 to 1.0.
    ///
    /// # Returns
    /// True if this is a trigger axis, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::JoypadAxis;
    /// assert!(JoypadAxis::TriggerLeft.is_trigger_axis());
    /// assert!(JoypadAxis::TriggerRight.is_trigger_axis());
    /// assert!(!JoypadAxis::LeftStickX.is_trigger_axis());
    /// assert!(!JoypadAxis::RightStickY.is_trigger_axis());
    /// ```
    #[inline]
    pub const fn is_trigger_axis(self) -> bool {
        matches!(self,
            JoypadAxis::TriggerLeft |
            JoypadAxis::TriggerRight
        )
    }

    /// ### Gets the axis index for array-based storage.
    ///
    /// Returns a zero-based index suitable for storing axis values
    /// in arrays. Useful for efficient axis state tracking.
    ///
    /// # Returns
    /// Zero-based axis index.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::JoypadAxis;
    /// assert_eq!(JoypadAxis::LeftStickX.index(), 0);
    /// assert_eq!(JoypadAxis::LeftStickY.index(), 1);
    /// assert_eq!(JoypadAxis::TriggerLeft.index(), 4);
    /// ```
    #[inline]
    pub const fn index(self) -> usize {
        self as usize
    }
}

impl std::fmt::Display for JoypadAxis {
    /// ### Formats the JoypadAxis for display.
    ///
    /// Provides human-readable names for gamepad axes suitable
    /// for user interfaces and control configuration screens.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::JoypadAxis;
    /// assert_eq!(format!("{}", JoypadAxis::LeftStickX), "Left Stick X");
    /// assert_eq!(format!("{}", JoypadAxis::TriggerRight), "Right Trigger");
    /// ```
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let name = match self {
            JoypadAxis::LeftStickX => "Left Stick X",
            JoypadAxis::LeftStickY => "Left Stick Y",
            JoypadAxis::RightStickX => "Right Stick X",
            JoypadAxis::RightStickY => "Right Stick Y",
            JoypadAxis::TriggerLeft => "Left Trigger",
            JoypadAxis::TriggerRight => "Right Trigger",
        };
        write!(f, "{}", name)
    }
}

/// ### Gamepad analog motion event data.
///
/// Contains analog input information for gamepad axes including the axis type,
/// current value, and device index. Used for analog stick movement, trigger
/// pressure, and other continuous input values.
///
/// ## Motion Data
///
/// - **Axis**: Which analog axis generated this motion
/// - **Value**: Current axis value (typically -1.0 to 1.0 for sticks, 0.0 to 1.0 for triggers)
/// - **Device**: Controller device index for multi-controller support
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::input::{JoypadMotion, JoypadAxis};
/// // Create analog motion event
/// let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.75, 0);
///
/// // Apply deadzone filtering
/// let deadzone = 0.1;
/// if motion.value().abs() > deadzone {
///     println!("Stick moved: {}", motion.value());
/// }
///
/// // Use for character movement
/// let move_speed = motion.value() * 100.0; // Scale to movement speed
/// ```
#[derive(Copy, Clone, Debug, PartialEq)]
pub struct JoypadMotion {
    /// The axis that generated this motion
    axis: JoypadAxis,
    /// Current axis value
    value: f32,
    /// Controller device index
    device: i32,
}

impl JoypadMotion {
    /// ### Creates a new JoypadMotion event.
    ///
    /// Constructs a gamepad motion event with axis, value, and device information.
    /// The value should be in the appropriate range for the axis type.
    ///
    /// # Parameters
    /// - `axis`: The gamepad axis that generated this motion
    /// - `value`: Current axis value (range depends on axis type)
    /// - `device`: Controller device index (0 for first controller)
    ///
    /// # Returns
    /// New JoypadMotion instance with the specified data.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{JoypadMotion, JoypadAxis};
    /// let stick_motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.5, 0);
    /// let trigger_motion = JoypadMotion::new(JoypadAxis::TriggerRight, 0.8, 1);
    /// ```
    #[inline]
    pub const fn new(axis: JoypadAxis, value: f32, device: i32) -> Self {
        Self {
            axis,
            value,
            device,
        }
    }

    /// ### Gets the axis that generated this motion.
    ///
    /// Returns the JoypadAxis enum value indicating which analog input
    /// generated this motion event.
    ///
    /// # Returns
    /// JoypadAxis that generated this motion.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{JoypadMotion, JoypadAxis};
    /// let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.5, 0);
    /// assert_eq!(motion.axis(), JoypadAxis::LeftStickX);
    /// ```
    #[inline]
    pub const fn axis(self) -> JoypadAxis {
        self.axis
    }

    /// ### Gets the current axis value.
    ///
    /// Returns the analog value for this axis. Stick axes range from -1.0 to 1.0,
    /// trigger axes range from 0.0 to 1.0.
    ///
    /// # Returns
    /// Current axis value as a floating-point number.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{JoypadMotion, JoypadAxis};
    /// let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.75, 0);
    /// assert_eq!(motion.value(), 0.75);
    /// ```
    #[inline]
    pub const fn value(self) -> f32 {
        self.value
    }

    /// ### Gets the controller device index.
    ///
    /// Returns the device index for multi-controller support.
    /// Device 0 is typically the first connected controller.
    ///
    /// # Returns
    /// Controller device index.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{JoypadMotion, JoypadAxis};
    /// let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.5, 1);
    /// assert_eq!(motion.device(), 1); // Second controller
    /// ```
    #[inline]
    pub const fn device(self) -> i32 {
        self.device
    }

    /// ### Applies deadzone filtering to the axis value.
    ///
    /// Returns the axis value with deadzone applied. Values within the deadzone
    /// are returned as 0.0, values outside are scaled to maintain smooth response.
    ///
    /// # Parameters
    /// - `deadzone`: Deadzone threshold (typically 0.1 to 0.2)
    ///
    /// # Returns
    /// Filtered axis value with deadzone applied.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{JoypadMotion, JoypadAxis};
    /// let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.05, 0);
    /// assert_eq!(motion.apply_deadzone(0.1), 0.0); // Within deadzone
    ///
    /// let motion2 = JoypadMotion::new(JoypadAxis::LeftStickX, 0.5, 0);
    /// assert!(motion2.apply_deadzone(0.1) > 0.0); // Outside deadzone
    /// ```
    #[inline]
    pub fn apply_deadzone(self, deadzone: f32) -> f32 {
        let abs_value = self.value.abs();
        if abs_value < deadzone {
            0.0
        } else {
            // Scale the value to maintain smooth response
            let sign = self.value.signum();
            let scaled = (abs_value - deadzone) / (1.0 - deadzone);
            sign * scaled
        }
    }

    /// ### Checks if the axis value is within the deadzone.
    ///
    /// Returns true if the absolute value is less than or equal to the deadzone threshold.
    /// Useful for detecting when an analog input should be considered at rest.
    ///
    /// # Parameters
    /// - `deadzone`: Deadzone threshold to check against
    ///
    /// # Returns
    /// True if the value is within the deadzone, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{JoypadMotion, JoypadAxis};
    /// let small_motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.05, 0);
    /// assert!(small_motion.is_in_deadzone(0.1));
    ///
    /// let large_motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.5, 0);
    /// assert!(!large_motion.is_in_deadzone(0.1));
    /// ```
    #[inline]
    pub fn is_in_deadzone(self, deadzone: f32) -> bool {
        self.value.abs() <= deadzone
    }
}

impl Default for JoypadMotion {
    /// ### Creates the default JoypadMotion value.
    ///
    /// Returns a JoypadMotion with LeftStickX axis, zero value, and device 0.
    /// This represents a neutral analog input state.
    ///
    /// # Returns
    /// JoypadMotion with default values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{JoypadMotion, JoypadAxis};
    /// let motion = JoypadMotion::default();
    /// assert_eq!(motion.axis(), JoypadAxis::LeftStickX);
    /// assert_eq!(motion.value(), 0.0);
    /// assert_eq!(motion.device(), 0);
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new(JoypadAxis::LeftStickX, 0.0, 0)
    }
}

impl std::fmt::Display for JoypadMotion {
    /// ### Formats the JoypadMotion for display.
    ///
    /// Provides human-readable representation of gamepad motion data
    /// including axis, value, and device information.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{JoypadMotion, JoypadAxis};
    /// let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.75, 0);
    /// println!("{}", motion); // Shows axis, value, and device
    /// ```
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "JoypadMotion[{}: {:.3}, device: {}]", 
               self.axis, self.value, self.device)
    }
}
