/// ### Godot-compatible input management and action mapping system.
///
/// This module provides the main input coordination system following <PERSON><PERSON>'s
/// Input singleton patterns. It includes action mapping, input state management,
/// and event processing for comprehensive input handling in games and applications.
///
/// ## Input Management Features
///
/// - **Action Mapping**: Map physical inputs to logical game actions
/// - **State Tracking**: Track current state of all input devices
/// - **Event Processing**: Process and filter input events
/// - **Multi-Device Support**: Handle multiple controllers and input devices
/// - **Input Buffering**: Buffer input events for frame-perfect timing
/// - **Deadzone Handling**: Apply deadzones to analog inputs
///
/// ## Examples
///
/// ```rust
/// use verturion::core::input::{Input, InputMap, KeyCode, JoypadButton};
///
/// // Create input manager and action map
/// let mut input = Input::new();
/// let mut input_map = InputMap::new();
///
/// // Map physical inputs to logical actions
/// input_map.add_action("jump", KeyCode::Space);
/// input_map.add_action("jump", JoypadButton::A);
/// input_map.add_action("move_left", KeyCode::A);
/// input_map.add_action("move_right", KeyCode::D);
///
/// // Check action states
/// if input.is_action_pressed("jump") {
///     println!("Player is jumping!");
/// }
///
/// // Get analog input vector
/// let movement = input.get_vector("move_left", "move_right", "move_up", "move_down");
/// ```
use std::collections::HashMap;
use super::{InputEvent, KeyCode, MouseButton, JoypadButton, JoypadAxis};
use crate::core::math::Vector2;

/// ### Input action mapping system.
///
/// Manages the mapping between physical input events (keys, buttons, axes)
/// and logical game actions. Provides a flexible system for configuring
/// input controls and supporting multiple input methods for the same action.
///
/// ## Action Types
///
/// Actions can be mapped to various input types including keyboard keys,
/// mouse buttons, gamepad buttons, and analog axes. Multiple inputs can
/// be mapped to the same action for flexibility.
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::input::{InputMap, KeyCode, JoypadButton, MouseButton};
/// let mut input_map = InputMap::new();
///
/// // Map multiple inputs to the same action
/// input_map.add_action("jump", KeyCode::Space);
/// input_map.add_action("jump", JoypadButton::A);
/// input_map.add_action("jump", MouseButton::Left);
///
/// // Map movement actions
/// input_map.add_action("move_left", KeyCode::A);
/// input_map.add_action("move_right", KeyCode::D);
/// input_map.add_action("move_up", KeyCode::W);
/// input_map.add_action("move_down", KeyCode::S);
/// ```
#[derive(Clone, Debug)]
pub struct InputMap {
    /// Maps action names to lists of input events
    actions: HashMap<String, Vec<InputBinding>>,
}

/// ### Input binding for action mapping.
///
/// Represents a single input binding that can trigger an action.
/// Supports different types of physical inputs including digital
/// buttons and analog axes with configurable parameters.
#[derive(Clone, Debug, PartialEq)]
pub enum InputBinding {
    /// Keyboard key binding
    Key(KeyCode),
    /// Mouse button binding
    MouseButton(MouseButton),
    /// Gamepad button binding
    JoypadButton(JoypadButton),
    /// Gamepad axis binding with threshold
    JoypadAxis { axis: JoypadAxis, threshold: f32 },
}

impl InputMap {
    /// ### Creates a new empty InputMap.
    ///
    /// Returns an InputMap with no action mappings configured.
    /// Actions must be added using the add_action methods.
    ///
    /// # Returns
    /// New empty InputMap instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::InputMap;
    /// let input_map = InputMap::new();
    /// assert!(input_map.get_actions().is_empty());
    /// ```
    pub fn new() -> Self {
        Self {
            actions: HashMap::new(),
        }
    }

    /// ### Adds a keyboard key binding to an action.
    ///
    /// Maps a keyboard key to the specified action name. If the action
    /// doesn't exist, it will be created. Multiple keys can be mapped
    /// to the same action.
    ///
    /// # Parameters
    /// - `action`: Name of the action to bind to
    /// - `key`: Keyboard key to bind
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputMap, KeyCode};
    /// let mut input_map = InputMap::new();
    /// input_map.add_action("jump", KeyCode::Space);
    /// input_map.add_action("jump", KeyCode::Enter); // Alternative key
    /// ```
    pub fn add_action<T: Into<InputBinding>>(&mut self, action: &str, input: T) {
        let binding = input.into();
        self.actions
            .entry(action.to_string())
            .or_insert_with(Vec::new)
            .push(binding);
    }

    /// ### Removes all bindings for an action.
    ///
    /// Completely removes an action and all its input bindings from the map.
    /// Returns true if the action existed and was removed.
    ///
    /// # Parameters
    /// - `action`: Name of the action to remove
    ///
    /// # Returns
    /// True if the action was removed, false if it didn't exist.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputMap, KeyCode};
    /// let mut input_map = InputMap::new();
    /// input_map.add_action("jump", KeyCode::Space);
    /// assert!(input_map.remove_action("jump"));
    /// assert!(!input_map.remove_action("jump")); // Already removed
    /// ```
    pub fn remove_action(&mut self, action: &str) -> bool {
        self.actions.remove(action).is_some()
    }

    /// ### Gets all action names in the input map.
    ///
    /// Returns a vector of all configured action names.
    /// Useful for iterating over all available actions.
    ///
    /// # Returns
    /// Vector of action name strings.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputMap, KeyCode};
    /// let mut input_map = InputMap::new();
    /// input_map.add_action("jump", KeyCode::Space);
    /// input_map.add_action("run", KeyCode::Shift);
    ///
    /// let actions = input_map.get_actions();
    /// assert!(actions.contains(&"jump".to_string()));
    /// assert!(actions.contains(&"run".to_string()));
    /// ```
    pub fn get_actions(&self) -> Vec<String> {
        self.actions.keys().cloned().collect()
    }

    /// ### Gets all input bindings for an action.
    ///
    /// Returns a slice of all input bindings mapped to the specified action.
    /// Returns an empty slice if the action doesn't exist.
    ///
    /// # Parameters
    /// - `action`: Name of the action to query
    ///
    /// # Returns
    /// Slice of InputBinding for the action.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputMap, KeyCode, JoypadButton};
    /// let mut input_map = InputMap::new();
    /// input_map.add_action("jump", KeyCode::Space);
    /// input_map.add_action("jump", JoypadButton::A);
    ///
    /// let bindings = input_map.get_action_bindings("jump");
    /// assert_eq!(bindings.len(), 2);
    /// ```
    pub fn get_action_bindings(&self, action: &str) -> &[InputBinding] {
        self.actions.get(action).map(|v| v.as_slice()).unwrap_or(&[])
    }

    /// ### Checks if an action has any bindings.
    ///
    /// Returns true if the specified action exists and has at least one
    /// input binding configured.
    ///
    /// # Parameters
    /// - `action`: Name of the action to check
    ///
    /// # Returns
    /// True if the action has bindings, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputMap, KeyCode};
    /// let mut input_map = InputMap::new();
    /// assert!(!input_map.has_action("jump"));
    ///
    /// input_map.add_action("jump", KeyCode::Space);
    /// assert!(input_map.has_action("jump"));
    /// ```
    pub fn has_action(&self, action: &str) -> bool {
        self.actions.get(action).map_or(false, |bindings| !bindings.is_empty())
    }
}

impl Default for InputMap {
    /// ### Creates the default InputMap.
    ///
    /// Returns an empty InputMap with no configured actions.
    /// Equivalent to calling InputMap::new().
    ///
    /// # Returns
    /// Empty InputMap instance.
    fn default() -> Self {
        Self::new()
    }
}

// Implement From traits for convenient binding creation
impl From<KeyCode> for InputBinding {
    fn from(key: KeyCode) -> Self {
        InputBinding::Key(key)
    }
}

impl From<MouseButton> for InputBinding {
    fn from(button: MouseButton) -> Self {
        InputBinding::MouseButton(button)
    }
}

impl From<JoypadButton> for InputBinding {
    fn from(button: JoypadButton) -> Self {
        InputBinding::JoypadButton(button)
    }
}

impl From<(JoypadAxis, f32)> for InputBinding {
    fn from((axis, threshold): (JoypadAxis, f32)) -> Self {
        InputBinding::JoypadAxis { axis, threshold }
    }
}

/// ### Action state tracking for input management.
///
/// Tracks the current state of a logical action including whether it's
/// currently pressed, its strength/intensity, and timing information
/// for advanced input handling features.
#[derive(Clone, Debug, PartialEq)]
pub struct ActionState {
    /// Whether the action is currently pressed/active
    pub pressed: bool,
    /// Strength/intensity of the action (0.0 to 1.0)
    pub strength: f32,
    /// Timestamp when the action was last pressed
    pub press_time: u64,
    /// Timestamp when the action was last released
    pub release_time: u64,
}

impl ActionState {
    /// ### Creates a new ActionState in the released state.
    ///
    /// Returns an ActionState with pressed=false, zero strength,
    /// and zero timestamps. This represents an inactive action.
    ///
    /// # Returns
    /// New ActionState in released state.
    pub fn new() -> Self {
        Self {
            pressed: false,
            strength: 0.0,
            press_time: 0,
            release_time: 0,
        }
    }

    /// ### Checks if the action was just pressed this frame.
    ///
    /// Returns true if the action transitioned from released to pressed.
    /// Useful for detecting single-frame press events.
    ///
    /// # Parameters
    /// - `current_time`: Current frame timestamp
    ///
    /// # Returns
    /// True if just pressed, false otherwise.
    pub fn just_pressed(&self, current_time: u64) -> bool {
        self.pressed && self.press_time == current_time
    }

    /// ### Checks if the action was just released this frame.
    ///
    /// Returns true if the action transitioned from pressed to released.
    /// Useful for detecting single-frame release events.
    ///
    /// # Parameters
    /// - `current_time`: Current frame timestamp
    ///
    /// # Returns
    /// True if just released, false otherwise.
    pub fn just_released(&self, current_time: u64) -> bool {
        !self.pressed && self.release_time == current_time
    }
}

impl Default for ActionState {
    fn default() -> Self {
        Self::new()
    }
}

/// ### Main input management system.
///
/// Provides the central input coordination system following Godot's Input
/// singleton pattern. Manages input state, processes events, and provides
/// high-level input query methods for game logic.
///
/// ## Input Features
///
/// The Input system provides both low-level input state queries and
/// high-level action-based input handling for flexible game development.
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::input::{Input, InputMap, KeyCode, JoypadButton};
/// let mut input = Input::new();
/// let mut input_map = InputMap::new();
///
/// // Configure actions
/// input_map.add_action("jump", KeyCode::Space);
/// input_map.add_action("jump", JoypadButton::A);
///
/// // Process input events and check states
/// if input.is_action_pressed("jump") {
///     println!("Jump action is active!");
/// }
/// ```
#[derive(Debug)]
pub struct Input {
    /// Current action states
    action_states: HashMap<String, ActionState>,
    /// Input event queue for processing
    event_queue: Vec<InputEvent>,
    /// Current frame timestamp
    current_time: u64,
}

impl Input {
    /// ### Creates a new Input manager.
    ///
    /// Returns an Input instance with empty state and event queue.
    /// Ready to process input events and track action states.
    ///
    /// # Returns
    /// New Input manager instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::Input;
    /// let input = Input::new();
    /// ```
    pub fn new() -> Self {
        Self {
            action_states: HashMap::new(),
            event_queue: Vec::new(),
            current_time: 0,
        }
    }

    /// ### Processes an input event.
    ///
    /// Adds an input event to the processing queue. Events are processed
    /// during the next update cycle to update action states.
    ///
    /// # Parameters
    /// - `event`: Input event to process
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{Input, InputEvent, KeyCode};
    /// let mut input = Input::new();
    /// let key_event = InputEvent::key(KeyCode::Space, true, false);
    /// input.process_event(key_event);
    /// ```
    pub fn process_event(&mut self, event: InputEvent) {
        self.event_queue.push(event);
    }

    /// ### Updates input state for the current frame.
    ///
    /// Processes all queued input events and updates action states.
    /// Should be called once per frame before checking input states.
    ///
    /// # Parameters
    /// - `input_map`: Action mapping configuration
    /// - `delta_time`: Time elapsed since last frame
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{Input, InputMap};
    /// let mut input = Input::new();
    /// let input_map = InputMap::new();
    /// input.update(&input_map, 16); // 16ms frame time
    /// ```
    pub fn update(&mut self, input_map: &InputMap, delta_time: u64) {
        self.current_time += delta_time;

        // Process queued events
        let events: Vec<InputEvent> = self.event_queue.drain(..).collect();
        for event in events {
            self.process_event_internal(event, input_map);
        }
    }

    /// ### Checks if an action is currently pressed.
    ///
    /// Returns true if the specified action is currently active.
    /// This includes both digital button presses and analog inputs
    /// above their threshold values.
    ///
    /// # Parameters
    /// - `action`: Name of the action to check
    ///
    /// # Returns
    /// True if the action is pressed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::Input;
    /// let input = Input::new();
    /// if input.is_action_pressed("jump") {
    ///     println!("Jump is active!");
    /// }
    /// ```
    pub fn is_action_pressed(&self, action: &str) -> bool {
        self.action_states
            .get(action)
            .map_or(false, |state| state.pressed)
    }

    /// ### Checks if an action was just pressed this frame.
    ///
    /// Returns true if the action transitioned from inactive to active
    /// during the current frame. Useful for single-frame press detection.
    ///
    /// # Parameters
    /// - `action`: Name of the action to check
    ///
    /// # Returns
    /// True if just pressed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::Input;
    /// let input = Input::new();
    /// if input.is_action_just_pressed("jump") {
    ///     println!("Jump was just pressed!");
    /// }
    /// ```
    pub fn is_action_just_pressed(&self, action: &str) -> bool {
        self.action_states
            .get(action)
            .map_or(false, |state| state.just_pressed(self.current_time))
    }

    /// ### Checks if an action was just released this frame.
    ///
    /// Returns true if the action transitioned from active to inactive
    /// during the current frame. Useful for single-frame release detection.
    ///
    /// # Parameters
    /// - `action`: Name of the action to check
    ///
    /// # Returns
    /// True if just released, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::Input;
    /// let input = Input::new();
    /// if input.is_action_just_released("jump") {
    ///     println!("Jump was just released!");
    /// }
    /// ```
    pub fn is_action_just_released(&self, action: &str) -> bool {
        self.action_states
            .get(action)
            .map_or(false, |state| state.just_released(self.current_time))
    }

    /// ### Gets the strength of an action.
    ///
    /// Returns the current strength/intensity of an action as a value
    /// from 0.0 to 1.0. For digital inputs, this is either 0.0 or 1.0.
    /// For analog inputs, this represents the current input value.
    ///
    /// # Parameters
    /// - `action`: Name of the action to query
    ///
    /// # Returns
    /// Action strength from 0.0 to 1.0.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::Input;
    /// let input = Input::new();
    /// let strength = input.get_action_strength("accelerate");
    /// println!("Acceleration: {:.2}", strength);
    /// ```
    pub fn get_action_strength(&self, action: &str) -> f32 {
        self.action_states
            .get(action)
            .map_or(0.0, |state| state.strength)
    }

    /// ### Gets a 2D input vector from four directional actions.
    ///
    /// Combines four directional actions into a normalized 2D vector.
    /// Useful for character movement, camera control, and other 2D input.
    ///
    /// # Parameters
    /// - `negative_x`: Action for leftward movement
    /// - `positive_x`: Action for rightward movement
    /// - `negative_y`: Action for upward movement
    /// - `positive_y`: Action for downward movement
    ///
    /// # Returns
    /// Normalized 2D vector representing the input direction.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::Input;
    /// let input = Input::new();
    /// let movement = input.get_vector("move_left", "move_right", "move_up", "move_down");
    /// println!("Movement vector: {}", movement);
    /// ```
    pub fn get_vector(&self, negative_x: &str, positive_x: &str, negative_y: &str, positive_y: &str) -> Vector2 {
        let x = self.get_action_strength(positive_x) - self.get_action_strength(negative_x);
        let y = self.get_action_strength(positive_y) - self.get_action_strength(negative_y);

        let vector = Vector2::new(x, y);
        if vector.length_squared() > 1.0 {
            vector.normalized()
        } else {
            vector
        }
    }

    // Internal event processing
    fn process_event_internal(&mut self, _event: InputEvent, _input_map: &InputMap) {
        // This would contain the logic to map input events to actions
        // and update action states accordingly
        // Implementation would check input_map bindings and update action_states
    }
}

impl Default for Input {
    fn default() -> Self {
        Self::new()
    }
}
