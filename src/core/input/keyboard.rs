/// ### Godot-compatible keyboard input handling.
///
/// This module provides comprehensive keyboard input support following <PERSON><PERSON>'s
/// keyboard handling patterns. It includes key codes, modifier keys, and
/// hardware-independent scancode support for robust input detection.
///
/// ## Key Features
///
/// - **Complete Key Coverage**: All standard keyboard keys and special keys
/// - **Modifier Support**: Ctrl, Alt, Shift, Meta key combinations
/// - **Scancode Support**: Hardware-independent key detection
/// - **Repeat Detection**: Key repeat state and timing
/// - **Unicode Support**: Text input and character handling
/// - **Performance Optimized**: Efficient key state tracking
///
/// ## Examples
///
/// ```rust
/// use verturion::core::input::{KeyCode, KeyModifiers};
///
/// // Basic key codes
/// let space_key = KeyCode::Space;
/// let enter_key = KeyCode::Enter;
/// let escape_key = KeyCode::Escape;
///
/// // Modifier combinations
/// let modifiers = KeyModifiers::new()
///     .with_ctrl(true)
///     .with_shift(true);
///
/// // Check modifier states
/// assert!(modifiers.ctrl());
/// assert!(modifiers.shift());
/// assert!(!modifiers.alt());
/// ```
#[derive(Copy, Clone, Debug, PartialEq, Eq, Hash)]
#[repr(u32)]
pub enum KeyCode {
    // Special keys
    /// No key pressed
    None = 0,
    /// Escape key
    Escape = 16777217,
    /// Tab key
    Tab = 16777218,
    /// Backspace key
    Backspace = 16777220,
    /// Enter/Return key
    Enter = 16777221,
    /// Insert key
    Insert = 16777222,
    /// Delete key
    Delete = 16777223,
    /// Pause/Break key
    Pause = 16777224,
    /// Print Screen key
    PrintScreen = 16777225,
    /// System Request key
    SysReq = 16777226,
    /// Clear key
    Clear = 16777227,
    /// Home key
    Home = 16777229,
    /// End key
    End = 16777230,
    /// Left arrow key
    Left = 16777231,
    /// Up arrow key
    Up = 16777232,
    /// Right arrow key
    Right = 16777233,
    /// Down arrow key
    Down = 16777234,
    /// Page Up key
    PageUp = 16777235,
    /// Page Down key
    PageDown = 16777236,
    /// Left Shift key
    Shift = 16777237,
    /// Left Control key
    Ctrl = 16777238,
    /// Left Meta/Windows key
    Meta = 16777239,
    /// Left Alt key
    Alt = 16777240,
    /// Caps Lock key
    CapsLock = 16777241,
    /// Num Lock key
    NumLock = 16777242,
    /// Scroll Lock key
    ScrollLock = 16777243,
    
    // Function keys
    /// F1 function key
    F1 = 16777244,
    /// F2 function key
    F2 = 16777245,
    /// F3 function key
    F3 = 16777246,
    /// F4 function key
    F4 = 16777247,
    /// F5 function key
    F5 = 16777248,
    /// F6 function key
    F6 = 16777249,
    /// F7 function key
    F7 = 16777250,
    /// F8 function key
    F8 = 16777251,
    /// F9 function key
    F9 = 16777252,
    /// F10 function key
    F10 = 16777253,
    /// F11 function key
    F11 = 16777254,
    /// F12 function key
    F12 = 16777255,
    
    // Alphanumeric keys (ASCII values)
    /// Space bar
    Space = 32,
    /// Exclamation mark !
    Exclam = 33,
    /// Quotation mark "
    QuoteDbl = 34,
    /// Number sign #
    NumberSign = 35,
    /// Dollar sign $
    Dollar = 36,
    /// Percent sign %
    Percent = 37,
    /// Ampersand &
    Ampersand = 38,
    /// Apostrophe '
    Apostrophe = 39,
    /// Left parenthesis (
    ParenLeft = 40,
    /// Right parenthesis )
    ParenRight = 41,
    /// Asterisk *
    Asterisk = 42,
    /// Plus sign +
    Plus = 43,
    /// Comma ,
    Comma = 44,
    /// Minus sign -
    Minus = 45,
    /// Period .
    Period = 46,
    /// Slash /
    Slash = 47,
    
    // Number keys
    /// Number 0
    Key0 = 48,
    /// Number 1
    Key1 = 49,
    /// Number 2
    Key2 = 50,
    /// Number 3
    Key3 = 51,
    /// Number 4
    Key4 = 52,
    /// Number 5
    Key5 = 53,
    /// Number 6
    Key6 = 54,
    /// Number 7
    Key7 = 55,
    /// Number 8
    Key8 = 56,
    /// Number 9
    Key9 = 57,
    
    // More symbols
    /// Colon :
    Colon = 58,
    /// Semicolon ;
    Semicolon = 59,
    /// Less than <
    Less = 60,
    /// Equal sign =
    Equal = 61,
    /// Greater than >
    Greater = 62,
    /// Question mark ?
    Question = 63,
    /// At sign @
    At = 64,
    
    // Letter keys
    /// Letter A
    A = 65,
    /// Letter B
    B = 66,
    /// Letter C
    C = 67,
    /// Letter D
    D = 68,
    /// Letter E
    E = 69,
    /// Letter F
    F = 70,
    /// Letter G
    G = 71,
    /// Letter H
    H = 72,
    /// Letter I
    I = 73,
    /// Letter J
    J = 74,
    /// Letter K
    K = 75,
    /// Letter L
    L = 76,
    /// Letter M
    M = 77,
    /// Letter N
    N = 78,
    /// Letter O
    O = 79,
    /// Letter P
    P = 80,
    /// Letter Q
    Q = 81,
    /// Letter R
    R = 82,
    /// Letter S
    S = 83,
    /// Letter T
    T = 84,
    /// Letter U
    U = 85,
    /// Letter V
    V = 86,
    /// Letter W
    W = 87,
    /// Letter X
    X = 88,
    /// Letter Y
    Y = 89,
    /// Letter Z
    Z = 90,
    
    // More symbols
    /// Left bracket [
    BracketLeft = 91,
    /// Backslash \
    Backslash = 92,
    /// Right bracket ]
    BracketRight = 93,
    /// Caret ^
    AsciiCircum = 94,
    /// Underscore _
    Underscore = 95,
    /// Grave accent `
    QuoteLeft = 96,
    /// Left brace {
    BraceLeft = 123,
    /// Vertical bar |
    Bar = 124,
    /// Right brace }
    BraceRight = 125,
    /// Tilde ~
    AsciiTilde = 126,
}

/// ### Keyboard modifier key states.
///
/// Represents the state of modifier keys (Ctrl, Alt, Shift, Meta) during
/// keyboard input events. Provides efficient bit-packed storage and
/// convenient access methods for modifier combinations.
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::input::KeyModifiers;
/// // Create modifier state
/// let modifiers = KeyModifiers::new()
///     .with_ctrl(true)
///     .with_shift(true);
///
/// // Check individual modifiers
/// assert!(modifiers.ctrl());
/// assert!(modifiers.shift());
/// assert!(!modifiers.alt());
/// assert!(!modifiers.meta());
///
/// // Check combinations
/// assert!(modifiers.ctrl_shift());
/// assert!(!modifiers.ctrl_alt());
/// ```
#[derive(Copy, Clone, Debug, PartialEq, Eq, Hash)]
pub struct KeyModifiers {
    /// Bit-packed modifier flags
    flags: u8,
}

impl KeyModifiers {
    /// Ctrl key flag
    const CTRL: u8 = 1 << 0;
    /// Alt key flag
    const ALT: u8 = 1 << 1;
    /// Shift key flag
    const SHIFT: u8 = 1 << 2;
    /// Meta/Windows key flag
    const META: u8 = 1 << 3;

    /// ### Creates a new KeyModifiers with no modifiers pressed.
    ///
    /// Returns a KeyModifiers instance with all modifier flags cleared.
    /// This is the default state when no modifier keys are active.
    ///
    /// # Returns
    /// KeyModifiers with no active modifiers.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new();
    /// assert!(!modifiers.ctrl());
    /// assert!(!modifiers.alt());
    /// assert!(!modifiers.shift());
    /// assert!(!modifiers.meta());
    /// ```
    #[inline]
    pub const fn new() -> Self {
        Self { flags: 0 }
    }

    /// ### Sets the Ctrl modifier state.
    ///
    /// Returns a new KeyModifiers instance with the Ctrl flag set to the specified state.
    /// This method uses builder pattern for convenient modifier combinations.
    ///
    /// # Parameters
    /// - `pressed`: Whether the Ctrl key is pressed
    ///
    /// # Returns
    /// New KeyModifiers instance with updated Ctrl state.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_ctrl(true);
    /// assert!(modifiers.ctrl());
    /// ```
    #[inline]
    pub const fn with_ctrl(mut self, pressed: bool) -> Self {
        if pressed {
            self.flags |= Self::CTRL;
        } else {
            self.flags &= !Self::CTRL;
        }
        self
    }

    /// ### Sets the Alt modifier state.
    ///
    /// Returns a new KeyModifiers instance with the Alt flag set to the specified state.
    /// This method uses builder pattern for convenient modifier combinations.
    ///
    /// # Parameters
    /// - `pressed`: Whether the Alt key is pressed
    ///
    /// # Returns
    /// New KeyModifiers instance with updated Alt state.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_alt(true);
    /// assert!(modifiers.alt());
    /// ```
    #[inline]
    pub const fn with_alt(mut self, pressed: bool) -> Self {
        if pressed {
            self.flags |= Self::ALT;
        } else {
            self.flags &= !Self::ALT;
        }
        self
    }

    /// ### Sets the Shift modifier state.
    ///
    /// Returns a new KeyModifiers instance with the Shift flag set to the specified state.
    /// This method uses builder pattern for convenient modifier combinations.
    ///
    /// # Parameters
    /// - `pressed`: Whether the Shift key is pressed
    ///
    /// # Returns
    /// New KeyModifiers instance with updated Shift state.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_shift(true);
    /// assert!(modifiers.shift());
    /// ```
    #[inline]
    pub const fn with_shift(mut self, pressed: bool) -> Self {
        if pressed {
            self.flags |= Self::SHIFT;
        } else {
            self.flags &= !Self::SHIFT;
        }
        self
    }

    /// ### Sets the Meta modifier state.
    ///
    /// Returns a new KeyModifiers instance with the Meta flag set to the specified state.
    /// This method uses builder pattern for convenient modifier combinations.
    ///
    /// # Parameters
    /// - `pressed`: Whether the Meta/Windows key is pressed
    ///
    /// # Returns
    /// New KeyModifiers instance with updated Meta state.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_meta(true);
    /// assert!(modifiers.meta());
    /// ```
    #[inline]
    pub const fn with_meta(mut self, pressed: bool) -> Self {
        if pressed {
            self.flags |= Self::META;
        } else {
            self.flags &= !Self::META;
        }
        self
    }

    /// ### Checks if the Ctrl key is pressed.
    ///
    /// Returns true if the Ctrl modifier is active in this KeyModifiers instance.
    ///
    /// # Returns
    /// True if Ctrl is pressed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_ctrl(true);
    /// assert!(modifiers.ctrl());
    /// ```
    #[inline]
    pub const fn ctrl(self) -> bool {
        (self.flags & Self::CTRL) != 0
    }

    /// ### Checks if the Alt key is pressed.
    ///
    /// Returns true if the Alt modifier is active in this KeyModifiers instance.
    ///
    /// # Returns
    /// True if Alt is pressed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_alt(true);
    /// assert!(modifiers.alt());
    /// ```
    #[inline]
    pub const fn alt(self) -> bool {
        (self.flags & Self::ALT) != 0
    }

    /// ### Checks if the Shift key is pressed.
    ///
    /// Returns true if the Shift modifier is active in this KeyModifiers instance.
    ///
    /// # Returns
    /// True if Shift is pressed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_shift(true);
    /// assert!(modifiers.shift());
    /// ```
    #[inline]
    pub const fn shift(self) -> bool {
        (self.flags & Self::SHIFT) != 0
    }

    /// ### Checks if the Meta key is pressed.
    ///
    /// Returns true if the Meta/Windows modifier is active in this KeyModifiers instance.
    ///
    /// # Returns
    /// True if Meta is pressed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_meta(true);
    /// assert!(modifiers.meta());
    /// ```
    #[inline]
    pub const fn meta(self) -> bool {
        (self.flags & Self::META) != 0
    }

    /// ### Checks if both Ctrl and Shift are pressed.
    ///
    /// Convenience method for checking common modifier combinations.
    ///
    /// # Returns
    /// True if both Ctrl and Shift are pressed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_ctrl(true).with_shift(true);
    /// assert!(modifiers.ctrl_shift());
    /// ```
    #[inline]
    pub const fn ctrl_shift(self) -> bool {
        self.ctrl() && self.shift()
    }

    /// ### Checks if both Ctrl and Alt are pressed.
    ///
    /// Convenience method for checking common modifier combinations.
    ///
    /// # Returns
    /// True if both Ctrl and Alt are pressed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_ctrl(true).with_alt(true);
    /// assert!(modifiers.ctrl_alt());
    /// ```
    #[inline]
    pub const fn ctrl_alt(self) -> bool {
        self.ctrl() && self.alt()
    }

    /// ### Checks if no modifiers are pressed.
    ///
    /// Returns true if this KeyModifiers instance has no active modifier flags.
    ///
    /// # Returns
    /// True if no modifiers are pressed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new();
    /// assert!(modifiers.is_empty());
    /// 
    /// let with_ctrl = modifiers.with_ctrl(true);
    /// assert!(!with_ctrl.is_empty());
    /// ```
    #[inline]
    pub const fn is_empty(self) -> bool {
        self.flags == 0
    }
}

impl Default for KeyModifiers {
    /// ### Creates the default KeyModifiers value.
    ///
    /// Returns KeyModifiers::new() with no modifiers pressed.
    /// This is equivalent to calling KeyModifiers::new().
    ///
    /// # Returns
    /// KeyModifiers with no active modifiers.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::default();
    /// assert!(modifiers.is_empty());
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}

impl std::fmt::Display for KeyModifiers {
    /// ### Formats the KeyModifiers for display.
    ///
    /// Provides human-readable representation of active modifier keys.
    /// Shows combinations like "Ctrl+Shift" or "None" for empty modifiers.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::KeyModifiers;
    /// let modifiers = KeyModifiers::new().with_ctrl(true).with_shift(true);
    /// assert_eq!(format!("{}", modifiers), "Ctrl+Shift");
    /// 
    /// let empty = KeyModifiers::new();
    /// assert_eq!(format!("{}", empty), "None");
    /// ```
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        if self.is_empty() {
            return write!(f, "None");
        }

        let mut parts = Vec::new();
        if self.ctrl() { parts.push("Ctrl"); }
        if self.alt() { parts.push("Alt"); }
        if self.shift() { parts.push("Shift"); }
        if self.meta() { parts.push("Meta"); }

        write!(f, "{}", parts.join("+"))
    }
}
