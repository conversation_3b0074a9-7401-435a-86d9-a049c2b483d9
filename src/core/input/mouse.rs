/// ### Godot-compatible mouse input handling.
///
/// This module provides comprehensive mouse input support following <PERSON><PERSON>'s
/// mouse handling patterns. It includes mouse buttons, position tracking,
/// motion events, and scroll wheel support for complete mouse interaction.
///
/// ## Mouse Features
///
/// - **Button Support**: Left, right, middle, and extended mouse buttons
/// - **Position Tracking**: Absolute and relative mouse positioning
/// - **Motion Events**: Mouse movement with velocity and acceleration
/// - **Scroll Wheel**: Vertical and horizontal scroll wheel support
/// - **Double-Click Detection**: Automatic double-click timing and detection
/// - **Performance Optimized**: Efficient mouse state tracking and event processing
///
/// ## Examples
///
/// ```rust
/// use verturion::core::input::{MouseButton, MouseMotion};
/// use verturion::core::math::Vector2;
///
/// // Mouse button handling
/// let left_button = MouseButton::Left;
/// let right_button = MouseButton::Right;
/// let wheel_up = MouseButton::WheelUp;
///
/// // Mouse motion tracking
/// let motion = MouseMotion::new(
///     Vector2::new(100.0, 200.0),  // position
///     Vector2::new(5.0, -3.0),     // relative movement
///     Vector2::new(1.2, 0.8)       // velocity
/// );
///
/// // Access motion data
/// println!("Mouse at: {}", motion.position());
/// println!("Moved by: {}", motion.relative());
/// ```
use crate::core::math::Vector2;

/// ### Mouse button enumeration following Godot's button mapping.
///
/// Represents all supported mouse buttons including standard buttons
/// (left, right, middle) and extended buttons for gaming mice.
/// Also includes scroll wheel directions as button events.
///
/// ## Button Mapping
///
/// The button values match Godot's MOUSE_BUTTON_* constants for
/// compatibility with existing Godot projects and input handling.
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::input::MouseButton;
/// // Standard mouse buttons
/// let primary = MouseButton::Left;
/// let secondary = MouseButton::Right;
/// let tertiary = MouseButton::Middle;
///
/// // Scroll wheel as buttons
/// let scroll_up = MouseButton::WheelUp;
/// let scroll_down = MouseButton::WheelDown;
///
/// // Extended buttons for gaming mice
/// let thumb_back = MouseButton::XButton1;
/// let thumb_forward = MouseButton::XButton2;
/// ```
#[derive(Copy, Clone, Debug, PartialEq, Eq, Hash)]
#[repr(u8)]
pub enum MouseButton {
    /// No mouse button
    None = 0,
    /// Left mouse button (primary button)
    Left = 1,
    /// Right mouse button (secondary button)
    Right = 2,
    /// Middle mouse button (wheel button)
    Middle = 3,
    /// Mouse wheel up (scroll up)
    WheelUp = 4,
    /// Mouse wheel down (scroll down)
    WheelDown = 5,
    /// Mouse wheel left (horizontal scroll left)
    WheelLeft = 6,
    /// Mouse wheel right (horizontal scroll right)
    WheelRight = 7,
    /// Extra button 1 (typically thumb back button)
    XButton1 = 8,
    /// Extra button 2 (typically thumb forward button)
    XButton2 = 9,
}

impl MouseButton {
    /// ### Checks if this is a scroll wheel button.
    ///
    /// Returns true for wheel up, down, left, and right buttons.
    /// These buttons typically generate press events without release.
    ///
    /// # Returns
    /// True if this is a scroll wheel button, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseButton;
    /// assert!(MouseButton::WheelUp.is_wheel());
    /// assert!(MouseButton::WheelDown.is_wheel());
    /// assert!(!MouseButton::Left.is_wheel());
    /// assert!(!MouseButton::Right.is_wheel());
    /// ```
    #[inline]
    pub const fn is_wheel(self) -> bool {
        matches!(self, 
            MouseButton::WheelUp | 
            MouseButton::WheelDown | 
            MouseButton::WheelLeft | 
            MouseButton::WheelRight
        )
    }

    /// ### Checks if this is a standard clickable button.
    ///
    /// Returns true for left, right, middle, and extended buttons.
    /// These buttons can be held down and generate both press and release events.
    ///
    /// # Returns
    /// True if this is a clickable button, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseButton;
    /// assert!(MouseButton::Left.is_clickable());
    /// assert!(MouseButton::Right.is_clickable());
    /// assert!(MouseButton::Middle.is_clickable());
    /// assert!(!MouseButton::WheelUp.is_clickable());
    /// ```
    #[inline]
    pub const fn is_clickable(self) -> bool {
        matches!(self,
            MouseButton::Left |
            MouseButton::Right |
            MouseButton::Middle |
            MouseButton::XButton1 |
            MouseButton::XButton2
        )
    }

    /// ### Gets the button index for array-based storage.
    ///
    /// Returns a zero-based index suitable for storing button states
    /// in arrays or bit fields. Useful for efficient button state tracking.
    ///
    /// # Returns
    /// Zero-based button index.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseButton;
    /// assert_eq!(MouseButton::Left.index(), 1);
    /// assert_eq!(MouseButton::Right.index(), 2);
    /// assert_eq!(MouseButton::Middle.index(), 3);
    /// ```
    #[inline]
    pub const fn index(self) -> usize {
        self as usize
    }
}

impl Default for MouseButton {
    /// ### Creates the default MouseButton value.
    ///
    /// Returns MouseButton::None representing no button pressed.
    /// This is used when no specific button is being referenced.
    ///
    /// # Returns
    /// MouseButton::None as the default value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseButton;
    /// let button = MouseButton::default();
    /// assert_eq!(button, MouseButton::None);
    /// ```
    #[inline]
    fn default() -> Self {
        MouseButton::None
    }
}

impl std::fmt::Display for MouseButton {
    /// ### Formats the MouseButton for display.
    ///
    /// Provides human-readable names for mouse buttons suitable
    /// for user interfaces and debugging output.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseButton;
    /// assert_eq!(format!("{}", MouseButton::Left), "Left Button");
    /// assert_eq!(format!("{}", MouseButton::Right), "Right Button");
    /// assert_eq!(format!("{}", MouseButton::WheelUp), "Wheel Up");
    /// ```
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let name = match self {
            MouseButton::None => "None",
            MouseButton::Left => "Left Button",
            MouseButton::Right => "Right Button",
            MouseButton::Middle => "Middle Button",
            MouseButton::WheelUp => "Wheel Up",
            MouseButton::WheelDown => "Wheel Down",
            MouseButton::WheelLeft => "Wheel Left",
            MouseButton::WheelRight => "Wheel Right",
            MouseButton::XButton1 => "Extra Button 1",
            MouseButton::XButton2 => "Extra Button 2",
        };
        write!(f, "{}", name)
    }
}

/// ### Mouse motion event data.
///
/// Contains comprehensive mouse movement information including absolute position,
/// relative movement, and velocity data. Used for mouse look, camera control,
/// and precise cursor tracking in games and applications.
///
/// ## Motion Data
///
/// - **Position**: Absolute mouse coordinates in screen/window space
/// - **Relative**: Movement delta since last motion event
/// - **Velocity**: Current mouse movement velocity for smooth interpolation
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::input::MouseMotion;
/// # use verturion::core::math::Vector2;
/// // Create mouse motion event
/// let motion = MouseMotion::new(
///     Vector2::new(320.0, 240.0),  // cursor position
///     Vector2::new(10.0, -5.0),    // moved right 10, up 5
///     Vector2::new(2.5, 1.2)       // current velocity
/// );
///
/// // Use for camera control
/// let sensitivity = 0.1;
/// let camera_rotation = motion.relative() * sensitivity;
/// ```
#[derive(Copy, Clone, Debug, PartialEq)]
pub struct MouseMotion {
    /// Absolute mouse position in screen coordinates
    position: Vector2,
    /// Relative movement since last motion event
    relative: Vector2,
    /// Current mouse velocity for smooth movement
    velocity: Vector2,
}

impl MouseMotion {
    /// ### Creates a new MouseMotion event.
    ///
    /// Constructs a mouse motion event with position, relative movement,
    /// and velocity data. All parameters use Vector2 for 2D coordinates.
    ///
    /// # Parameters
    /// - `position`: Absolute mouse position in screen coordinates
    /// - `relative`: Movement delta since last motion event
    /// - `velocity`: Current mouse movement velocity
    ///
    /// # Returns
    /// New MouseMotion instance with the specified data.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseMotion;
    /// # use verturion::core::math::Vector2;
    /// let motion = MouseMotion::new(
    ///     Vector2::new(100.0, 200.0),
    ///     Vector2::new(5.0, -3.0),
    ///     Vector2::new(1.2, 0.8)
    /// );
    /// ```
    #[inline]
    pub const fn new(position: Vector2, relative: Vector2, velocity: Vector2) -> Self {
        Self {
            position,
            relative,
            velocity,
        }
    }

    /// ### Gets the absolute mouse position.
    ///
    /// Returns the current mouse cursor position in screen or window coordinates.
    /// This is the absolute position where the mouse cursor is located.
    ///
    /// # Returns
    /// Vector2 containing the absolute mouse position.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseMotion;
    /// # use verturion::core::math::Vector2;
    /// let motion = MouseMotion::new(
    ///     Vector2::new(320.0, 240.0),
    ///     Vector2::ZERO,
    ///     Vector2::ZERO
    /// );
    /// assert_eq!(motion.position(), Vector2::new(320.0, 240.0));
    /// ```
    #[inline]
    pub const fn position(self) -> Vector2 {
        self.position
    }

    /// ### Gets the relative mouse movement.
    ///
    /// Returns the movement delta since the last motion event.
    /// Positive X values indicate rightward movement, positive Y values indicate downward movement.
    ///
    /// # Returns
    /// Vector2 containing the relative movement delta.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseMotion;
    /// # use verturion::core::math::Vector2;
    /// let motion = MouseMotion::new(
    ///     Vector2::ZERO,
    ///     Vector2::new(10.0, -5.0),
    ///     Vector2::ZERO
    /// );
    /// assert_eq!(motion.relative(), Vector2::new(10.0, -5.0));
    /// ```
    #[inline]
    pub const fn relative(self) -> Vector2 {
        self.relative
    }

    /// ### Gets the mouse movement velocity.
    ///
    /// Returns the current velocity of mouse movement for smooth interpolation
    /// and acceleration-based input handling.
    ///
    /// # Returns
    /// Vector2 containing the current mouse velocity.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseMotion;
    /// # use verturion::core::math::Vector2;
    /// let motion = MouseMotion::new(
    ///     Vector2::ZERO,
    ///     Vector2::ZERO,
    ///     Vector2::new(2.5, 1.2)
    /// );
    /// assert_eq!(motion.velocity(), Vector2::new(2.5, 1.2));
    /// ```
    #[inline]
    pub const fn velocity(self) -> Vector2 {
        self.velocity
    }

    /// ### Gets the movement speed magnitude.
    ///
    /// Returns the length of the relative movement vector, representing
    /// the total distance moved regardless of direction.
    ///
    /// # Returns
    /// Movement speed as a floating-point value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseMotion;
    /// # use verturion::core::math::Vector2;
    /// let motion = MouseMotion::new(
    ///     Vector2::ZERO,
    ///     Vector2::new(3.0, 4.0),  // 3-4-5 triangle
    ///     Vector2::ZERO
    /// );
    /// assert!((motion.speed() - 5.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn speed(self) -> f32 {
        self.relative.length()
    }

    /// ### Checks if the mouse is moving.
    ///
    /// Returns true if there is any relative movement in this motion event.
    /// Useful for detecting when the mouse has stopped moving.
    ///
    /// # Returns
    /// True if the mouse is moving, false if stationary.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseMotion;
    /// # use verturion::core::math::Vector2;
    /// let moving = MouseMotion::new(
    ///     Vector2::ZERO,
    ///     Vector2::new(1.0, 0.0),
    ///     Vector2::ZERO
    /// );
    /// assert!(moving.is_moving());
    ///
    /// let stationary = MouseMotion::new(
    ///     Vector2::ZERO,
    ///     Vector2::ZERO,
    ///     Vector2::ZERO
    /// );
    /// assert!(!stationary.is_moving());
    /// ```
    #[inline]
    pub fn is_moving(self) -> bool {
        !self.relative.is_zero_approx()
    }
}

impl Default for MouseMotion {
    /// ### Creates the default MouseMotion value.
    ///
    /// Returns a MouseMotion with zero position, movement, and velocity.
    /// This represents a stationary mouse with no motion data.
    ///
    /// # Returns
    /// MouseMotion with all zero values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseMotion;
    /// # use verturion::core::math::Vector2;
    /// let motion = MouseMotion::default();
    /// assert_eq!(motion.position(), Vector2::ZERO);
    /// assert_eq!(motion.relative(), Vector2::ZERO);
    /// assert_eq!(motion.velocity(), Vector2::ZERO);
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new(Vector2::ZERO, Vector2::ZERO, Vector2::ZERO)
    }
}

impl std::fmt::Display for MouseMotion {
    /// ### Formats the MouseMotion for display.
    ///
    /// Provides human-readable representation of mouse motion data
    /// including position, relative movement, and velocity.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::MouseMotion;
    /// # use verturion::core::math::Vector2;
    /// let motion = MouseMotion::new(
    ///     Vector2::new(100.0, 200.0),
    ///     Vector2::new(5.0, -3.0),
    ///     Vector2::new(1.2, 0.8)
    /// );
    /// println!("{}", motion); // Shows position, relative, and velocity
    /// ```
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "MouseMotion[pos: {}, rel: {}, vel: {}]", 
               self.position, self.relative, self.velocity)
    }
}
