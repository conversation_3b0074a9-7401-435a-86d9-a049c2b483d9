/// ### Error handling module for Godot-compatible error types.
///
/// This module provides comprehensive error handling capabilities following Godot's
/// error system patterns. It includes the main Error enum with all Godot error codes
/// and seamless integration with the Variant system.
///
/// ## Features
///
/// - **Complete Error Coverage**: All Godot error codes (OK, FAILED, ERR_UNAVAILABLE, etc.)
/// - **Type Safety**: Strong typing with compile-time error checking
/// - **Variant Integration**: Seamless integration with the Variant type system
/// - **Performance Optimized**: Copy semantics and efficient hash functions
/// - **Godot Compatible**: API and behavior patterns matching Godot Engine
///
/// ## Examples
///
/// ```rust
/// use verturion::core::error::Error;
/// use verturion::core::variant::Variant;
///
/// // Create error instances
/// let ok = Error::OK;
/// let failed = Error::FAILED;
/// let unavailable = Error::ERR_UNAVAILABLE;
///
/// // Check error status
/// assert!(ok.is_ok());
/// assert!(failed.is_error());
/// assert!(unavailable.is_error());
///
/// // Get error information
/// println!("Error name: {}", failed.get_error_name());
/// println!("Error message: {}", failed.to_string());
///
/// // Use in Variant system
/// let error_variant = Variant::from(Error::ERR_FILE_NOT_FOUND);
/// assert!(error_variant.is_error());
/// ```

mod error;

pub use error::Error;
