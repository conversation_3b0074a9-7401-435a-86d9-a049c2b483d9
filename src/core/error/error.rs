/// ### Godot-compatible Error enum for comprehensive error handling.
///
/// This enum represents all possible error states in the Godot Engine, providing
/// type-safe error handling with performance optimization for game development.
/// The Error type implements Copy semantics for efficient passing and supports
/// seamless integration with the Variant system.
///
/// ## Error Categories
///
/// - **Success**: OK - No error occurred
/// - **General**: FAILED - Generic failure
/// - **System**: File, network, and system-level errors
/// - **Configuration**: Setup and configuration errors
/// - **Runtime**: Memory, parsing, and execution errors
/// - **Security**: Permission and authentication errors
///
/// ## Performance Characteristics
///
/// - **Copy Semantics**: Efficient passing by value
/// - **Zero-Cost Abstractions**: No runtime overhead
/// - **Optimized Hashing**: Fast hash computation for collections
/// - **Compact Representation**: Single byte storage
///
/// ## Examples
///
/// ```rust
/// use verturion::core::error::Error;
///
/// // Create and check errors
/// let result = Error::OK;
/// assert!(result.is_ok());
/// assert!(!result.is_error());
///
/// let failure = Error::FAILED;
/// assert!(!failure.is_ok());
/// assert!(failure.is_error());
///
/// // Get error information
/// println!("Error: {} - {}", failure.get_error_name(), failure.to_string());
///
/// // Use in error handling
/// fn process_file() -> Error {
///     // ... file processing logic
///     Error::ERR_FILE_NOT_FOUND
/// }
///
/// match process_file() {
///     Error::OK => println!("Success!"),
///     error => println!("Failed: {}", error),
/// }
/// ```
#[derive(Copy, Clone, Debug, PartialEq, Eq, Hash)]
#[repr(u8)]
pub enum Error {
    /// No error occurred - operation completed successfully.
    OK = 0,
    /// Generic failure - operation failed for unspecified reasons.
    FAILED = 1,
    /// Resource or service is unavailable.
    ERR_UNAVAILABLE = 2,
    /// Feature or functionality is not configured.
    ERR_UNCONFIGURED = 3,
    /// Operation is not authorized or lacks permissions.
    ERR_UNAUTHORIZED = 4,
    /// Invalid parameter provided to function or method.
    ERR_INVALID_PARAMETER = 5,
    /// Insufficient memory to complete operation.
    ERR_OUT_OF_MEMORY = 6,
    /// File was not found at specified path.
    ERR_FILE_NOT_FOUND = 7,
    /// File format is invalid or corrupted.
    ERR_FILE_BAD_DRIVE = 8,
    /// File path is invalid or malformed.
    ERR_FILE_BAD_PATH = 9,
    /// No permission to access file or directory.
    ERR_FILE_NO_PERMISSION = 10,
    /// File is already in use by another process.
    ERR_FILE_ALREADY_IN_USE = 11,
    /// Cannot open file for reading or writing.
    ERR_FILE_CANT_OPEN = 12,
    /// Cannot write to file or directory.
    ERR_FILE_CANT_WRITE = 13,
    /// Cannot read from file or directory.
    ERR_FILE_CANT_READ = 14,
    /// File is not recognized or has unknown format.
    ERR_FILE_UNRECOGNIZED = 15,
    /// File is corrupted or contains invalid data.
    ERR_FILE_CORRUPT = 16,
    /// Reached end of file unexpectedly.
    ERR_FILE_EOF = 17,
    /// Cannot resolve network address or hostname.
    ERR_CANT_RESOLVE = 18,
    /// Cannot establish network connection.
    ERR_CANT_CONNECT = 19,
    /// Cannot create new resource or object.
    ERR_CANT_CREATE = 20,
    /// Query operation failed or returned invalid results.
    ERR_QUERY_FAILED = 21,
    /// Resource is already in use or occupied.
    ERR_ALREADY_IN_USE = 22,
    /// Resource is locked and cannot be accessed.
    ERR_LOCKED = 23,
    /// Operation timed out before completion.
    ERR_TIMEOUT = 24,
    /// Cannot acquire exclusive lock on resource.
    ERR_CANT_ACQUIRE_RESOURCE = 25,
    /// Invalid data format or structure.
    ERR_INVALID_DATA = 26,
    /// Invalid declaration or statement.
    ERR_INVALID_DECLARATION = 27,
    /// Duplicate name or identifier found.
    ERR_DUPLICATE_SYMBOL = 28,
    /// Parsing operation failed.
    ERR_PARSE_ERROR = 29,
    /// Resource is busy and cannot be accessed.
    ERR_BUSY = 30,
    /// Operation was skipped or bypassed.
    ERR_SKIP = 31,
    /// Help information or documentation requested.
    ERR_HELP = 32,
    /// Bug or internal error detected.
    ERR_BUG = 33,
    /// Printer or printing system error.
    ERR_PRINTER_ON_FIRE = 34,
}

impl Error {
    /// ### Creates a new Error instance.
    ///
    /// Returns the OK error state, indicating successful operation.
    /// This is the default constructor for Error instances.
    ///
    /// # Returns
    /// Error::OK representing successful operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::error::Error;
    /// let error = Error::new();
    /// assert_eq!(error, Error::OK);
    /// assert!(error.is_ok());
    /// ```
    #[inline]
    pub const fn new() -> Self {
        Error::OK
    }

    /// ### Checks if the error represents a successful operation.
    ///
    /// Returns true if the error is OK, indicating no error occurred.
    /// This is the primary method for checking operation success.
    ///
    /// # Returns
    /// True if error is OK, false for any error condition.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::error::Error;
    /// assert!(Error::OK.is_ok());
    /// assert!(!Error::FAILED.is_ok());
    /// assert!(!Error::ERR_FILE_NOT_FOUND.is_ok());
    /// ```
    #[inline]
    pub const fn is_ok(self) -> bool {
        matches!(self, Error::OK)
    }

    /// ### Checks if the error represents a failure condition.
    ///
    /// Returns true for any error condition except OK.
    /// This is the complement of is_ok() for error checking.
    ///
    /// # Returns
    /// True for any error condition, false only for OK.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::error::Error;
    /// assert!(!Error::OK.is_error());
    /// assert!(Error::FAILED.is_error());
    /// assert!(Error::ERR_UNAVAILABLE.is_error());
    /// ```
    #[inline]
    pub const fn is_error(self) -> bool {
        !self.is_ok()
    }

    /// ### Gets the string name of the error code.
    ///
    /// Returns the exact name of the error variant as used in code.
    /// This is useful for debugging and logging purposes.
    ///
    /// # Returns
    /// Static string slice containing the error name.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::error::Error;
    /// assert_eq!(Error::OK.get_error_name(), "OK");
    /// assert_eq!(Error::FAILED.get_error_name(), "FAILED");
    /// assert_eq!(Error::ERR_FILE_NOT_FOUND.get_error_name(), "ERR_FILE_NOT_FOUND");
    /// ```
    #[inline]
    pub const fn get_error_name(self) -> &'static str {
        match self {
            Error::OK => "OK",
            Error::FAILED => "FAILED",
            Error::ERR_UNAVAILABLE => "ERR_UNAVAILABLE",
            Error::ERR_UNCONFIGURED => "ERR_UNCONFIGURED",
            Error::ERR_UNAUTHORIZED => "ERR_UNAUTHORIZED",
            Error::ERR_INVALID_PARAMETER => "ERR_INVALID_PARAMETER",
            Error::ERR_OUT_OF_MEMORY => "ERR_OUT_OF_MEMORY",
            Error::ERR_FILE_NOT_FOUND => "ERR_FILE_NOT_FOUND",
            Error::ERR_FILE_BAD_DRIVE => "ERR_FILE_BAD_DRIVE",
            Error::ERR_FILE_BAD_PATH => "ERR_FILE_BAD_PATH",
            Error::ERR_FILE_NO_PERMISSION => "ERR_FILE_NO_PERMISSION",
            Error::ERR_FILE_ALREADY_IN_USE => "ERR_FILE_ALREADY_IN_USE",
            Error::ERR_FILE_CANT_OPEN => "ERR_FILE_CANT_OPEN",
            Error::ERR_FILE_CANT_WRITE => "ERR_FILE_CANT_WRITE",
            Error::ERR_FILE_CANT_READ => "ERR_FILE_CANT_READ",
            Error::ERR_FILE_UNRECOGNIZED => "ERR_FILE_UNRECOGNIZED",
            Error::ERR_FILE_CORRUPT => "ERR_FILE_CORRUPT",
            Error::ERR_FILE_EOF => "ERR_FILE_EOF",
            Error::ERR_CANT_RESOLVE => "ERR_CANT_RESOLVE",
            Error::ERR_CANT_CONNECT => "ERR_CANT_CONNECT",
            Error::ERR_CANT_CREATE => "ERR_CANT_CREATE",
            Error::ERR_QUERY_FAILED => "ERR_QUERY_FAILED",
            Error::ERR_ALREADY_IN_USE => "ERR_ALREADY_IN_USE",
            Error::ERR_LOCKED => "ERR_LOCKED",
            Error::ERR_TIMEOUT => "ERR_TIMEOUT",
            Error::ERR_CANT_ACQUIRE_RESOURCE => "ERR_CANT_ACQUIRE_RESOURCE",
            Error::ERR_INVALID_DATA => "ERR_INVALID_DATA",
            Error::ERR_INVALID_DECLARATION => "ERR_INVALID_DECLARATION",
            Error::ERR_DUPLICATE_SYMBOL => "ERR_DUPLICATE_SYMBOL",
            Error::ERR_PARSE_ERROR => "ERR_PARSE_ERROR",
            Error::ERR_BUSY => "ERR_BUSY",
            Error::ERR_SKIP => "ERR_SKIP",
            Error::ERR_HELP => "ERR_HELP",
            Error::ERR_BUG => "ERR_BUG",
            Error::ERR_PRINTER_ON_FIRE => "ERR_PRINTER_ON_FIRE",
        }
    }

    /// ### Gets the numeric error code.
    ///
    /// Returns the underlying numeric value of the error code.
    /// This matches Godot's error code numbering system.
    ///
    /// # Returns
    /// Numeric error code as u8.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::error::Error;
    /// assert_eq!(Error::OK.get_error_code(), 0);
    /// assert_eq!(Error::FAILED.get_error_code(), 1);
    /// assert_eq!(Error::ERR_FILE_NOT_FOUND.get_error_code(), 7);
    /// ```
    #[inline]
    pub const fn get_error_code(self) -> u8 {
        self as u8
    }

    /// ### Creates an Error from a numeric error code.
    ///
    /// Converts a numeric error code back to an Error enum variant.
    /// Returns None if the code doesn't correspond to a valid error.
    ///
    /// # Parameters
    /// - `code`: Numeric error code to convert
    ///
    /// # Returns
    /// Some(Error) if code is valid, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::error::Error;
    /// assert_eq!(Error::from_code(0), Some(Error::OK));
    /// assert_eq!(Error::from_code(1), Some(Error::FAILED));
    /// assert_eq!(Error::from_code(7), Some(Error::ERR_FILE_NOT_FOUND));
    /// assert_eq!(Error::from_code(255), None);
    /// ```
    #[inline]
    pub const fn from_code(code: u8) -> Option<Self> {
        match code {
            0 => Some(Error::OK),
            1 => Some(Error::FAILED),
            2 => Some(Error::ERR_UNAVAILABLE),
            3 => Some(Error::ERR_UNCONFIGURED),
            4 => Some(Error::ERR_UNAUTHORIZED),
            5 => Some(Error::ERR_INVALID_PARAMETER),
            6 => Some(Error::ERR_OUT_OF_MEMORY),
            7 => Some(Error::ERR_FILE_NOT_FOUND),
            8 => Some(Error::ERR_FILE_BAD_DRIVE),
            9 => Some(Error::ERR_FILE_BAD_PATH),
            10 => Some(Error::ERR_FILE_NO_PERMISSION),
            11 => Some(Error::ERR_FILE_ALREADY_IN_USE),
            12 => Some(Error::ERR_FILE_CANT_OPEN),
            13 => Some(Error::ERR_FILE_CANT_WRITE),
            14 => Some(Error::ERR_FILE_CANT_READ),
            15 => Some(Error::ERR_FILE_UNRECOGNIZED),
            16 => Some(Error::ERR_FILE_CORRUPT),
            17 => Some(Error::ERR_FILE_EOF),
            18 => Some(Error::ERR_CANT_RESOLVE),
            19 => Some(Error::ERR_CANT_CONNECT),
            20 => Some(Error::ERR_CANT_CREATE),
            21 => Some(Error::ERR_QUERY_FAILED),
            22 => Some(Error::ERR_ALREADY_IN_USE),
            23 => Some(Error::ERR_LOCKED),
            24 => Some(Error::ERR_TIMEOUT),
            25 => Some(Error::ERR_CANT_ACQUIRE_RESOURCE),
            26 => Some(Error::ERR_INVALID_DATA),
            27 => Some(Error::ERR_INVALID_DECLARATION),
            28 => Some(Error::ERR_DUPLICATE_SYMBOL),
            29 => Some(Error::ERR_PARSE_ERROR),
            30 => Some(Error::ERR_BUSY),
            31 => Some(Error::ERR_SKIP),
            32 => Some(Error::ERR_HELP),
            33 => Some(Error::ERR_BUG),
            34 => Some(Error::ERR_PRINTER_ON_FIRE),
            _ => None,
        }
    }
}

impl Default for Error {
    /// ### Creates the default Error value.
    ///
    /// Returns Error::OK as the default, representing successful operation.
    /// This follows the convention that default values represent success states.
    ///
    /// # Returns
    /// Error::OK as the default error state.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::error::Error;
    /// let error = Error::default();
    /// assert_eq!(error, Error::OK);
    /// assert!(error.is_ok());
    /// ```
    #[inline]
    fn default() -> Self {
        Error::OK
    }
}

impl std::fmt::Display for Error {
    /// ### Formats the Error for display.
    ///
    /// Provides human-readable error messages for each error code.
    /// These messages are suitable for user interfaces and logging.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::error::Error;
    /// assert_eq!(format!("{}", Error::OK), "Success");
    /// assert_eq!(format!("{}", Error::FAILED), "Operation failed");
    /// assert_eq!(format!("{}", Error::ERR_FILE_NOT_FOUND), "File not found");
    /// ```
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let message = match self {
            Error::OK => "Success",
            Error::FAILED => "Operation failed",
            Error::ERR_UNAVAILABLE => "Resource unavailable",
            Error::ERR_UNCONFIGURED => "Feature not configured",
            Error::ERR_UNAUTHORIZED => "Unauthorized access",
            Error::ERR_INVALID_PARAMETER => "Invalid parameter",
            Error::ERR_OUT_OF_MEMORY => "Out of memory",
            Error::ERR_FILE_NOT_FOUND => "File not found",
            Error::ERR_FILE_BAD_DRIVE => "Invalid drive",
            Error::ERR_FILE_BAD_PATH => "Invalid file path",
            Error::ERR_FILE_NO_PERMISSION => "No file permission",
            Error::ERR_FILE_ALREADY_IN_USE => "File already in use",
            Error::ERR_FILE_CANT_OPEN => "Cannot open file",
            Error::ERR_FILE_CANT_WRITE => "Cannot write file",
            Error::ERR_FILE_CANT_READ => "Cannot read file",
            Error::ERR_FILE_UNRECOGNIZED => "Unrecognized file format",
            Error::ERR_FILE_CORRUPT => "File corrupted",
            Error::ERR_FILE_EOF => "End of file reached",
            Error::ERR_CANT_RESOLVE => "Cannot resolve address",
            Error::ERR_CANT_CONNECT => "Cannot establish connection",
            Error::ERR_CANT_CREATE => "Cannot create resource",
            Error::ERR_QUERY_FAILED => "Query operation failed",
            Error::ERR_ALREADY_IN_USE => "Resource already in use",
            Error::ERR_LOCKED => "Resource locked",
            Error::ERR_TIMEOUT => "Operation timed out",
            Error::ERR_CANT_ACQUIRE_RESOURCE => "Cannot acquire resource",
            Error::ERR_INVALID_DATA => "Invalid data format",
            Error::ERR_INVALID_DECLARATION => "Invalid declaration",
            Error::ERR_DUPLICATE_SYMBOL => "Duplicate symbol",
            Error::ERR_PARSE_ERROR => "Parse error",
            Error::ERR_BUSY => "Resource busy",
            Error::ERR_SKIP => "Operation skipped",
            Error::ERR_HELP => "Help requested",
            Error::ERR_BUG => "Internal bug detected",
            Error::ERR_PRINTER_ON_FIRE => "Printer on fire",
        };
        write!(f, "{}", message)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_error_new() {
        let error = Error::new();
        assert_eq!(error, Error::OK);
        assert!(error.is_ok());
        assert!(!error.is_error());
    }

    #[test]
    fn test_error_default() {
        let error = Error::default();
        assert_eq!(error, Error::OK);
        assert!(error.is_ok());
        assert!(!error.is_error());
    }

    #[test]
    fn test_error_is_ok() {
        assert!(Error::OK.is_ok());
        assert!(!Error::FAILED.is_ok());
        assert!(!Error::ERR_FILE_NOT_FOUND.is_ok());
        assert!(!Error::ERR_OUT_OF_MEMORY.is_ok());
        assert!(!Error::ERR_PRINTER_ON_FIRE.is_ok());
    }

    #[test]
    fn test_error_is_error() {
        assert!(!Error::OK.is_error());
        assert!(Error::FAILED.is_error());
        assert!(Error::ERR_UNAVAILABLE.is_error());
        assert!(Error::ERR_FILE_NOT_FOUND.is_error());
        assert!(Error::ERR_TIMEOUT.is_error());
        assert!(Error::ERR_PRINTER_ON_FIRE.is_error());
    }

    #[test]
    fn test_error_get_error_name() {
        assert_eq!(Error::OK.get_error_name(), "OK");
        assert_eq!(Error::FAILED.get_error_name(), "FAILED");
        assert_eq!(Error::ERR_UNAVAILABLE.get_error_name(), "ERR_UNAVAILABLE");
        assert_eq!(Error::ERR_FILE_NOT_FOUND.get_error_name(), "ERR_FILE_NOT_FOUND");
        assert_eq!(Error::ERR_OUT_OF_MEMORY.get_error_name(), "ERR_OUT_OF_MEMORY");
        assert_eq!(Error::ERR_TIMEOUT.get_error_name(), "ERR_TIMEOUT");
        assert_eq!(Error::ERR_PRINTER_ON_FIRE.get_error_name(), "ERR_PRINTER_ON_FIRE");
    }

    #[test]
    fn test_error_get_error_code() {
        assert_eq!(Error::OK.get_error_code(), 0);
        assert_eq!(Error::FAILED.get_error_code(), 1);
        assert_eq!(Error::ERR_UNAVAILABLE.get_error_code(), 2);
        assert_eq!(Error::ERR_FILE_NOT_FOUND.get_error_code(), 7);
        assert_eq!(Error::ERR_OUT_OF_MEMORY.get_error_code(), 6);
        assert_eq!(Error::ERR_TIMEOUT.get_error_code(), 24);
        assert_eq!(Error::ERR_PRINTER_ON_FIRE.get_error_code(), 34);
    }

    #[test]
    fn test_error_from_code() {
        assert_eq!(Error::from_code(0), Some(Error::OK));
        assert_eq!(Error::from_code(1), Some(Error::FAILED));
        assert_eq!(Error::from_code(2), Some(Error::ERR_UNAVAILABLE));
        assert_eq!(Error::from_code(7), Some(Error::ERR_FILE_NOT_FOUND));
        assert_eq!(Error::from_code(24), Some(Error::ERR_TIMEOUT));
        assert_eq!(Error::from_code(34), Some(Error::ERR_PRINTER_ON_FIRE));

        // Test invalid codes
        assert_eq!(Error::from_code(35), None);
        assert_eq!(Error::from_code(100), None);
        assert_eq!(Error::from_code(255), None);
    }

    #[test]
    fn test_error_roundtrip_conversion() {
        // Test that all error codes can be converted to numeric and back
        let all_errors = [
            Error::OK, Error::FAILED, Error::ERR_UNAVAILABLE, Error::ERR_UNCONFIGURED,
            Error::ERR_UNAUTHORIZED, Error::ERR_INVALID_PARAMETER, Error::ERR_OUT_OF_MEMORY,
            Error::ERR_FILE_NOT_FOUND, Error::ERR_FILE_BAD_DRIVE, Error::ERR_FILE_BAD_PATH,
            Error::ERR_FILE_NO_PERMISSION, Error::ERR_FILE_ALREADY_IN_USE, Error::ERR_FILE_CANT_OPEN,
            Error::ERR_FILE_CANT_WRITE, Error::ERR_FILE_CANT_READ, Error::ERR_FILE_UNRECOGNIZED,
            Error::ERR_FILE_CORRUPT, Error::ERR_FILE_EOF, Error::ERR_CANT_RESOLVE,
            Error::ERR_CANT_CONNECT, Error::ERR_CANT_CREATE, Error::ERR_QUERY_FAILED,
            Error::ERR_ALREADY_IN_USE, Error::ERR_LOCKED, Error::ERR_TIMEOUT,
            Error::ERR_CANT_ACQUIRE_RESOURCE, Error::ERR_INVALID_DATA, Error::ERR_INVALID_DECLARATION,
            Error::ERR_DUPLICATE_SYMBOL, Error::ERR_PARSE_ERROR, Error::ERR_BUSY,
            Error::ERR_SKIP, Error::ERR_HELP, Error::ERR_BUG, Error::ERR_PRINTER_ON_FIRE,
        ];

        for &error in &all_errors {
            let code = error.get_error_code();
            let converted_back = Error::from_code(code);
            assert_eq!(converted_back, Some(error));
        }
    }

    #[test]
    fn test_error_display() {
        assert_eq!(format!("{}", Error::OK), "Success");
        assert_eq!(format!("{}", Error::FAILED), "Operation failed");
        assert_eq!(format!("{}", Error::ERR_UNAVAILABLE), "Resource unavailable");
        assert_eq!(format!("{}", Error::ERR_FILE_NOT_FOUND), "File not found");
        assert_eq!(format!("{}", Error::ERR_OUT_OF_MEMORY), "Out of memory");
        assert_eq!(format!("{}", Error::ERR_TIMEOUT), "Operation timed out");
        assert_eq!(format!("{}", Error::ERR_PRINTER_ON_FIRE), "Printer on fire");
    }

    #[test]
    fn test_error_debug() {
        let error = Error::ERR_FILE_NOT_FOUND;
        let debug_str = format!("{:?}", error);
        assert!(debug_str.contains("ERR_FILE_NOT_FOUND"));
    }

    #[test]
    fn test_error_equality() {
        assert_eq!(Error::OK, Error::OK);
        assert_eq!(Error::FAILED, Error::FAILED);
        assert_ne!(Error::OK, Error::FAILED);
        assert_ne!(Error::ERR_FILE_NOT_FOUND, Error::ERR_OUT_OF_MEMORY);
    }

    #[test]
    fn test_error_clone() {
        let error = Error::ERR_TIMEOUT;
        let cloned = error.clone();
        assert_eq!(error, cloned);
    }

    #[test]
    fn test_error_copy() {
        let error = Error::ERR_BUSY;
        let copied = error; // Copy semantics
        assert_eq!(error, copied);
        // Both variables should still be usable
        assert!(error.is_error());
        assert!(copied.is_error());
    }

    #[test]
    fn test_error_hash() {
        let mut map = HashMap::new();

        map.insert(Error::OK, "success");
        map.insert(Error::FAILED, "failure");
        map.insert(Error::ERR_FILE_NOT_FOUND, "file_not_found");

        assert_eq!(map.get(&Error::OK), Some(&"success"));
        assert_eq!(map.get(&Error::FAILED), Some(&"failure"));
        assert_eq!(map.get(&Error::ERR_FILE_NOT_FOUND), Some(&"file_not_found"));
        assert_eq!(map.get(&Error::ERR_TIMEOUT), None);
    }
}
