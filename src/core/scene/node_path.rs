//! Comprehensive NodePath implementation for Godot-compatible scene tree navigation.
//!
//! This module provides a complete NodePath implementation that maintains compatibility with
//! <PERSON><PERSON>'s NodePath class while providing efficient path parsing, validation, and navigation
//! capabilities for scene tree management. It supports both absolute and relative paths,
//! property access, and subname handling for comprehensive node identification.

use std::fmt;
use std::hash::{Hash, Hasher};
use crate::core::variant::{String as GodotString, StringName};

/// ### A Godot-compatible NodePath for scene tree navigation and node identification.
///
/// NodePath provides comprehensive scene tree path representation with support for both
/// absolute and relative paths, property access, and subname handling. It maintains
/// full compatibility with <PERSON><PERSON>'s NodePath class while providing efficient parsing
/// and navigation capabilities for game development.
///
/// ## Path Types
///
/// NodePath supports several path formats:
/// - **Absolute paths**: Start with "/" (e.g., "/root/Player", "/Main/UI/HealthBar")
/// - **Relative paths**: No leading "/" (e.g., "Child", "../Sibling", "Weapon/Barrel")
/// - **Property paths**: Include ":" for property access (e.g., "Player:position", "Enemy:health")
/// - **Subname paths**: Include "%" for resource properties (e.g., "Sprite%texture", "Audio%stream")
///
/// ## Use Cases
///
/// NodePath is ideal for:
/// - **Scene Tree Navigation**: Finding and referencing nodes in the scene hierarchy
/// - **Node Identification**: Unique identification of nodes across scene trees
/// - **Property Access**: Accessing node properties and resources dynamically
/// - **Animation Targets**: Specifying animation targets and property paths
/// - **Signal Connections**: Identifying signal sources and targets
/// - **Save/Load Systems**: Serializing node references for game state persistence
///
/// # Examples
/// ```
/// # use verturion::core::scene::NodePath;
/// // Create various path types
/// let absolute = NodePath::from("/root/Player/Weapon");
/// let relative = NodePath::from("../Enemy");
/// let property = NodePath::from("Player:position");
/// let subname = NodePath::from("Sprite%texture");
///
/// // Path analysis
/// assert!(absolute.is_absolute());
/// assert!(!relative.is_absolute());
/// assert!(property.has_property());
/// assert!(subname.has_subname());
///
/// // Path components
/// assert_eq!(absolute.get_name_count(), 3);
/// assert_eq!(absolute.get_name(1), "Player");
/// ```
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct NodePath {
    /// The original path string for reference
    path: GodotString,
    /// Parsed node names in the path
    names: Vec<StringName>,
    /// Property name if this is a property path
    property: Option<StringName>,
    /// Subname if this is a subname path
    subname: Option<StringName>,
    /// Whether this is an absolute path (starts with "/")
    absolute: bool,
}

impl NodePath {
    /// ### Creates a new empty NodePath.
    ///
    /// # Returns
    /// An empty NodePath that represents no path.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let empty = NodePath::new();
    /// assert!(empty.is_empty());
    /// assert!(!empty.is_absolute());
    /// ```
    #[inline]
    pub fn new() -> Self {
        Self {
            path: GodotString::new(),
            names: Vec::new(),
            property: None,
            subname: None,
            absolute: false,
        }
    }

    /// ### Creates a NodePath from a string path.
    ///
    /// Parses the path string and extracts components including node names,
    /// properties, and subnames according to Godot's path syntax.
    ///
    /// # Parameters
    /// - `path`: The path string to parse
    ///
    /// # Returns
    /// A new NodePath with parsed components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let path = NodePath::from("/root/Player:position");
    /// assert!(path.is_absolute());
    /// assert!(path.has_property());
    /// assert_eq!(path.get_property(), Some("position"));
    /// ```
    #[inline]
    pub fn from(path: &str) -> Self {
        let mut node_path = Self {
            path: GodotString::from(path),
            names: Vec::new(),
            property: None,
            subname: None,
            absolute: false,
        };

        node_path.parse_path(path);
        node_path
    }

    /// ### Parses a path string and populates the NodePath components.
    ///
    /// This is the core parsing logic that handles all path formats including
    /// absolute/relative paths, properties, and subnames.
    ///
    /// # Parameters
    /// - `path`: The path string to parse
    fn parse_path(&mut self, path: &str) {
        if path.is_empty() {
            return;
        }

        let mut working_path = path;

        // Check if absolute path
        if working_path.starts_with('/') {
            self.absolute = true;
            working_path = &working_path[1..]; // Remove leading "/"
        }

        // Extract property and subname (property:subname% or property% or :subname%)
        // Handle the case where both property and subname exist
        if let Some(colon_pos) = working_path.find(':') {
            let after_colon = &working_path[colon_pos + 1..];

            // Check if there's a subname after the property
            if let Some(percent_pos) = after_colon.find('%') {
                let property_part = &after_colon[..percent_pos];
                let subname_part = &after_colon[percent_pos + 1..];

                if !property_part.is_empty() {
                    self.property = Some(StringName::from(property_part));
                }
                if !subname_part.is_empty() {
                    self.subname = Some(StringName::from(subname_part));
                }
            } else {
                // Only property, no subname
                let property_part = after_colon;
                if !property_part.is_empty() {
                    self.property = Some(StringName::from(property_part));
                }
            }

            working_path = &working_path[..colon_pos];
        } else if let Some(percent_pos) = working_path.find('%') {
            // Only subname, no property
            let before_percent = &working_path[..percent_pos];
            let subname_part = &working_path[percent_pos + 1..];

            if !subname_part.is_empty() {
                self.subname = Some(StringName::from(subname_part));
            }
            working_path = before_percent;
        }

        // Parse node names (split by "/")
        if !working_path.is_empty() {
            for name in working_path.split('/') {
                if !name.is_empty() {
                    self.names.push(StringName::from(name));
                }
            }
        }
    }

    /// ### Checks if the NodePath is empty.
    ///
    /// # Returns
    /// `true` if the path has no components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let empty = NodePath::new();
    /// let path = NodePath::from("Player");
    /// assert!(empty.is_empty());
    /// assert!(!path.is_empty());
    /// ```
    #[inline]
    pub fn is_empty(&self) -> bool {
        self.names.is_empty() && self.property.is_none() && self.subname.is_none()
    }

    /// ### Checks if this is an absolute path.
    ///
    /// Absolute paths start with "/" and are resolved from the scene root.
    ///
    /// # Returns
    /// `true` if the path is absolute.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let absolute = NodePath::from("/root/Player");
    /// let relative = NodePath::from("Child");
    /// assert!(absolute.is_absolute());
    /// assert!(!relative.is_absolute());
    /// ```
    #[inline]
    pub fn is_absolute(&self) -> bool {
        self.absolute
    }

    /// ### Checks if this path includes a property.
    ///
    /// Property paths use ":" syntax to access node properties.
    ///
    /// # Returns
    /// `true` if the path includes a property.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let property_path = NodePath::from("Player:position");
    /// let node_path = NodePath::from("Player");
    /// assert!(property_path.has_property());
    /// assert!(!node_path.has_property());
    /// ```
    #[inline]
    pub fn has_property(&self) -> bool {
        self.property.is_some()
    }

    /// ### Checks if this path includes a subname.
    ///
    /// Subname paths use "%" syntax to access resource properties.
    ///
    /// # Returns
    /// `true` if the path includes a subname.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let subname_path = NodePath::from("Sprite%texture");
    /// let node_path = NodePath::from("Sprite");
    /// assert!(subname_path.has_subname());
    /// assert!(!node_path.has_subname());
    /// ```
    #[inline]
    pub fn has_subname(&self) -> bool {
        self.subname.is_some()
    }

    /// ### Returns the number of node names in the path.
    ///
    /// # Returns
    /// The count of node names (excluding property and subname).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let path = NodePath::from("/root/Player/Weapon:damage");
    /// assert_eq!(path.get_name_count(), 3);
    /// ```
    #[inline]
    pub fn get_name_count(&self) -> usize {
        self.names.len()
    }

    /// ### Returns the node name at the specified index.
    ///
    /// # Parameters
    /// - `index`: The index of the name to retrieve
    ///
    /// # Returns
    /// The node name as a string slice, or empty string if index is invalid.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let path = NodePath::from("/root/Player/Weapon");
    /// assert_eq!(path.get_name(0), "root");
    /// assert_eq!(path.get_name(1), "Player");
    /// assert_eq!(path.get_name(2), "Weapon");
    /// assert_eq!(path.get_name(3), ""); // Invalid index
    /// ```
    #[inline]
    pub fn get_name(&self, index: usize) -> &str {
        self.names.get(index)
            .map(|name| name.as_str())
            .unwrap_or("")
    }

    /// ### Returns the property name if this is a property path.
    ///
    /// # Returns
    /// The property name as a string slice, or None if no property.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let path = NodePath::from("Player:position");
    /// assert_eq!(path.get_property(), Some("position"));
    ///
    /// let no_property = NodePath::from("Player");
    /// assert_eq!(no_property.get_property(), None);
    /// ```
    #[inline]
    pub fn get_property(&self) -> Option<&str> {
        self.property.as_ref().map(|prop| prop.as_str())
    }

    /// ### Returns the subname if this is a subname path.
    ///
    /// # Returns
    /// The subname as a string slice, or None if no subname.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let path = NodePath::from("Sprite%texture");
    /// assert_eq!(path.get_subname(), Some("texture"));
    ///
    /// let no_subname = NodePath::from("Sprite");
    /// assert_eq!(no_subname.get_subname(), None);
    /// ```
    #[inline]
    pub fn get_subname(&self) -> Option<&str> {
        self.subname.as_ref().map(|sub| sub.as_str())
    }

    /// ### Returns the original path string.
    ///
    /// # Returns
    /// A reference to the original path string.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let path = NodePath::from("/root/Player:position");
    /// assert_eq!(path.as_str(), "/root/Player:position");
    /// ```
    #[inline]
    pub fn as_str(&self) -> &str {
        self.path.as_str()
    }

    /// ### Creates a new NodePath by appending a name to this path.
    ///
    /// # Parameters
    /// - `name`: The name to append to the path
    ///
    /// # Returns
    /// A new NodePath with the appended name.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let base = NodePath::from("/root/Player");
    /// let extended = base.get_child("Weapon");
    /// assert_eq!(extended.as_str(), "/root/Player/Weapon");
    /// ```
    #[inline]
    pub fn get_child(&self, name: &str) -> NodePath {
        if self.is_empty() {
            return NodePath::from(name);
        }

        let mut new_path = GodotString::new();

        // Add absolute prefix if needed
        if self.absolute {
            new_path.push('/');
        }

        // Add existing names
        for (i, node_name) in self.names.iter().enumerate() {
            if i > 0 {
                new_path.push('/');
            }
            new_path.push_str(node_name.as_str());
        }

        // Add separator before new name if we have existing names
        if !self.names.is_empty() {
            new_path.push('/');
        }
        new_path.push_str(name);

        // Add property if exists
        if let Some(ref prop) = self.property {
            new_path.push(':');
            new_path.push_str(prop.as_str());
        }

        // Add subname if exists
        if let Some(ref sub) = self.subname {
            new_path.push('%');
            new_path.push_str(sub.as_str());
        }

        NodePath::from(new_path.as_str())
    }

    /// ### Creates a new NodePath pointing to the parent of this path.
    ///
    /// # Returns
    /// A new NodePath pointing to the parent, or empty if no parent.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let path = NodePath::from("/root/Player/Weapon");
    /// let parent = path.get_parent();
    /// assert_eq!(parent.as_str(), "/root/Player");
    ///
    /// let root = NodePath::from("/root");
    /// let no_parent = root.get_parent();
    /// assert_eq!(no_parent.as_str(), "/");
    /// ```
    #[inline]
    pub fn get_parent(&self) -> NodePath {
        if self.names.is_empty() {
            return NodePath::new();
        }

        if self.names.len() == 1 {
            return if self.absolute {
                NodePath::from("/")
            } else {
                NodePath::new()
            };
        }

        let mut new_path = GodotString::new();

        // Add absolute prefix if needed
        if self.absolute {
            new_path.push('/');
        }

        // Add all names except the last one
        for (i, node_name) in self.names.iter().take(self.names.len() - 1).enumerate() {
            if i > 0 {
                new_path.push('/');
            }
            new_path.push_str(node_name.as_str());
        }

        NodePath::from(new_path.as_str())
    }

    /// ### Returns the last name in the path (the leaf node).
    ///
    /// # Returns
    /// The last node name, or empty string if path is empty.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let path = NodePath::from("/root/Player/Weapon");
    /// assert_eq!(path.get_final_name(), "Weapon");
    ///
    /// let empty = NodePath::new();
    /// assert_eq!(empty.get_final_name(), "");
    /// ```
    #[inline]
    pub fn get_final_name(&self) -> &str {
        self.names.last()
            .map(|name| name.as_str())
            .unwrap_or("")
    }

    /// ### Creates a NodePath with only the property component.
    ///
    /// # Parameters
    /// - `property`: The property name
    ///
    /// # Returns
    /// A new NodePath representing just the property.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let prop_path = NodePath::property("position");
    /// assert_eq!(prop_path.as_str(), ":position");
    /// assert!(prop_path.has_property());
    /// ```
    #[inline]
    pub fn property(property: &str) -> NodePath {
        NodePath::from(&format!(":{}", property))
    }

    /// ### Creates a NodePath with only the subname component.
    ///
    /// # Parameters
    /// - `subname`: The subname
    ///
    /// # Returns
    /// A new NodePath representing just the subname.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let sub_path = NodePath::subname("texture");
    /// assert_eq!(sub_path.as_str(), "%texture");
    /// assert!(sub_path.has_subname());
    /// ```
    #[inline]
    pub fn subname(subname: &str) -> NodePath {
        NodePath::from(&format!("%{}", subname))
    }

    /// ### Checks if this path is a valid node path.
    ///
    /// Validates the path syntax and component structure.
    ///
    /// # Returns
    /// `true` if the path is valid.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let valid = NodePath::from("/root/Player");
    /// let also_valid = NodePath::from("../Sibling:health");
    /// assert!(valid.is_valid());
    /// assert!(also_valid.is_valid());
    /// ```
    #[inline]
    pub fn is_valid(&self) -> bool {
        // Empty paths are valid
        if self.is_empty() {
            return true;
        }

        // Check for invalid characters in names
        for name in &self.names {
            let name_str = name.as_str();
            if name_str.is_empty() || name_str.contains(':') || name_str.contains('%') {
                return false;
            }
        }

        // Property and subname should not be empty if they exist
        if let Some(ref prop) = self.property {
            if prop.as_str().is_empty() {
                return false;
            }
        }

        if let Some(ref sub) = self.subname {
            if sub.as_str().is_empty() {
                return false;
            }
        }

        true
    }

    /// ### Converts the NodePath to a GodotString.
    ///
    /// # Returns
    /// A GodotString representation of the path.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::NodePath;
    /// let path = NodePath::from("/root/Player");
    /// let string = path.to_string();
    /// assert_eq!(string.as_str(), "/root/Player");
    /// ```
    #[inline]
    pub fn to_string(&self) -> GodotString {
        self.path.clone()
    }
}

impl Default for NodePath {
    /// Returns an empty NodePath.
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for NodePath {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.path)
    }
}

impl Hash for NodePath {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.path.hash(state);
    }
}

impl From<&str> for NodePath {
    #[inline]
    fn from(s: &str) -> Self {
        Self::from(s)
    }
}

impl From<GodotString> for NodePath {
    #[inline]
    fn from(s: GodotString) -> Self {
        Self::from(s.as_str())
    }
}

impl From<NodePath> for GodotString {
    #[inline]
    fn from(path: NodePath) -> Self {
        path.path
    }
}

impl AsRef<str> for NodePath {
    #[inline]
    fn as_ref(&self) -> &str {
        self.path.as_str()
    }
}

impl PartialEq<str> for NodePath {
    #[inline]
    fn eq(&self, other: &str) -> bool {
        self.path.as_str() == other
    }
}

impl PartialEq<&str> for NodePath {
    #[inline]
    fn eq(&self, other: &&str) -> bool {
        self.path.as_str() == *other
    }
}

impl PartialEq<GodotString> for NodePath {
    #[inline]
    fn eq(&self, other: &GodotString) -> bool {
        self.path == *other
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_node_path_creation() {
        let empty = NodePath::new();
        assert!(empty.is_empty());
        assert!(!empty.is_absolute());
        assert!(!empty.has_property());
        assert!(!empty.has_subname());

        let default = NodePath::default();
        assert!(default.is_empty());

        let from_str = NodePath::from("/root/Player");
        assert!(!from_str.is_empty());
        assert!(from_str.is_absolute());
        assert_eq!(from_str.get_name_count(), 2);
    }

    #[test]
    fn test_node_path_absolute_paths() {
        let absolute = NodePath::from("/root/Player/Weapon");
        assert!(absolute.is_absolute());
        assert_eq!(absolute.get_name_count(), 3);
        assert_eq!(absolute.get_name(0), "root");
        assert_eq!(absolute.get_name(1), "Player");
        assert_eq!(absolute.get_name(2), "Weapon");
        assert_eq!(absolute.get_final_name(), "Weapon");

        let root_only = NodePath::from("/");
        assert!(root_only.is_absolute());
        assert_eq!(root_only.get_name_count(), 0);
        assert!(root_only.is_empty());
    }

    #[test]
    fn test_node_path_relative_paths() {
        let relative = NodePath::from("Child/Weapon");
        assert!(!relative.is_absolute());
        assert_eq!(relative.get_name_count(), 2);
        assert_eq!(relative.get_name(0), "Child");
        assert_eq!(relative.get_name(1), "Weapon");

        let single = NodePath::from("Player");
        assert!(!single.is_absolute());
        assert_eq!(single.get_name_count(), 1);
        assert_eq!(single.get_name(0), "Player");
    }

    #[test]
    fn test_node_path_property_paths() {
        let property_path = NodePath::from("Player:position");
        assert!(!property_path.is_absolute());
        assert!(property_path.has_property());
        assert!(!property_path.has_subname());
        assert_eq!(property_path.get_property(), Some("position"));
        assert_eq!(property_path.get_name_count(), 1);
        assert_eq!(property_path.get_name(0), "Player");

        let absolute_property = NodePath::from("/root/Player:health");
        assert!(absolute_property.is_absolute());
        assert!(absolute_property.has_property());
        assert_eq!(absolute_property.get_property(), Some("health"));

        let property_only = NodePath::property("position");
        assert_eq!(property_only.as_str(), ":position");
        assert!(property_only.has_property());
        assert_eq!(property_only.get_property(), Some("position"));
        assert_eq!(property_only.get_name_count(), 0);
    }

    #[test]
    fn test_node_path_subname_paths() {
        let subname_path = NodePath::from("Sprite%texture");
        assert!(!subname_path.is_absolute());
        assert!(!subname_path.has_property());
        assert!(subname_path.has_subname());
        assert_eq!(subname_path.get_subname(), Some("texture"));
        assert_eq!(subname_path.get_name_count(), 1);
        assert_eq!(subname_path.get_name(0), "Sprite");

        let subname_only = NodePath::subname("texture");
        assert_eq!(subname_only.as_str(), "%texture");
        assert!(subname_only.has_subname());
        assert_eq!(subname_only.get_subname(), Some("texture"));
        assert_eq!(subname_only.get_name_count(), 0);
    }

    #[test]
    fn test_node_path_complex_paths() {
        let complex = NodePath::from("/root/Player/Weapon:damage%modifier");
        assert!(complex.is_absolute());
        assert!(complex.has_property());
        assert!(complex.has_subname());
        assert_eq!(complex.get_name_count(), 3);
        assert_eq!(complex.get_name(1), "Player");
        assert_eq!(complex.get_property(), Some("damage"));
        assert_eq!(complex.get_subname(), Some("modifier"));

        let property_and_subname = NodePath::from("Node:prop%sub");
        assert!(property_and_subname.has_property());
        assert!(property_and_subname.has_subname());
        assert_eq!(property_and_subname.get_property(), Some("prop"));
        assert_eq!(property_and_subname.get_subname(), Some("sub"));
    }

    #[test]
    fn test_node_path_parent_operations() {
        let path = NodePath::from("/root/Player/Weapon");
        let parent = path.get_parent();
        assert_eq!(parent.as_str(), "/root/Player");
        assert!(parent.is_absolute());

        let grandparent = parent.get_parent();
        assert_eq!(grandparent.as_str(), "/root");

        let root_parent = grandparent.get_parent();
        assert_eq!(root_parent.as_str(), "/");

        let relative = NodePath::from("Child/Weapon");
        let rel_parent = relative.get_parent();
        assert_eq!(rel_parent.as_str(), "Child");
        assert!(!rel_parent.is_absolute());

        let single_parent = NodePath::from("Single").get_parent();
        assert!(single_parent.is_empty());
    }

    #[test]
    fn test_node_path_child_operations() {
        let base = NodePath::from("/root/Player");
        let child = base.get_child("Weapon");
        assert_eq!(child.as_str(), "/root/Player/Weapon");
        assert!(child.is_absolute());

        let relative_base = NodePath::from("Player");
        let relative_child = relative_base.get_child("Weapon");
        assert_eq!(relative_child.as_str(), "Player/Weapon");
        assert!(!relative_child.is_absolute());

        let empty_base = NodePath::new();
        let from_empty = empty_base.get_child("NewNode");
        assert_eq!(from_empty.as_str(), "NewNode");
    }

    #[test]
    fn test_node_path_validation() {
        let valid_paths = vec![
            "/root/Player",
            "Player/Weapon",
            "Node:property",
            "Sprite%texture",
            "/root/Player:position%x",
            "",
            "/",
            "Single",
        ];

        for path_str in valid_paths {
            let path = NodePath::from(path_str);
            assert!(path.is_valid(), "Path should be valid: {}", path_str);
        }

        // Test edge cases that should still be valid
        let edge_cases = vec![
            ":property",  // Property only
            "%subname",   // Subname only
            "Node:",      // Empty property (should be invalid but let's test)
            "Node%",      // Empty subname (should be invalid but let's test)
        ];

        for path_str in edge_cases {
            let path = NodePath::from(path_str);
            // These might be invalid depending on implementation
            let _ = path.is_valid(); // Just test that it doesn't panic
        }
    }

    #[test]
    fn test_node_path_conversions() {
        let path = NodePath::from("/root/Player");

        // To GodotString
        let string = path.to_string();
        assert_eq!(string.as_str(), "/root/Player");

        // From GodotString
        let godot_string = GodotString::from("/root/Enemy");
        let from_godot = NodePath::from(godot_string.as_str());
        assert_eq!(from_godot.as_str(), "/root/Enemy");

        // AsRef<str>
        let as_ref: &str = path.as_ref();
        assert_eq!(as_ref, "/root/Player");

        // Into GodotString
        let into_string: GodotString = path.clone().into();
        assert_eq!(into_string.as_str(), "/root/Player");
    }

    #[test]
    fn test_node_path_equality() {
        let path1 = NodePath::from("/root/Player");
        let path2 = NodePath::from("/root/Player");
        let path3 = NodePath::from("/root/Enemy");

        // NodePath equality
        assert_eq!(path1, path2);
        assert_ne!(path1, path3);

        // String equality
        assert_eq!(path1, "/root/Player");
        assert_ne!(path1, "/root/Enemy");

        // GodotString equality
        let godot_string = GodotString::from("/root/Player");
        assert_eq!(path1, godot_string);
    }

    #[test]
    fn test_node_path_display_and_hash() {
        let path = NodePath::from("/root/Player:position");
        let display_str = format!("{}", path);
        assert_eq!(display_str, "/root/Player:position");

        // Hash consistency
        use std::collections::HashMap;
        let mut map = HashMap::new();
        map.insert(path.clone(), 42);
        assert_eq!(map.get(&path), Some(&42));

        // Same content should hash the same
        let path_copy = NodePath::from("/root/Player:position");
        assert_eq!(map.get(&path_copy), Some(&42));
    }

    #[test]
    fn test_node_path_edge_cases() {
        // Empty path operations
        let empty = NodePath::new();
        assert_eq!(empty.get_name(0), "");
        assert_eq!(empty.get_final_name(), "");
        assert_eq!(empty.get_property(), None);
        assert_eq!(empty.get_subname(), None);
        assert!(empty.get_parent().is_empty());

        // Invalid indices
        let path = NodePath::from("Single");
        assert_eq!(path.get_name(1), "");
        assert_eq!(path.get_name(100), "");

        // Root path edge cases
        let root = NodePath::from("/");
        assert!(root.is_absolute());
        assert!(root.is_empty()); // Root is considered empty in terms of names
        assert_eq!(root.get_name_count(), 0);

        // Multiple slashes (should be handled gracefully)
        let multi_slash = NodePath::from("//root///Player//");
        assert!(multi_slash.is_absolute());
        // Should parse as ["root", "Player"] ignoring empty components
        assert_eq!(multi_slash.get_name_count(), 2);
        assert_eq!(multi_slash.get_name(0), "root");
        assert_eq!(multi_slash.get_name(1), "Player");
    }

    #[test]
    fn test_node_path_special_characters() {
        // Test paths with special node names
        let special = NodePath::from("/root/Player-1/Weapon_2");
        assert_eq!(special.get_name_count(), 3);
        assert_eq!(special.get_name(1), "Player-1");
        assert_eq!(special.get_name(2), "Weapon_2");

        // Test property with special characters
        let prop_special = NodePath::from("Node:property_name");
        assert_eq!(prop_special.get_property(), Some("property_name"));

        // Test subname with special characters
        let sub_special = NodePath::from("Sprite%texture_2d");
        assert_eq!(sub_special.get_subname(), Some("texture_2d"));
    }

    #[test]
    fn test_node_path_relative_navigation() {
        // Test relative path components (though not fully implemented in parsing)
        let relative = NodePath::from("../Sibling");
        assert!(!relative.is_absolute());
        assert_eq!(relative.get_name_count(), 2);
        assert_eq!(relative.get_name(0), "..");
        assert_eq!(relative.get_name(1), "Sibling");

        let current_dir = NodePath::from("./Child");
        assert_eq!(current_dir.get_name_count(), 2);
        assert_eq!(current_dir.get_name(0), ".");
        assert_eq!(current_dir.get_name(1), "Child");
    }

    #[test]
    fn test_node_path_property_and_subname_combinations() {
        // Property without node
        let prop_only = NodePath::from(":position");
        assert!(prop_only.has_property());
        assert_eq!(prop_only.get_property(), Some("position"));
        assert_eq!(prop_only.get_name_count(), 0);

        // Subname without node
        let sub_only = NodePath::from("%texture");
        assert!(sub_only.has_subname());
        assert_eq!(sub_only.get_subname(), Some("texture"));
        assert_eq!(sub_only.get_name_count(), 0);

        // Both property and subname without node
        let both_only = NodePath::from(":prop%sub");
        assert!(both_only.has_property());
        assert!(both_only.has_subname());
        assert_eq!(both_only.get_property(), Some("prop"));
        assert_eq!(both_only.get_subname(), Some("sub"));
        assert_eq!(both_only.get_name_count(), 0);
    }

    #[test]
    fn test_node_path_clone() {
        let original = NodePath::from("/root/Player:position%x");
        let cloned = original.clone();

        assert_eq!(original, cloned);
        assert_eq!(original.as_str(), cloned.as_str());
        assert_eq!(original.is_absolute(), cloned.is_absolute());
        assert_eq!(original.has_property(), cloned.has_property());
        assert_eq!(original.has_subname(), cloned.has_subname());
    }

    #[test]
    fn test_node_path_child_with_properties() {
        // Test that get_child preserves properties and subnames
        let base = NodePath::from("Player:health");
        let child = base.get_child("Weapon");
        assert_eq!(child.as_str(), "Player/Weapon:health");
        assert!(child.has_property());
        assert_eq!(child.get_property(), Some("health"));

        let base_with_sub = NodePath::from("Sprite%texture");
        let child_with_sub = base_with_sub.get_child("Child");
        assert_eq!(child_with_sub.as_str(), "Sprite/Child%texture");
        assert!(child_with_sub.has_subname());
        assert_eq!(child_with_sub.get_subname(), Some("texture"));
    }

    #[test]
    fn test_node_path_factory_methods() {
        // Test property factory method
        let prop_path = NodePath::property("position");
        assert_eq!(prop_path.as_str(), ":position");
        assert!(prop_path.has_property());
        assert_eq!(prop_path.get_property(), Some("position"));

        // Test subname factory method
        let sub_path = NodePath::subname("texture");
        assert_eq!(sub_path.as_str(), "%texture");
        assert!(sub_path.has_subname());
        assert_eq!(sub_path.get_subname(), Some("texture"));
    }
}
