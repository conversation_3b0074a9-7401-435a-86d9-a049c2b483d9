//! Comprehensive Node implementation for Godot-compatible scene tree management.
//!
//! This module provides the base Node class that forms the foundation of the scene tree
//! system in Verturion. It maintains full compatibility with <PERSON><PERSON>'s Node class while
//! providing efficient parent/child relationship management, unique identification,
//! and lifecycle management for game development.

use std::fmt;
use std::collections::HashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::rc::{Rc, Weak};
use std::cell::RefCell;
use crate::core::variant::String as GodotString;
use super::NodePath;

/// Global node ID counter for unique identification
static NEXT_NODE_ID: AtomicU64 = AtomicU64::new(1);

/// ### Unique identifier for nodes in the scene tree.
///
/// NodeId provides a unique, stable identifier for each node that persists
/// throughout the node's lifetime and can be used for efficient lookups
/// and reference management.
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>h)]
pub struct NodeId(u64);

impl NodeId {
    /// ### Generates a new unique NodeId.
    ///
    /// # Returns
    /// A new unique NodeId that has never been used before.
    #[inline]
    pub fn new() -> Self {
        Self(NEXT_NODE_ID.fetch_add(1, Ordering::Relaxed))
    }

    /// ### Gets the raw ID value.
    ///
    /// # Returns
    /// The underlying u64 identifier.
    #[inline]
    pub fn value(&self) -> u64 {
        self.0
    }
}

impl fmt::Display for NodeId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "NodeId({})", self.0)
    }
}

/// ### Node lifecycle state for managing scene tree operations.
///
/// Tracks the current state of a node in the scene tree lifecycle,
/// enabling proper callback execution and state management.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum NodeState {
    /// Node has been created but not added to scene tree
    Created,
    /// Node is in the scene tree and ready for processing
    InTree,
    /// Node is queued for deletion
    QueuedForDeletion,
    /// Node has been removed from scene tree
    Removed,
}

/// ### Internal node data structure for managing node state.
///
/// Contains all the mutable state for a node, wrapped in RefCell
/// for interior mutability while maintaining Rust's safety guarantees.
#[derive(Debug)]
struct NodeData {
    /// Unique identifier for this node
    id: NodeId,
    /// Node name (must be unique among siblings)
    name: GodotString,
    /// Current lifecycle state
    state: NodeState,
    /// Weak reference to parent node
    parent: Option<Weak<RefCell<NodeData>>>,
    /// Strong references to child nodes
    children: Vec<Rc<RefCell<NodeData>>>,
    /// Child name lookup for fast access
    child_names: HashMap<String, usize>,
    /// Whether this node is queued for deletion
    queued_for_deletion: bool,
}

/// ### Base Node class for Godot-compatible scene tree management.
///
/// Node is the fundamental building block of the scene tree system, providing
/// parent/child relationship management, unique identification, lifecycle callbacks,
/// and scene tree navigation. It maintains full compatibility with Godot's Node
/// class while ensuring memory safety and efficient operations.
///
/// ## Core Features
///
/// - **Unique Identification**: Each node has a unique NodeId for stable references
/// - **Parent/Child Management**: Safe parent/child relationships with automatic cleanup
/// - **Name Resolution**: Unique naming among siblings with automatic conflict resolution
/// - **Lifecycle Management**: Proper ready, enter_tree, exit_tree callback support
/// - **Scene Tree Navigation**: Full NodePath-based navigation and node finding
/// - **Memory Safety**: Reference-counted with weak parent references to prevent cycles
///
/// ## Node Hierarchy
///
/// Nodes form a tree structure where each node can have one parent and multiple children.
/// The scene tree provides a hierarchical organization for game objects, UI elements,
/// and other scene components.
///
/// # Examples
/// ```
/// # use verturion::core::scene::Node;
/// // Create nodes
/// let mut root = Node::new("Root");
/// let mut player = Node::new("Player");
/// let mut weapon = Node::new("Weapon");
///
/// // Build hierarchy
/// player.add_child(weapon);
/// root.add_child(player);
///
/// // Navigate tree
/// assert_eq!(root.get_child_count(), 1);
/// assert!(root.has_node("Player"));
/// assert!(root.has_node("Player/Weapon"));
/// ```
#[derive(Debug)]
pub struct Node {
    /// Internal node data wrapped for interior mutability
    data: Rc<RefCell<NodeData>>,
}

impl Node {
    /// ### Creates a new Node with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this node (will be made unique among siblings)
    ///
    /// # Returns
    /// A new Node instance ready to be added to the scene tree.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let node = Node::new("Player");
    /// assert_eq!(node.get_name(), "Player");
    /// assert!(!node.is_in_tree());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            data: Rc::new(RefCell::new(NodeData {
                id: NodeId::new(),
                name: GodotString::from(name),
                state: NodeState::Created,
                parent: None,
                children: Vec::new(),
                child_names: HashMap::new(),
                queued_for_deletion: false,
            })),
        }
    }

    /// ### Gets the unique identifier for this node.
    ///
    /// # Returns
    /// The NodeId that uniquely identifies this node.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let node1 = Node::new("Node1");
    /// let node2 = Node::new("Node2");
    /// assert_ne!(node1.get_id(), node2.get_id());
    /// ```
    #[inline]
    pub fn get_id(&self) -> NodeId {
        self.data.borrow().id
    }

    /// ### Gets the name of this node.
    ///
    /// # Returns
    /// The current name of this node.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let node = Node::new("Player");
    /// assert_eq!(node.get_name(), "Player");
    /// ```
    #[inline]
    pub fn get_name(&self) -> String {
        self.data.borrow().name.as_str().to_string()
    }

    /// ### Sets the name of this node.
    ///
    /// If this node has a parent, the name will be made unique among siblings
    /// by appending a number if necessary.
    ///
    /// # Parameters
    /// - `name`: The new name for this node
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut node = Node::new("OldName");
    /// node.set_name("NewName");
    /// assert_eq!(node.get_name(), "NewName");
    /// ```
    #[inline]
    pub fn set_name(&mut self, name: &str) {
        let mut data = self.data.borrow_mut();
        data.name = GodotString::from(name);

        // If we have a parent, ensure name uniqueness
        if let Some(parent_weak) = &data.parent {
            if let Some(parent_rc) = parent_weak.upgrade() {
                drop(data); // Release borrow before calling ensure_unique_name
                self.ensure_unique_name_with_parent(&parent_rc);
            }
        }
    }

    /// ### Checks if this node is currently in the scene tree.
    ///
    /// # Returns
    /// True if the node is in the scene tree, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut root = Node::new("Root");
    /// let child = Node::new("Child");
    ///
    /// assert!(!child.is_in_tree());
    /// root.add_child(child.clone());
    /// assert!(child.is_in_tree());
    /// ```
    #[inline]
    pub fn is_in_tree(&self) -> bool {
        matches!(self.data.borrow().state, NodeState::InTree)
    }

    /// ### Checks if this node is queued for deletion.
    ///
    /// # Returns
    /// True if the node is queued for deletion, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut node = Node::new("Node");
    /// assert!(!node.is_queued_for_deletion());
    ///
    /// node.queue_free();
    /// assert!(node.is_queued_for_deletion());
    /// ```
    #[inline]
    pub fn is_queued_for_deletion(&self) -> bool {
        self.data.borrow().queued_for_deletion
    }

    /// ### Adds a child node to this node.
    ///
    /// The child will be added to the end of the children list and will
    /// have its name made unique among siblings if necessary.
    ///
    /// # Parameters
    /// - `child`: The node to add as a child
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut parent = Node::new("Parent");
    /// let child = Node::new("Child");
    ///
    /// parent.add_child(child.clone());
    /// assert_eq!(parent.get_child_count(), 1);
    /// assert!(child.is_in_tree());
    /// ```
    #[inline]
    pub fn add_child(&mut self, child: Node) {
        self.add_child_at_index(child, None);
    }

    /// ### Adds a child node at a specific index.
    ///
    /// # Parameters
    /// - `child`: The node to add as a child
    /// - `index`: Optional index to insert at (None = append to end)
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut parent = Node::new("Parent");
    /// let child1 = Node::new("Child1");
    /// let child2 = Node::new("Child2");
    ///
    /// parent.add_child_at_index(child1, None);
    /// parent.add_child_at_index(child2, Some(0));
    /// assert_eq!(parent.get_child(0).unwrap().get_name(), "Child2");
    /// ```
    #[inline]
    pub fn add_child_at_index(&mut self, child: Node, index: Option<usize>) {
        // Ensure child name is unique
        child.ensure_unique_name_with_parent(&self.data);

        // Set up parent-child relationship
        {
            let mut child_data = child.data.borrow_mut();
            child_data.parent = Some(Rc::downgrade(&self.data));
            child_data.state = NodeState::InTree;
        }

        // Ensure parent is also in tree
        {
            let mut self_data = self.data.borrow_mut();
            if self_data.state == NodeState::Created {
                self_data.state = NodeState::InTree;
            }
        }

        // Add to children list
        let mut data = self.data.borrow_mut();
        let insert_index = index.unwrap_or(data.children.len());
        let insert_index = insert_index.min(data.children.len());

        data.children.insert(insert_index, child.data.clone());

        // Rebuild child name lookup
        data.child_names.clear();
        let child_data: Vec<(String, usize)> = data.children.iter().enumerate()
            .map(|(i, child_rc)| (child_rc.borrow().name.as_str().to_string(), i))
            .collect();

        for (name, i) in child_data {
            data.child_names.insert(name, i);
        }
    }

    /// ### Removes a child node from this node.
    ///
    /// # Parameters
    /// - `child`: The child node to remove
    ///
    /// # Returns
    /// True if the child was found and removed, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut parent = Node::new("Parent");
    /// let child = Node::new("Child");
    ///
    /// parent.add_child(child.clone());
    /// assert_eq!(parent.get_child_count(), 1);
    ///
    /// assert!(parent.remove_child(&child));
    /// assert_eq!(parent.get_child_count(), 0);
    /// assert!(!child.is_in_tree());
    /// ```
    #[inline]
    pub fn remove_child(&mut self, child: &Node) -> bool {
        let child_id = child.get_id();
        let mut data = self.data.borrow_mut();

        if let Some(index) = data.children.iter().position(|c| c.borrow().id == child_id) {
            // Remove from children list
            let removed_child = data.children.remove(index);

            // Update child state
            {
                let mut child_data = removed_child.borrow_mut();
                child_data.parent = None;
                child_data.state = NodeState::Removed;
            }

            // Rebuild child name lookup
            data.child_names.clear();
            let child_data: Vec<(String, usize)> = data.children.iter().enumerate()
                .map(|(i, child_rc)| (child_rc.borrow().name.as_str().to_string(), i))
                .collect();

            for (name, i) in child_data {
                data.child_names.insert(name, i);
            }

            true
        } else {
            false
        }
    }

    /// ### Gets the number of child nodes.
    ///
    /// # Returns
    /// The number of direct children of this node.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut parent = Node::new("Parent");
    /// assert_eq!(parent.get_child_count(), 0);
    ///
    /// parent.add_child(Node::new("Child1"));
    /// parent.add_child(Node::new("Child2"));
    /// assert_eq!(parent.get_child_count(), 2);
    /// ```
    #[inline]
    pub fn get_child_count(&self) -> usize {
        self.data.borrow().children.len()
    }

    /// ### Gets a child node by index.
    ///
    /// # Parameters
    /// - `index`: The index of the child to retrieve
    ///
    /// # Returns
    /// Some(Node) if the index is valid, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut parent = Node::new("Parent");
    /// let child = Node::new("Child");
    ///
    /// parent.add_child(child.clone());
    /// assert_eq!(parent.get_child(0).unwrap().get_name(), "Child");
    /// assert!(parent.get_child(1).is_none());
    /// ```
    #[inline]
    pub fn get_child(&self, index: usize) -> Option<Node> {
        let data = self.data.borrow();
        data.children.get(index).map(|child_rc| Node {
            data: child_rc.clone(),
        })
    }

    /// ### Gets all child nodes.
    ///
    /// # Returns
    /// A vector containing all direct children of this node.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut parent = Node::new("Parent");
    /// parent.add_child(Node::new("Child1"));
    /// parent.add_child(Node::new("Child2"));
    ///
    /// let children = parent.get_children();
    /// assert_eq!(children.len(), 2);
    /// ```
    #[inline]
    pub fn get_children(&self) -> Vec<Node> {
        let data = self.data.borrow();
        data.children.iter().map(|child_rc| Node {
            data: child_rc.clone(),
        }).collect()
    }

    /// ### Gets the parent node.
    ///
    /// # Returns
    /// Some(Node) if this node has a parent, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut parent = Node::new("Parent");
    /// let child = Node::new("Child");
    ///
    /// assert!(child.get_parent().is_none());
    /// parent.add_child(child.clone());
    /// assert_eq!(child.get_parent().unwrap().get_name(), "Parent");
    /// ```
    #[inline]
    pub fn get_parent(&self) -> Option<Node> {
        let data = self.data.borrow();
        data.parent.as_ref()?.upgrade().map(|parent_rc| Node {
            data: parent_rc,
        })
    }

    /// ### Finds a child node by name.
    ///
    /// # Parameters
    /// - `name`: The name of the child to find
    ///
    /// # Returns
    /// Some(Node) if a child with the given name exists, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut parent = Node::new("Parent");
    /// let child = Node::new("Child");
    ///
    /// parent.add_child(child.clone());
    /// assert!(parent.find_child("Child").is_some());
    /// assert!(parent.find_child("NonExistent").is_none());
    /// ```
    #[inline]
    pub fn find_child(&self, name: &str) -> Option<Node> {
        let data = self.data.borrow();
        if let Some(&index) = data.child_names.get(name) {
            data.children.get(index).map(|child_rc| Node {
                data: child_rc.clone(),
            })
        } else {
            None
        }
    }

    /// ### Checks if a node exists at the given path.
    ///
    /// # Parameters
    /// - `path`: The NodePath to check
    ///
    /// # Returns
    /// True if a node exists at the path, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut root = Node::new("Root");
    /// let mut player = Node::new("Player");
    /// let weapon = Node::new("Weapon");
    ///
    /// player.add_child(weapon);
    /// root.add_child(player);
    ///
    /// assert!(root.has_node("Player"));
    /// assert!(root.has_node("Player/Weapon"));
    /// assert!(!root.has_node("NonExistent"));
    /// ```
    #[inline]
    pub fn has_node(&self, path: &str) -> bool {
        self.get_node(path).is_some()
    }

    /// ### Gets a node at the given path.
    ///
    /// # Parameters
    /// - `path`: The NodePath to the target node
    ///
    /// # Returns
    /// Some(Node) if a node exists at the path, None otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut root = Node::new("Root");
    /// let mut player = Node::new("Player");
    /// let weapon = Node::new("Weapon");
    ///
    /// player.add_child(weapon.clone());
    /// root.add_child(player);
    ///
    /// let found = root.get_node("Player/Weapon").unwrap();
    /// assert_eq!(found.get_name(), "Weapon");
    /// ```
    #[inline]
    pub fn get_node(&self, path: &str) -> Option<Node> {
        let node_path = NodePath::from(path);
        self.get_node_from_path(&node_path)
    }

    /// ### Gets a node using a NodePath.
    ///
    /// # Parameters
    /// - `path`: The NodePath to follow
    ///
    /// # Returns
    /// Some(Node) if a node exists at the path, None otherwise.
    #[inline]
    pub fn get_node_from_path(&self, path: &NodePath) -> Option<Node> {
        if path.is_empty() {
            return Some(Node { data: self.data.clone() });
        }

        let mut current = Node { data: self.data.clone() };

        for i in 0..path.get_name_count() {
            let name = path.get_name(i);
            if name == ".." {
                // Go to parent
                current = current.get_parent()?;
            } else if name == "." {
                // Stay at current node
                continue;
            } else {
                // Find child with this name
                current = current.find_child(&name)?;
            }
        }

        Some(current)
    }

    /// ### Queues this node for deletion.
    ///
    /// The node will be marked for deletion and removed from the scene tree
    /// during the next cleanup phase.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut node = Node::new("Node");
    /// node.queue_free();
    /// assert!(node.is_queued_for_deletion());
    /// ```
    #[inline]
    pub fn queue_free(&mut self) {
        let mut data = self.data.borrow_mut();
        data.queued_for_deletion = true;
        data.state = NodeState::QueuedForDeletion;
    }

    /// ### Creates a duplicate of this node.
    ///
    /// # Returns
    /// A new Node that is a copy of this node (without parent/child relationships).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let original = Node::new("Original");
    /// let duplicate = original.duplicate();
    ///
    /// assert_eq!(duplicate.get_name(), "Original");
    /// assert_ne!(duplicate.get_id(), original.get_id());
    /// ```
    #[inline]
    pub fn duplicate(&self) -> Node {
        let data = self.data.borrow();
        Node::new(&data.name.as_str())
    }

    /// ### Gets the path from the scene root to this node.
    ///
    /// # Returns
    /// A NodePath representing the path from root to this node.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut root = Node::new("Root");
    /// let mut player = Node::new("Player");
    /// let weapon = Node::new("Weapon");
    ///
    /// player.add_child(weapon.clone());
    /// root.add_child(player);
    ///
    /// let path = weapon.get_path();
    /// // Path would be something like "/Root/Player/Weapon"
    /// ```
    #[inline]
    pub fn get_path(&self) -> NodePath {
        let mut path_components = Vec::new();
        let mut current = Some(Node { data: self.data.clone() });

        while let Some(node) = current {
            path_components.push(node.get_name());
            current = node.get_parent();
        }

        path_components.reverse();

        if path_components.is_empty() {
            NodePath::new()
        } else {
            let path_str = format!("/{}", path_components.join("/"));
            NodePath::from(&path_str)
        }
    }

    /// ### Gets the path from this node to another node.
    ///
    /// # Parameters
    /// - `to`: The target node
    ///
    /// # Returns
    /// A NodePath representing the relative path to the target node.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::Node;
    /// let mut root = Node::new("Root");
    /// let player = Node::new("Player");
    /// let weapon = Node::new("Weapon");
    ///
    /// root.add_child(player.clone());
    /// root.add_child(weapon.clone());
    ///
    /// let path = player.get_path_to(&weapon);
    /// // Path would be something like "../Weapon"
    /// ```
    #[inline]
    pub fn get_path_to(&self, to: &Node) -> NodePath {
        let _from_path = self.get_path();
        let to_path = to.get_path();

        // For now, return absolute path to target
        // TODO: Implement proper relative path calculation
        to_path
    }

    /// ### Helper method to ensure unique name among siblings.
    fn ensure_unique_name_with_parent(&self, parent_rc: &Rc<RefCell<NodeData>>) {
        let parent_data = parent_rc.borrow();
        let base_name = self.data.borrow().name.as_str().to_string();

        let mut unique_name = base_name.clone();
        let mut counter = 1;

        while parent_data.child_names.contains_key(&unique_name) {
            unique_name = format!("{}{}", base_name, counter);
            counter += 1;
        }

        if unique_name != base_name {
            drop(parent_data); // Release borrow
            self.data.borrow_mut().name = GodotString::from(unique_name);
        }
    }
}

impl Clone for Node {
    /// ### Creates a clone of this node reference.
    ///
    /// This clones the reference to the same node, not the node itself.
    /// Use duplicate() to create a new node with the same properties.
    fn clone(&self) -> Self {
        Self {
            data: self.data.clone(),
        }
    }
}

impl PartialEq for Node {
    /// ### Compares two nodes for equality based on their unique IDs.
    fn eq(&self, other: &Self) -> bool {
        self.get_id() == other.get_id()
    }
}

impl Eq for Node {}

impl fmt::Display for Node {
    /// ### Formats the node for display showing name and ID.
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Node({}, {})", self.get_name(), self.get_id())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_node_creation() {
        let node = Node::new("TestNode");
        assert_eq!(node.get_name(), "TestNode");
        assert!(!node.is_in_tree());
        assert!(!node.is_queued_for_deletion());
    }

    #[test]
    fn test_node_unique_ids() {
        let node1 = Node::new("Node1");
        let node2 = Node::new("Node2");
        assert_ne!(node1.get_id(), node2.get_id());
    }

    #[test]
    fn test_node_name_setting() {
        let mut node = Node::new("OldName");
        assert_eq!(node.get_name(), "OldName");

        node.set_name("NewName");
        assert_eq!(node.get_name(), "NewName");
    }

    #[test]
    fn test_node_parent_child_relationship() {
        let mut parent = Node::new("Parent");
        let child = Node::new("Child");

        // Initially no relationship
        assert!(child.get_parent().is_none());
        assert_eq!(parent.get_child_count(), 0);

        // Add child
        parent.add_child(child.clone());
        assert_eq!(parent.get_child_count(), 1);
        assert!(child.is_in_tree());

        // Check parent-child relationship
        let retrieved_parent = child.get_parent().unwrap();
        assert_eq!(retrieved_parent.get_name(), "Parent");

        let retrieved_child = parent.get_child(0).unwrap();
        assert_eq!(retrieved_child.get_name(), "Child");
    }

    #[test]
    fn test_node_remove_child() {
        let mut parent = Node::new("Parent");
        let child = Node::new("Child");

        parent.add_child(child.clone());
        assert_eq!(parent.get_child_count(), 1);
        assert!(child.is_in_tree());

        // Remove child
        assert!(parent.remove_child(&child));
        assert_eq!(parent.get_child_count(), 0);
        assert!(!child.is_in_tree());

        // Try to remove again (should fail)
        assert!(!parent.remove_child(&child));
    }

    #[test]
    fn test_node_multiple_children() {
        let mut parent = Node::new("Parent");
        let child1 = Node::new("Child1");
        let child2 = Node::new("Child2");
        let child3 = Node::new("Child3");

        parent.add_child(child1.clone());
        parent.add_child(child2.clone());
        parent.add_child(child3.clone());

        assert_eq!(parent.get_child_count(), 3);

        let children = parent.get_children();
        assert_eq!(children.len(), 3);
        assert_eq!(children[0].get_name(), "Child1");
        assert_eq!(children[1].get_name(), "Child2");
        assert_eq!(children[2].get_name(), "Child3");
    }

    #[test]
    fn test_node_find_child() {
        let mut parent = Node::new("Parent");
        let child1 = Node::new("Child1");
        let child2 = Node::new("Child2");

        parent.add_child(child1.clone());
        parent.add_child(child2.clone());

        // Find existing children
        let found1 = parent.find_child("Child1").unwrap();
        assert_eq!(found1.get_name(), "Child1");

        let found2 = parent.find_child("Child2").unwrap();
        assert_eq!(found2.get_name(), "Child2");

        // Try to find non-existent child
        assert!(parent.find_child("NonExistent").is_none());
    }

    #[test]
    fn test_node_has_node() {
        let mut root = Node::new("Root");
        let mut player = Node::new("Player");
        let weapon = Node::new("Weapon");

        player.add_child(weapon.clone());
        root.add_child(player.clone());

        // Test direct children
        assert!(root.has_node("Player"));
        assert!(player.has_node("Weapon"));

        // Test nested paths
        assert!(root.has_node("Player/Weapon"));

        // Test non-existent paths
        assert!(!root.has_node("NonExistent"));
        assert!(!root.has_node("Player/NonExistent"));
    }

    #[test]
    fn test_node_get_node() {
        let mut root = Node::new("Root");
        let mut player = Node::new("Player");
        let weapon = Node::new("Weapon");

        player.add_child(weapon.clone());
        root.add_child(player.clone());

        // Get direct child
        let found_player = root.get_node("Player").unwrap();
        assert_eq!(found_player.get_name(), "Player");

        // Get nested child
        let found_weapon = root.get_node("Player/Weapon").unwrap();
        assert_eq!(found_weapon.get_name(), "Weapon");

        // Try to get non-existent node
        assert!(root.get_node("NonExistent").is_none());
    }

    #[test]
    fn test_node_queue_free() {
        let mut node = Node::new("TestNode");
        assert!(!node.is_queued_for_deletion());

        node.queue_free();
        assert!(node.is_queued_for_deletion());
    }

    #[test]
    fn test_node_duplicate() {
        let original = Node::new("Original");
        let duplicate = original.duplicate();

        assert_eq!(duplicate.get_name(), "Original");
        assert_ne!(duplicate.get_id(), original.get_id());
        assert!(!duplicate.is_in_tree());
    }

    #[test]
    fn test_node_get_path() {
        let mut root = Node::new("Root");
        let mut player = Node::new("Player");
        let weapon = Node::new("Weapon");

        player.add_child(weapon.clone());
        root.add_child(player.clone());

        let weapon_path = weapon.get_path();
        // The path should contain the hierarchy
        let path_str = weapon_path.as_str();
        assert!(path_str.contains("Root"));
        assert!(path_str.contains("Player"));
        assert!(path_str.contains("Weapon"));
    }

    #[test]
    fn test_node_equality() {
        let node1 = Node::new("Node1");
        let node2 = Node::new("Node2");
        let node1_clone = node1.clone();

        // Same node should be equal
        assert_eq!(node1, node1_clone);

        // Different nodes should not be equal
        assert_ne!(node1, node2);
    }

    #[test]
    fn test_node_display() {
        let node = Node::new("TestNode");
        let display_str = format!("{}", node);
        assert!(display_str.contains("TestNode"));
        assert!(display_str.contains("Node("));
    }

    #[test]
    fn test_node_add_child_at_index() {
        let mut parent = Node::new("Parent");
        let child1 = Node::new("Child1");
        let child2 = Node::new("Child2");
        let child3 = Node::new("Child3");

        // Add children in specific order
        parent.add_child_at_index(child1.clone(), None); // Append
        parent.add_child_at_index(child3.clone(), None); // Append
        parent.add_child_at_index(child2.clone(), Some(1)); // Insert at index 1

        assert_eq!(parent.get_child_count(), 3);
        assert_eq!(parent.get_child(0).unwrap().get_name(), "Child1");
        assert_eq!(parent.get_child(1).unwrap().get_name(), "Child2");
        assert_eq!(parent.get_child(2).unwrap().get_name(), "Child3");
    }
}
