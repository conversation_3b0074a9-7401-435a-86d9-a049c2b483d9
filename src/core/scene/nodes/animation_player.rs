//! AnimationPlayer implementation for keyframe animation and property tweening.
//!
//! This module provides the AnimationPlayer class that extends Node with
//! comprehensive animation functionality including keyframe animation,
//! property tweening, timeline control, animation blending, and signal
//! emission. It maintains full compatibility with Godot's AnimationPlayer
//! class while providing efficient animation processing.

use std::fmt;
use std::collections::HashMap;
use crate::core::scene::Node;
use crate::core::signal::{Signal, SignalManager, SignalData};
use crate::core::variant::Variant;

/// ### Animation playback modes for different animation behaviors.
///
/// Defines how animations should behave when they reach the end.
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum AnimationMode {
    /// Animation plays once and stops
    Forward,
    /// Animation plays backwards once and stops
    Backward,
    /// Animation loops continuously
    Loop,
    /// Animation ping-pongs back and forth
    PingPong,
}

/// ### Animation method call modes for different timing behaviors.
///
/// Defines when method calls in animations should be executed.
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>q, Eq)]
pub enum AnimationMethodCallMode {
    /// Method calls are deferred to idle time
    Deferred,
    /// Method calls are executed immediately
    Immediate,
}

/// ### Animation process modes for different update timing.
///
/// Defines when animations should be processed during the frame.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AnimationProcessMode {
    /// Process during physics updates
    Physics,
    /// Process during idle updates
    Idle,
    /// Process manually
    Manual,
}

/// ### Animation track for storing keyframe data.
///
/// Represents a single animation track that can animate a specific
/// property or trigger method calls over time.
#[derive(Debug, Clone)]
pub struct AnimationTrack {
    /// Path to the property or method being animated
    path: String,
    /// Keyframe times in seconds
    times: Vec<f32>,
    /// Keyframe values
    values: Vec<Variant>,
    /// Whether this track is enabled
    enabled: bool,
}

impl AnimationTrack {
    /// ### Creates a new AnimationTrack.
    ///
    /// # Parameters
    /// - `path`: The property path to animate
    ///
    /// # Returns
    /// A new AnimationTrack instance.
    #[inline]
    pub fn new(path: String) -> Self {
        Self {
            path,
            times: Vec::new(),
            values: Vec::new(),
            enabled: true,
        }
    }

    /// ### Adds a keyframe to the track.
    ///
    /// # Parameters
    /// - `time`: Time in seconds for the keyframe
    /// - `value`: Value at the keyframe
    #[inline]
    pub fn add_keyframe(&mut self, time: f32, value: Variant) {
        // Insert in sorted order
        let insert_pos = self.times.binary_search_by(|&t| t.partial_cmp(&time).unwrap()).unwrap_or_else(|pos| pos);
        self.times.insert(insert_pos, time);
        self.values.insert(insert_pos, value);
    }

    /// ### Gets the interpolated value at a specific time.
    ///
    /// # Parameters
    /// - `time`: Time to sample the animation
    ///
    /// # Returns
    /// The interpolated value at the given time.
    #[inline]
    pub fn sample(&self, time: f32) -> Option<Variant> {
        if self.times.is_empty() || !self.enabled {
            return None;
        }

        // Find the keyframes to interpolate between
        let pos = self.times.binary_search_by(|&t| t.partial_cmp(&time).unwrap()).unwrap_or_else(|pos| pos);

        if pos == 0 {
            // Before first keyframe
            Some(self.values[0].clone())
        } else if pos >= self.times.len() {
            // After last keyframe
            Some(self.values[self.times.len() - 1].clone())
        } else {
            // Interpolate between keyframes
            let t0 = self.times[pos - 1];
            let t1 = self.times[pos];
            let v0 = &self.values[pos - 1];
            let v1 = &self.values[pos];

            if t1 - t0 <= 0.0 {
                Some(v1.clone())
            } else {
                let factor = (time - t0) / (t1 - t0);
                self.interpolate_values(v0, v1, factor)
            }
        }
    }

    /// ### Interpolates between two variant values.
    ///
    /// # Parameters
    /// - `from`: Starting value
    /// - `to`: Ending value
    /// - `factor`: Interpolation factor (0.0 to 1.0)
    ///
    /// # Returns
    /// The interpolated value.
    #[inline]
    fn interpolate_values(&self, from: &Variant, to: &Variant, factor: f32) -> Option<Variant> {
        match (from, to) {
            (Variant::Float(a), Variant::Float(b)) => {
                Some(Variant::Float(a + (b - a) * factor as f64))
            }
            (Variant::Int(a), Variant::Int(b)) => {
                Some(Variant::Int(a + ((b - a) as f32 * factor) as i64))
            }
            _ => {
                // For non-numeric types, use step interpolation
                if factor < 0.5 {
                    Some(from.clone())
                } else {
                    Some(to.clone())
                }
            }
        }
    }

    /// ### Gets the path of this track.
    ///
    /// # Returns
    /// The property path being animated.
    #[inline]
    pub fn path(&self) -> &str {
        &self.path
    }

    /// ### Checks if the track is enabled.
    ///
    /// # Returns
    /// True if the track is enabled, false otherwise.
    #[inline]
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }

    /// ### Sets whether the track is enabled.
    ///
    /// # Parameters
    /// - `enabled`: Whether to enable the track
    #[inline]
    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }

    /// ### Gets the number of keyframes in the track.
    ///
    /// # Returns
    /// The number of keyframes.
    #[inline]
    pub fn keyframe_count(&self) -> usize {
        self.times.len()
    }

    /// ### Gets the duration of the track.
    ///
    /// # Returns
    /// The time of the last keyframe, or 0.0 if no keyframes.
    #[inline]
    pub fn duration(&self) -> f32 {
        self.times.last().copied().unwrap_or(0.0)
    }
}

/// ### Animation resource containing tracks and metadata.
///
/// Represents a complete animation with multiple tracks that can
/// animate different properties simultaneously.
#[derive(Debug, Clone)]
pub struct Animation {
    /// Name of the animation
    name: String,
    /// Animation tracks
    tracks: Vec<AnimationTrack>,
    /// Animation length in seconds
    length: f32,
    /// Whether the animation loops
    loop_mode: bool,
    /// Animation step for discrete animations
    step: f32,
}

impl Animation {
    /// ### Creates a new Animation.
    ///
    /// # Parameters
    /// - `name`: Name of the animation
    ///
    /// # Returns
    /// A new Animation instance.
    #[inline]
    pub fn new(name: String) -> Self {
        Self {
            name,
            tracks: Vec::new(),
            length: 1.0,
            loop_mode: false,
            step: 0.1,
        }
    }

    /// ### Adds a track to the animation.
    ///
    /// # Parameters
    /// - `track`: The animation track to add
    #[inline]
    pub fn add_track(&mut self, track: AnimationTrack) {
        self.tracks.push(track);
        self.update_length();
    }

    /// ### Updates the animation length based on track durations.
    #[inline]
    fn update_length(&mut self) {
        self.length = self.tracks.iter()
            .map(|track| track.duration())
            .fold(0.0, f32::max);
    }

    /// ### Gets the animation name.
    ///
    /// # Returns
    /// The name of the animation.
    #[inline]
    pub fn name(&self) -> &str {
        &self.name
    }

    /// ### Gets the animation length.
    ///
    /// # Returns
    /// The duration of the animation in seconds.
    #[inline]
    pub fn length(&self) -> f32 {
        self.length
    }

    /// ### Sets the animation length.
    ///
    /// # Parameters
    /// - `length`: New animation length in seconds
    #[inline]
    pub fn set_length(&mut self, length: f32) {
        self.length = length.max(0.0);
    }

    /// ### Checks if the animation loops.
    ///
    /// # Returns
    /// True if the animation loops, false otherwise.
    #[inline]
    pub fn is_loop(&self) -> bool {
        self.loop_mode
    }

    /// ### Sets whether the animation loops.
    ///
    /// # Parameters
    /// - `loop_mode`: Whether the animation should loop
    #[inline]
    pub fn set_loop(&mut self, loop_mode: bool) {
        self.loop_mode = loop_mode;
    }

    /// ### Gets the animation step.
    ///
    /// # Returns
    /// The animation step value.
    #[inline]
    pub fn step(&self) -> f32 {
        self.step
    }

    /// ### Sets the animation step.
    ///
    /// # Parameters
    /// - `step`: New animation step value
    #[inline]
    pub fn set_step(&mut self, step: f32) {
        self.step = step.max(0.001);
    }

    /// ### Gets the tracks in the animation.
    ///
    /// # Returns
    /// A reference to the animation tracks.
    #[inline]
    pub fn tracks(&self) -> &[AnimationTrack] {
        &self.tracks
    }

    /// ### Gets a mutable reference to the tracks.
    ///
    /// # Returns
    /// A mutable reference to the animation tracks.
    #[inline]
    pub fn tracks_mut(&mut self) -> &mut Vec<AnimationTrack> {
        &mut self.tracks
    }
}

/// ### AnimationPlayer for keyframe animation and property tweening.
///
/// AnimationPlayer extends Node with comprehensive animation functionality,
/// providing keyframe animation, property tweening, timeline control,
/// animation blending, and signal emission for animation events. It maintains
/// full compatibility with Godot's AnimationPlayer class while ensuring
/// efficient animation processing and smooth playback.
///
/// ## Core Features
///
/// - **Keyframe Animation**: Timeline-based property animation
/// - **Property Tweening**: Smooth interpolation between values
/// - **Animation Blending**: Smooth transitions between animations
/// - **Timeline Control**: Play, pause, stop, seek functionality
/// - **Signal Integration**: Animation event signal emission
/// - **Multiple Animations**: Library of named animations
/// - **Godot Compatibility**: API matching Godot's AnimationPlayer
///
/// ## Animation Properties
///
/// AnimationPlayer provides comprehensive animation control:
/// - **Current Animation**: Currently playing animation
/// - **Playback Speed**: Animation speed multiplier
/// - **Position**: Current playback position in seconds
/// - **Autoplay**: Whether to start playing automatically
/// - **Process Mode**: When to update animations
/// - **Method Call Mode**: How to handle method calls in animations
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::AnimationPlayer;
/// # use verturion::core::signal::SignalManager;
/// // Create an animation player
/// let mut player = AnimationPlayer::new("Animator");
/// let mut signal_manager = SignalManager::new();
///
/// // Create and add an animation
/// let mut animation = Animation::new("fade_in".to_string());
/// animation.set_length(2.0);
/// player.add_animation(animation);
///
/// // Configure player
/// player.set_autoplay("fade_in".to_string());
/// player.set_speed_scale(1.5);
///
/// // Register signals
/// signal_manager.register_signal(player.get_animation_finished_signal().clone());
///
/// // Play animation
/// player.play(&mut signal_manager, Some("fade_in".to_string()));
///
/// assert!(player.is_playing());
/// assert_eq!(player.get_current_animation(), Some("fade_in"));
/// ```
#[derive(Debug, Clone)]
pub struct AnimationPlayer {
    /// Base Node functionality
    base: Node,
    /// Animation library
    animations: HashMap<String, Animation>,
    /// Currently playing animation
    current_animation: Option<String>,
    /// Current playback position in seconds
    position: f32,
    /// Playback speed multiplier
    speed_scale: f32,
    /// Whether animation is currently playing
    playing: bool,
    /// Animation to play automatically
    autoplay: String,
    /// Animation playback mode
    playback_mode: AnimationMode,
    /// Animation process mode
    process_mode: AnimationProcessMode,
    /// Method call mode
    method_call_mode: AnimationMethodCallMode,
    /// Signal emitted when animation finishes
    animation_finished_signal: Signal,
    /// Signal emitted when animation starts
    animation_started_signal: Signal,
    /// Signal emitted when animation changes
    animation_changed_signal: Signal,
}

impl AnimationPlayer {
    /// ### Creates a new AnimationPlayer with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this animation player node
    ///
    /// # Returns
    /// A new AnimationPlayer instance with default animation properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AnimationPlayer;
    /// let player = AnimationPlayer::new("Animator");
    /// assert_eq!(player.get_name(), "Animator");
    /// assert!(!player.is_playing());
    /// assert_eq!(player.get_speed_scale(), 1.0);
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        let node = Node::new(name);
        let node_id = node.get_id();
        Self {
            base: node,
            animations: HashMap::new(),
            current_animation: None,
            position: 0.0,
            speed_scale: 1.0,
            playing: false,
            autoplay: String::new(),
            playback_mode: AnimationMode::Forward,
            process_mode: AnimationProcessMode::Idle,
            method_call_mode: AnimationMethodCallMode::Deferred,
            animation_finished_signal: Signal::new("animation_finished", node_id),
            animation_started_signal: Signal::new("animation_started", node_id),
            animation_changed_signal: Signal::new("animation_changed", node_id),
        }
    }

    /// ### Adds an animation to the player's library.
    ///
    /// # Parameters
    /// - `animation`: The animation to add
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::{AnimationPlayer, Animation};
    /// let mut player = AnimationPlayer::new("Player");
    /// let animation = Animation::new("walk".to_string());
    /// player.add_animation(animation);
    /// assert!(player.has_animation("walk"));
    /// ```
    #[inline]
    pub fn add_animation(&mut self, animation: Animation) {
        let name = animation.name().to_string();
        self.animations.insert(name, animation);
    }

    /// ### Removes an animation from the player's library.
    ///
    /// # Parameters
    /// - `name`: Name of the animation to remove
    ///
    /// # Returns
    /// True if the animation was removed, false if it didn't exist.
    #[inline]
    pub fn remove_animation(&mut self, name: &str) -> bool {
        self.animations.remove(name).is_some()
    }

    /// ### Checks if an animation exists in the library.
    ///
    /// # Parameters
    /// - `name`: Name of the animation to check
    ///
    /// # Returns
    /// True if the animation exists, false otherwise.
    #[inline]
    pub fn has_animation(&self, name: &str) -> bool {
        self.animations.contains_key(name)
    }

    /// ### Gets the list of animation names.
    ///
    /// # Returns
    /// A vector of animation names.
    #[inline]
    pub fn get_animation_list(&self) -> Vec<String> {
        self.animations.keys().cloned().collect()
    }

    /// ### Gets a reference to an animation.
    ///
    /// # Parameters
    /// - `name`: Name of the animation
    ///
    /// # Returns
    /// A reference to the animation, or None if it doesn't exist.
    #[inline]
    pub fn get_animation(&self, name: &str) -> Option<&Animation> {
        self.animations.get(name)
    }

    /// ### Gets a mutable reference to an animation.
    ///
    /// # Parameters
    /// - `name`: Name of the animation
    ///
    /// # Returns
    /// A mutable reference to the animation, or None if it doesn't exist.
    #[inline]
    pub fn get_animation_mut(&mut self, name: &str) -> Option<&mut Animation> {
        self.animations.get_mut(name)
    }

    /// ### Gets the current animation name.
    ///
    /// # Returns
    /// The name of the currently playing animation, or None.
    #[inline]
    pub fn get_current_animation(&self) -> Option<&str> {
        self.current_animation.as_deref()
    }

    /// ### Gets the current playback position.
    ///
    /// # Returns
    /// The current position in seconds.
    #[inline]
    pub fn get_current_animation_position(&self) -> f32 {
        self.position
    }

    /// ### Sets the playback position.
    ///
    /// # Parameters
    /// - `position`: New position in seconds
    #[inline]
    pub fn seek(&mut self, position: f32) {
        self.position = position.max(0.0);
    }

    /// ### Gets the speed scale.
    ///
    /// # Returns
    /// The current speed multiplier.
    #[inline]
    pub fn get_speed_scale(&self) -> f32 {
        self.speed_scale
    }

    /// ### Sets the speed scale.
    ///
    /// # Parameters
    /// - `speed`: New speed multiplier
    #[inline]
    pub fn set_speed_scale(&mut self, speed: f32) {
        self.speed_scale = speed;
    }

    /// ### Checks if an animation is currently playing.
    ///
    /// # Returns
    /// True if playing, false otherwise.
    #[inline]
    pub fn is_playing(&self) -> bool {
        self.playing
    }

    /// ### Gets the autoplay animation name.
    ///
    /// # Returns
    /// The name of the autoplay animation.
    #[inline]
    pub fn get_autoplay(&self) -> &str {
        &self.autoplay
    }

    /// ### Sets the autoplay animation.
    ///
    /// # Parameters
    /// - `animation`: Name of the animation to autoplay
    #[inline]
    pub fn set_autoplay(&mut self, animation: String) {
        self.autoplay = animation;
    }

    /// ### Gets the playback mode.
    ///
    /// # Returns
    /// The current animation playback mode.
    #[inline]
    pub fn get_playback_mode(&self) -> AnimationMode {
        self.playback_mode
    }

    /// ### Sets the playback mode.
    ///
    /// # Parameters
    /// - `mode`: New playback mode
    #[inline]
    pub fn set_playback_mode(&mut self, mode: AnimationMode) {
        self.playback_mode = mode;
    }

    /// ### Gets the process mode.
    ///
    /// # Returns
    /// The current animation process mode.
    #[inline]
    pub fn get_process_mode(&self) -> AnimationProcessMode {
        self.process_mode
    }

    /// ### Sets the process mode.
    ///
    /// # Parameters
    /// - `mode`: New process mode
    #[inline]
    pub fn set_process_mode(&mut self, mode: AnimationProcessMode) {
        self.process_mode = mode;
    }

    /// ### Gets the method call mode.
    ///
    /// # Returns
    /// The current method call mode.
    #[inline]
    pub fn get_method_call_mode(&self) -> AnimationMethodCallMode {
        self.method_call_mode
    }

    /// ### Sets the method call mode.
    ///
    /// # Parameters
    /// - `mode`: New method call mode
    #[inline]
    pub fn set_method_call_mode(&mut self, mode: AnimationMethodCallMode) {
        self.method_call_mode = mode;
    }

    /// ### Gets the animation finished signal.
    ///
    /// # Returns
    /// A reference to the animation finished signal.
    #[inline]
    pub fn get_animation_finished_signal(&self) -> &Signal {
        &self.animation_finished_signal
    }

    /// ### Gets the animation started signal.
    ///
    /// # Returns
    /// A reference to the animation started signal.
    #[inline]
    pub fn get_animation_started_signal(&self) -> &Signal {
        &self.animation_started_signal
    }

    /// ### Gets the animation changed signal.
    ///
    /// # Returns
    /// A reference to the animation changed signal.
    #[inline]
    pub fn get_animation_changed_signal(&self) -> &Signal {
        &self.animation_changed_signal
    }

    /// ### Plays an animation.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    /// - `name`: Optional name of the animation to play
    /// - `custom_blend`: Optional custom blend time
    /// - `custom_speed`: Optional custom speed multiplier
    /// - `from_end`: Whether to play from the end
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AnimationPlayer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut player = AnimationPlayer::new("Player");
    /// let mut manager = SignalManager::new();
    /// player.play(&mut manager, Some("walk".to_string()));
    /// ```
    #[inline]
    pub fn play(&mut self, signal_manager: &mut SignalManager, name: Option<String>) {
        self.play_with_options(signal_manager, name, None, None, false);
    }

    /// ### Plays an animation with custom options.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    /// - `name`: Optional name of the animation to play
    /// - `custom_blend`: Optional custom blend time
    /// - `custom_speed`: Optional custom speed multiplier
    /// - `from_end`: Whether to play from the end
    #[inline]
    pub fn play_with_options(
        &mut self,
        signal_manager: &mut SignalManager,
        name: Option<String>,
        _custom_blend: Option<f32>,
        custom_speed: Option<f32>,
        from_end: bool,
    ) {
        let animation_name = name.or_else(|| {
            if !self.autoplay.is_empty() {
                Some(self.autoplay.clone())
            } else {
                self.current_animation.clone()
            }
        });

        if let Some(anim_name) = animation_name {
            if self.animations.contains_key(&anim_name) {
                let old_animation = self.current_animation.clone();
                self.current_animation = Some(anim_name.clone());

                if let Some(speed) = custom_speed {
                    self.speed_scale = speed;
                }

                if from_end {
                    if let Some(animation) = self.animations.get(&anim_name) {
                        self.position = animation.length();
                    }
                } else {
                    self.position = 0.0;
                }

                self.playing = true;

                // Emit signals
                if old_animation != self.current_animation {
                    let mut data = SignalData::empty();
                    data.add_arg(Variant::from(anim_name.clone()));
                    signal_manager.emit(self.animation_changed_signal.id(), data);
                }

                let mut data = SignalData::empty();
                data.add_arg(Variant::from(anim_name));
                signal_manager.emit(self.animation_started_signal.id(), data);
            }
        }
    }

    /// ### Stops the current animation.
    ///
    /// # Parameters
    /// - `reset`: Whether to reset the position to 0
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AnimationPlayer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut player = AnimationPlayer::new("Player");
    /// let mut manager = SignalManager::new();
    /// player.play(&mut manager, Some("walk".to_string()));
    /// player.stop(true);
    /// assert!(!player.is_playing());
    /// ```
    #[inline]
    pub fn stop(&mut self, reset: bool) {
        self.playing = false;
        if reset {
            self.position = 0.0;
        }
    }

    /// ### Pauses the current animation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AnimationPlayer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut player = AnimationPlayer::new("Player");
    /// let mut manager = SignalManager::new();
    /// player.play(&mut manager, Some("walk".to_string()));
    /// player.pause();
    /// assert!(!player.is_playing());
    /// ```
    #[inline]
    pub fn pause(&mut self) {
        self.playing = false;
    }

    /// ### Resumes the current animation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AnimationPlayer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut player = AnimationPlayer::new("Player");
    /// let mut manager = SignalManager::new();
    /// player.play(&mut manager, Some("walk".to_string()));
    /// player.pause();
    /// player.resume();
    /// assert!(player.is_playing());
    /// ```
    #[inline]
    pub fn resume(&mut self) {
        if self.current_animation.is_some() {
            self.playing = true;
        }
    }

    /// ### Updates the animation player with delta time.
    ///
    /// This should be called every frame to update animation playback.
    ///
    /// # Parameters
    /// - `delta`: Time elapsed since last update in seconds
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Returns
    /// True if an animation finished this frame, false otherwise.
    #[inline]
    pub fn update(&mut self, delta: f32, signal_manager: &mut SignalManager) -> bool {
        if !self.playing || self.current_animation.is_none() {
            return false;
        }

        let animation_name = self.current_animation.as_ref().unwrap().clone();
        let animation_length = self.animations.get(&animation_name)
            .map(|anim| anim.length())
            .unwrap_or(0.0);

        if animation_length <= 0.0 {
            return false;
        }

        // Update position
        let old_position = self.position;
        self.position += delta * self.speed_scale;

        // Handle animation completion and looping
        let mut finished = false;
        match self.playback_mode {
            AnimationMode::Forward => {
                if self.position >= animation_length {
                    if let Some(animation) = self.animations.get(&animation_name) {
                        if animation.is_loop() {
                            self.position = self.position % animation_length;
                        } else {
                            self.position = animation_length;
                            self.playing = false;
                            finished = true;
                        }
                    }
                }
            }
            AnimationMode::Backward => {
                if self.position <= 0.0 {
                    self.position = 0.0;
                    self.playing = false;
                    finished = true;
                }
            }
            AnimationMode::Loop => {
                self.position = self.position % animation_length;
            }
            AnimationMode::PingPong => {
                // Simplified ping-pong implementation
                if self.position >= animation_length {
                    self.position = animation_length;
                    self.speed_scale = -self.speed_scale.abs();
                } else if self.position <= 0.0 {
                    self.position = 0.0;
                    self.speed_scale = self.speed_scale.abs();
                }
            }
        }

        // Apply animation to properties (simplified implementation)
        if let Some(animation) = self.animations.get(&animation_name) {
            for track in animation.tracks() {
                if let Some(_value) = track.sample(self.position) {
                    // In a real implementation, this would apply the value to the target property
                    // For now, we just sample the track to validate the animation system
                }
            }
        }

        // Emit finished signal if animation completed
        if finished {
            let mut data = SignalData::empty();
            data.add_arg(Variant::from(animation_name));
            signal_manager.emit(self.animation_finished_signal.id(), data);
        }

        finished
    }

    /// ### Gets the length of the current animation.
    ///
    /// # Returns
    /// The length of the current animation in seconds, or 0.0 if no animation.
    #[inline]
    pub fn get_current_animation_length(&self) -> f32 {
        if let Some(name) = &self.current_animation {
            self.animations.get(name)
                .map(|anim| anim.length())
                .unwrap_or(0.0)
        } else {
            0.0
        }
    }

    /// ### Provides access to the base Node functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node.
    #[inline]
    pub fn base(&self) -> &Node {
        &self.base
    }

    /// ### Provides mutable access to the base Node functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node {
        &mut self.base
    }

    /// ### Gets the node name from the base Node.
    ///
    /// # Returns
    /// The name of this animation player node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.get_name()
    }
}

impl fmt::Display for AnimationPlayer {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "AnimationPlayer({}, current: {:?}, playing: {}, speed: {:.2})",
               self.get_name(),
               self.current_animation,
               self.playing,
               self.speed_scale)
    }
}

impl PartialEq for AnimationPlayer {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for AnimationPlayer {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::signal::SignalManager;

    #[test]
    fn test_animation_track_creation() {
        let track = AnimationTrack::new("position".to_string());
        assert_eq!(track.path(), "position");
        assert!(track.is_enabled());
        assert_eq!(track.keyframe_count(), 0);
        assert_eq!(track.duration(), 0.0);
    }

    #[test]
    fn test_animation_track_keyframes() {
        let mut track = AnimationTrack::new("opacity".to_string());

        // Add keyframes
        track.add_keyframe(0.0, Variant::Float(0.0));
        track.add_keyframe(1.0, Variant::Float(1.0));
        track.add_keyframe(0.5, Variant::Float(0.5));

        assert_eq!(track.keyframe_count(), 3);
        assert_eq!(track.duration(), 1.0);

        // Test sampling
        assert_eq!(track.sample(0.0), Some(Variant::Float(0.0)));
        assert_eq!(track.sample(0.5), Some(Variant::Float(0.5)));
        assert_eq!(track.sample(1.0), Some(Variant::Float(1.0)));

        // Test interpolation
        if let Some(Variant::Float(value)) = track.sample(0.25) {
            assert!((value - 0.25).abs() < 0.01);
        } else {
            panic!("Expected interpolated float value");
        }
    }

    #[test]
    fn test_animation_track_disabled() {
        let mut track = AnimationTrack::new("test".to_string());
        track.add_keyframe(0.0, Variant::Float(1.0));
        track.set_enabled(false);

        assert!(!track.is_enabled());
        assert_eq!(track.sample(0.0), None);
    }

    #[test]
    fn test_animation_creation() {
        let animation = Animation::new("test_anim".to_string());
        assert_eq!(animation.name(), "test_anim");
        assert_eq!(animation.length(), 1.0);
        assert!(!animation.is_loop());
        assert_eq!(animation.step(), 0.1);
        assert_eq!(animation.tracks().len(), 0);
    }

    #[test]
    fn test_animation_properties() {
        let mut animation = Animation::new("test".to_string());

        // Test length
        animation.set_length(2.5);
        assert_eq!(animation.length(), 2.5);

        // Test negative length clamping
        animation.set_length(-1.0);
        assert_eq!(animation.length(), 0.0);

        // Test loop mode
        animation.set_loop(true);
        assert!(animation.is_loop());

        // Test step
        animation.set_step(0.05);
        assert_eq!(animation.step(), 0.05);

        // Test minimum step
        animation.set_step(-1.0);
        assert_eq!(animation.step(), 0.001);
    }

    #[test]
    fn test_animation_tracks() {
        let mut animation = Animation::new("test".to_string());

        let mut track1 = AnimationTrack::new("position.x".to_string());
        track1.add_keyframe(0.0, Variant::Float(0.0));
        track1.add_keyframe(2.0, Variant::Float(100.0));

        let mut track2 = AnimationTrack::new("position.y".to_string());
        track2.add_keyframe(0.0, Variant::Float(0.0));
        track2.add_keyframe(1.5, Variant::Float(50.0));

        animation.add_track(track1);
        animation.add_track(track2);

        assert_eq!(animation.tracks().len(), 2);
        assert_eq!(animation.length(), 2.0); // Updated to longest track
    }

    #[test]
    fn test_animation_player_creation() {
        let player = AnimationPlayer::new("TestPlayer");
        assert_eq!(player.get_name(), "TestPlayer");
        assert!(!player.is_playing());
        assert_eq!(player.get_speed_scale(), 1.0);
        assert_eq!(player.get_current_animation(), None);
        assert_eq!(player.get_current_animation_position(), 0.0);
        assert_eq!(player.get_autoplay(), "");
        assert_eq!(player.get_playback_mode(), AnimationMode::Forward);
        assert_eq!(player.get_process_mode(), AnimationProcessMode::Idle);
        assert_eq!(player.get_method_call_mode(), AnimationMethodCallMode::Deferred);
    }

    #[test]
    fn test_animation_player_properties() {
        let mut player = AnimationPlayer::new("Player");

        // Test speed scale
        player.set_speed_scale(2.0);
        assert_eq!(player.get_speed_scale(), 2.0);

        // Test autoplay
        player.set_autoplay("idle".to_string());
        assert_eq!(player.get_autoplay(), "idle");

        // Test playback mode
        player.set_playback_mode(AnimationMode::Loop);
        assert_eq!(player.get_playback_mode(), AnimationMode::Loop);

        // Test process mode
        player.set_process_mode(AnimationProcessMode::Physics);
        assert_eq!(player.get_process_mode(), AnimationProcessMode::Physics);

        // Test method call mode
        player.set_method_call_mode(AnimationMethodCallMode::Immediate);
        assert_eq!(player.get_method_call_mode(), AnimationMethodCallMode::Immediate);
    }

    #[test]
    fn test_animation_library_management() {
        let mut player = AnimationPlayer::new("Player");

        // Test adding animations
        let anim1 = Animation::new("walk".to_string());
        let anim2 = Animation::new("run".to_string());

        player.add_animation(anim1);
        player.add_animation(anim2);

        assert!(player.has_animation("walk"));
        assert!(player.has_animation("run"));
        assert!(!player.has_animation("jump"));

        let anim_list = player.get_animation_list();
        assert_eq!(anim_list.len(), 2);
        assert!(anim_list.contains(&"walk".to_string()));
        assert!(anim_list.contains(&"run".to_string()));

        // Test getting animations
        assert!(player.get_animation("walk").is_some());
        assert!(player.get_animation("nonexistent").is_none());

        // Test removing animations
        assert!(player.remove_animation("walk"));
        assert!(!player.remove_animation("nonexistent"));
        assert!(!player.has_animation("walk"));
        assert!(player.has_animation("run"));
    }

    #[test]
    fn test_animation_playback_control() {
        let mut player = AnimationPlayer::new("Player");
        let mut signal_manager = SignalManager::new();

        // Register signals
        signal_manager.register_signal(player.get_animation_started_signal().clone());
        signal_manager.register_signal(player.get_animation_finished_signal().clone());
        signal_manager.register_signal(player.get_animation_changed_signal().clone());

        // Add test animation
        let mut animation = Animation::new("test".to_string());
        animation.set_length(2.0);
        player.add_animation(animation);

        // Test play
        player.play(&mut signal_manager, Some("test".to_string()));
        assert!(player.is_playing());
        assert_eq!(player.get_current_animation(), Some("test"));
        assert_eq!(player.get_current_animation_position(), 0.0);

        // Test pause
        player.pause();
        assert!(!player.is_playing());

        // Test resume
        player.resume();
        assert!(player.is_playing());

        // Test stop
        player.stop(true);
        assert!(!player.is_playing());
        assert_eq!(player.get_current_animation_position(), 0.0);
    }

    #[test]
    fn test_animation_seeking() {
        let mut player = AnimationPlayer::new("Player");

        // Add test animation
        let mut animation = Animation::new("test".to_string());
        animation.set_length(5.0);
        player.add_animation(animation);

        // Test seeking
        player.seek(2.5);
        assert_eq!(player.get_current_animation_position(), 2.5);

        // Test negative seek clamping
        player.seek(-1.0);
        assert_eq!(player.get_current_animation_position(), 0.0);
    }

    #[test]
    fn test_animation_update_and_completion() {
        let mut player = AnimationPlayer::new("Player");
        let mut signal_manager = SignalManager::new();

        // Register signals
        signal_manager.register_signal(player.get_animation_finished_signal().clone());

        // Add test animation
        let mut animation = Animation::new("test".to_string());
        animation.set_length(1.0);
        animation.set_loop(false);
        player.add_animation(animation);

        // Start playing
        player.play(&mut signal_manager, Some("test".to_string()));

        // Update without finishing
        let finished = player.update(0.5, &mut signal_manager);
        assert!(!finished);
        assert!(player.is_playing());
        assert_eq!(player.get_current_animation_position(), 0.5);

        // Update to finish
        let finished = player.update(0.6, &mut signal_manager);
        assert!(finished);
        assert!(!player.is_playing());
        assert_eq!(player.get_current_animation_position(), 1.0);
    }

    #[test]
    fn test_animation_looping() {
        let mut player = AnimationPlayer::new("Player");
        let mut signal_manager = SignalManager::new();

        // Add looping animation
        let mut animation = Animation::new("loop_test".to_string());
        animation.set_length(1.0);
        animation.set_loop(true);
        player.add_animation(animation);

        // Start playing
        player.play(&mut signal_manager, Some("loop_test".to_string()));

        // Update past the end
        let finished = player.update(1.5, &mut signal_manager);
        assert!(!finished); // Should not finish due to looping
        assert!(player.is_playing());
        assert_eq!(player.get_current_animation_position(), 0.5); // Should wrap around
    }

    #[test]
    fn test_animation_speed_scale() {
        let mut player = AnimationPlayer::new("Player");
        let mut signal_manager = SignalManager::new();

        // Add test animation
        let mut animation = Animation::new("speed_test".to_string());
        animation.set_length(2.0);
        player.add_animation(animation);

        // Set speed scale
        player.set_speed_scale(2.0);
        player.play(&mut signal_manager, Some("speed_test".to_string()));

        // Update with double speed
        player.update(0.5, &mut signal_manager);
        assert_eq!(player.get_current_animation_position(), 1.0); // 0.5 * 2.0 speed
    }

    #[test]
    fn test_animation_autoplay() {
        let mut player = AnimationPlayer::new("Player");
        let mut signal_manager = SignalManager::new();

        // Add animation and set autoplay
        let animation = Animation::new("auto".to_string());
        player.add_animation(animation);
        player.set_autoplay("auto".to_string());

        // Play without specifying animation (should use autoplay)
        player.play(&mut signal_manager, None);
        assert!(player.is_playing());
        assert_eq!(player.get_current_animation(), Some("auto"));
    }

    #[test]
    fn test_animation_current_length() {
        let mut player = AnimationPlayer::new("Player");
        let mut signal_manager = SignalManager::new();

        // No current animation
        assert_eq!(player.get_current_animation_length(), 0.0);

        // Add and play animation
        let mut animation = Animation::new("length_test".to_string());
        animation.set_length(3.5);
        player.add_animation(animation);
        player.play(&mut signal_manager, Some("length_test".to_string()));

        assert_eq!(player.get_current_animation_length(), 3.5);
    }

    #[test]
    fn test_animation_player_base_access() {
        let mut player = AnimationPlayer::new("BaseTest");

        // Test base access
        assert_eq!(player.base().get_name(), "BaseTest");

        // Test mutable base access
        player.base_mut().set_name("NewName");
        assert_eq!(player.get_name(), "NewName");
    }

    #[test]
    fn test_animation_player_equality() {
        let player1 = AnimationPlayer::new("Player1");
        let player2 = AnimationPlayer::new("Player2");
        let player1_clone = player1.clone();

        // Same player should be equal
        assert_eq!(player1, player1_clone);

        // Different players should not be equal
        assert_ne!(player1, player2);
    }

    #[test]
    fn test_animation_player_display() {
        let mut player = AnimationPlayer::new("DisplayTest");
        let mut signal_manager = SignalManager::new();

        // Add and play animation
        let animation = Animation::new("test_anim".to_string());
        player.add_animation(animation);
        player.set_speed_scale(1.5);
        player.play(&mut signal_manager, Some("test_anim".to_string()));

        let display_str = format!("{}", player);
        assert!(display_str.contains("DisplayTest"));
        assert!(display_str.contains("test_anim"));
        assert!(display_str.contains("playing: true"));
        assert!(display_str.contains("speed: 1.50"));
    }

    #[test]
    fn test_animation_modes() {
        // Test all animation modes for completeness
        assert_eq!(AnimationMode::Forward, AnimationMode::Forward);
        assert_ne!(AnimationMode::Forward, AnimationMode::Backward);

        assert_eq!(AnimationProcessMode::Idle, AnimationProcessMode::Idle);
        assert_ne!(AnimationProcessMode::Idle, AnimationProcessMode::Physics);

        assert_eq!(AnimationMethodCallMode::Deferred, AnimationMethodCallMode::Deferred);
        assert_ne!(AnimationMethodCallMode::Deferred, AnimationMethodCallMode::Immediate);
    }

    #[test]
    fn test_complex_animation_scenario() {
        let mut player = AnimationPlayer::new("ComplexTest");
        let mut signal_manager = SignalManager::new();

        // Register all signals
        signal_manager.register_signal(player.get_animation_started_signal().clone());
        signal_manager.register_signal(player.get_animation_finished_signal().clone());
        signal_manager.register_signal(player.get_animation_changed_signal().clone());

        // Create complex animation with multiple tracks
        let mut animation = Animation::new("complex".to_string());
        animation.set_length(3.0);
        animation.set_loop(false);

        let mut position_track = AnimationTrack::new("position".to_string());
        position_track.add_keyframe(0.0, Variant::Float(0.0));
        position_track.add_keyframe(1.5, Variant::Float(50.0));
        position_track.add_keyframe(3.0, Variant::Float(100.0));

        let mut opacity_track = AnimationTrack::new("opacity".to_string());
        opacity_track.add_keyframe(0.0, Variant::Float(0.0));
        opacity_track.add_keyframe(1.0, Variant::Float(1.0));
        opacity_track.add_keyframe(3.0, Variant::Float(0.0));

        animation.add_track(position_track);
        animation.add_track(opacity_track);
        player.add_animation(animation);

        // Configure player
        player.set_speed_scale(1.2);
        player.set_playback_mode(AnimationMode::Forward);

        // Play animation
        player.play(&mut signal_manager, Some("complex".to_string()));
        assert!(player.is_playing());
        assert_eq!(player.get_current_animation(), Some("complex"));

        // Update through animation
        for i in 0..10 {
            let delta = 0.3;
            let finished = player.update(delta, &mut signal_manager);

            if finished {
                assert!(!player.is_playing());
                break;
            }

            // Verify position is advancing
            let expected_pos = (i + 1) as f32 * delta * 1.2; // delta * speed_scale
            let actual_pos = player.get_current_animation_position();
            assert!((actual_pos - expected_pos.min(3.0)).abs() < 0.01);
        }

        // Verify final state
        assert!(!player.is_playing());
        assert_eq!(player.get_current_animation_position(), 3.0);
        assert_eq!(player.get_current_animation_length(), 3.0);
    }
}
