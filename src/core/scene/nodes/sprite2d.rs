//! Sprite2D implementation for displaying 2D textures with advanced rendering options.
//!
//! This module provides the Sprite2D class that extends Node2D with texture rendering
//! capabilities including texture regions, flipping, centering, and filtering options.
//! It maintains full compatibility with Godot's Sprite2D class while providing
//! efficient 2D sprite rendering for game development.

use std::fmt;
use crate::core::math::{Vector2, Rect2};
use crate::core::variant::Color;
use crate::core::scene::nodes::Node2D;

/// ### Texture filtering modes for sprite rendering.
///
/// Defines how textures are filtered when scaled or rotated.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum TextureFilter {
    /// Nearest neighbor filtering (pixelated)
    Nearest,
    /// Linear filtering (smooth)
    Linear,
}

/// ### Texture repeat modes for sprite rendering.
///
/// Defines how textures behave when UV coordinates exceed [0,1] range.
#[derive(<PERSON>bug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Eq)]
pub enum TextureRepeat {
    /// No repeat, clamp to edge
    Disabled,
    /// Repeat texture
    Enabled,
    /// Mirror repeat texture
    Mirror,
}

/// ### 2D sprite node for displaying textures with advanced rendering options.
///
/// Sprite2D extends Node2D with comprehensive texture rendering capabilities,
/// providing texture display, region support, flipping, centering, and filtering
/// options. It maintains full compatibility with Godot's Sprite2D class while
/// ensuring efficient 2D sprite rendering and manipulation.
///
/// ## Core Features
///
/// - **Texture Rendering**: Display 2D textures with full transform support
/// - **Region Support**: Display specific portions of textures
/// - **Flip Options**: Horizontal and vertical texture flipping
/// - **Centering**: Automatic sprite centering around pivot point
/// - **Filtering**: Nearest neighbor and linear texture filtering
/// - **Modulation**: Color tinting and transparency effects
/// - **Godot Compatibility**: API matching Godot's Sprite2D class
///
/// ## Texture Properties
///
/// Sprite2D provides comprehensive texture manipulation:
/// - **Texture**: The texture resource to display
/// - **Region**: Specific texture region to display (UV coordinates)
/// - **Flip H/V**: Horizontal and vertical flipping
/// - **Centered**: Whether to center the sprite around its position
/// - **Offset**: Additional positioning offset
/// - **Modulate**: Color tinting and alpha blending
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::Sprite2D;
/// # use verturion::core::math::{Vector2, Rect2};
/// # use verturion::core::variant::Color;
/// // Create a sprite
/// let mut sprite = Sprite2D::new("PlayerSprite");
///
/// // Configure sprite properties
/// sprite.set_centered(true);
/// sprite.set_flip_h(false);
/// sprite.set_flip_v(false);
/// sprite.set_modulate(Color::new(1.0, 0.8, 0.8, 1.0)); // Slight red tint
///
/// // Set texture region (if using sprite sheet)
/// sprite.set_region_enabled(true);
/// sprite.set_region_rect(Rect2::new(0.0, 0.0, 64.0, 64.0));
///
/// assert!(sprite.is_centered());
/// assert!(sprite.is_region_enabled());
/// ```
#[derive(Debug, Clone)]
pub struct Sprite2D {
    /// Base Node2D functionality
    base: Node2D,
    /// Texture resource path or identifier
    texture: Option<String>,
    /// Whether to use texture region
    region_enabled: bool,
    /// Texture region rectangle (in pixels)
    region_rect: Rect2,
    /// Whether to flip texture horizontally
    flip_h: bool,
    /// Whether to flip texture vertically
    flip_v: bool,
    /// Whether sprite is centered around position
    centered: bool,
    /// Additional positioning offset
    offset: Vector2,
    /// Color modulation (tint and alpha)
    modulate: Color,
    /// Texture filtering mode
    texture_filter: TextureFilter,
    /// Texture repeat mode
    texture_repeat: TextureRepeat,
}

impl Sprite2D {
    /// ### Creates a new Sprite2D with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this sprite node
    ///
    /// # Returns
    /// A new Sprite2D instance with default rendering properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Sprite2D;
    /// let sprite = Sprite2D::new("PlayerSprite");
    /// assert_eq!(sprite.get_name(), "PlayerSprite");
    /// assert!(sprite.is_centered());
    /// assert!(!sprite.is_region_enabled());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            base: Node2D::new(name),
            texture: None,
            region_enabled: false,
            region_rect: Rect2::new(0.0, 0.0, 0.0, 0.0),
            flip_h: false,
            flip_v: false,
            centered: true,
            offset: Vector2::ZERO,
            modulate: Color::WHITE,
            texture_filter: TextureFilter::Linear,
            texture_repeat: TextureRepeat::Disabled,
        }
    }

    /// ### Gets the texture resource path.
    ///
    /// # Returns
    /// The current texture path, or None if no texture is set.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Sprite2D;
    /// let sprite = Sprite2D::new("Sprite");
    /// assert_eq!(sprite.get_texture(), None);
    /// ```
    #[inline]
    pub fn get_texture(&self) -> Option<&String> {
        self.texture.as_ref()
    }

    /// ### Sets the texture resource path.
    ///
    /// # Parameters
    /// - `texture`: The texture path, or None to clear the texture
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Sprite2D;
    /// let mut sprite = Sprite2D::new("Sprite");
    /// sprite.set_texture(Some("res://textures/player.png".to_string()));
    /// assert_eq!(sprite.get_texture(), Some(&"res://textures/player.png".to_string()));
    /// ```
    #[inline]
    pub fn set_texture(&mut self, texture: Option<String>) {
        self.texture = texture;
    }

    /// ### Checks if texture region is enabled.
    ///
    /// # Returns
    /// True if region rendering is enabled, false otherwise.
    #[inline]
    pub fn is_region_enabled(&self) -> bool {
        self.region_enabled
    }

    /// ### Sets whether texture region is enabled.
    ///
    /// # Parameters
    /// - `enabled`: Whether to enable region rendering
    #[inline]
    pub fn set_region_enabled(&mut self, enabled: bool) {
        self.region_enabled = enabled;
    }

    /// ### Gets the texture region rectangle.
    ///
    /// # Returns
    /// The current region rectangle in texture pixels.
    #[inline]
    pub fn get_region_rect(&self) -> Rect2 {
        self.region_rect
    }

    /// ### Sets the texture region rectangle.
    ///
    /// # Parameters
    /// - `rect`: The region rectangle in texture pixels
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Sprite2D;
    /// # use verturion::core::math::Rect2;
    /// let mut sprite = Sprite2D::new("Sprite");
    /// sprite.set_region_rect(Rect2::new(32.0, 64.0, 32.0, 32.0));
    /// assert_eq!(sprite.get_region_rect(), Rect2::new(32.0, 64.0, 32.0, 32.0));
    /// ```
    #[inline]
    pub fn set_region_rect(&mut self, rect: Rect2) {
        self.region_rect = rect;
    }

    /// ### Checks if the sprite is flipped horizontally.
    ///
    /// # Returns
    /// True if horizontally flipped, false otherwise.
    #[inline]
    pub fn is_flip_h(&self) -> bool {
        self.flip_h
    }

    /// ### Sets horizontal flipping.
    ///
    /// # Parameters
    /// - `flip`: Whether to flip horizontally
    #[inline]
    pub fn set_flip_h(&mut self, flip: bool) {
        self.flip_h = flip;
    }

    /// ### Checks if the sprite is flipped vertically.
    ///
    /// # Returns
    /// True if vertically flipped, false otherwise.
    #[inline]
    pub fn is_flip_v(&self) -> bool {
        self.flip_v
    }

    /// ### Sets vertical flipping.
    ///
    /// # Parameters
    /// - `flip`: Whether to flip vertically
    #[inline]
    pub fn set_flip_v(&mut self, flip: bool) {
        self.flip_v = flip;
    }

    /// ### Checks if the sprite is centered around its position.
    ///
    /// # Returns
    /// True if centered, false otherwise.
    #[inline]
    pub fn is_centered(&self) -> bool {
        self.centered
    }

    /// ### Sets whether the sprite is centered around its position.
    ///
    /// # Parameters
    /// - `centered`: Whether to center the sprite
    #[inline]
    pub fn set_centered(&mut self, centered: bool) {
        self.centered = centered;
    }

    /// ### Gets the sprite offset.
    ///
    /// # Returns
    /// The current offset vector.
    #[inline]
    pub fn get_offset(&self) -> Vector2 {
        self.offset
    }

    /// ### Sets the sprite offset.
    ///
    /// # Parameters
    /// - `offset`: The new offset vector
    #[inline]
    pub fn set_offset(&mut self, offset: Vector2) {
        self.offset = offset;
    }

    /// ### Gets the color modulation.
    ///
    /// # Returns
    /// The current modulation color.
    #[inline]
    pub fn get_modulate(&self) -> Color {
        self.modulate
    }

    /// ### Sets the color modulation.
    ///
    /// # Parameters
    /// - `modulate`: The new modulation color
    #[inline]
    pub fn set_modulate(&mut self, modulate: Color) {
        self.modulate = modulate;
    }

    /// ### Gets the texture filtering mode.
    ///
    /// # Returns
    /// The current texture filter mode.
    #[inline]
    pub fn get_texture_filter(&self) -> TextureFilter {
        self.texture_filter
    }

    /// ### Sets the texture filtering mode.
    ///
    /// # Parameters
    /// - `filter`: The new texture filter mode
    #[inline]
    pub fn set_texture_filter(&mut self, filter: TextureFilter) {
        self.texture_filter = filter;
    }

    /// ### Gets the texture repeat mode.
    ///
    /// # Returns
    /// The current texture repeat mode.
    #[inline]
    pub fn get_texture_repeat(&self) -> TextureRepeat {
        self.texture_repeat
    }

    /// ### Sets the texture repeat mode.
    ///
    /// # Parameters
    /// - `repeat`: The new texture repeat mode
    #[inline]
    pub fn set_texture_repeat(&mut self, repeat: TextureRepeat) {
        self.texture_repeat = repeat;
    }

    /// ### Provides access to the base Node2D functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node2D.
    #[inline]
    pub fn base(&self) -> &Node2D {
        &self.base
    }

    /// ### Provides mutable access to the base Node2D functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node2D.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node2D {
        &mut self.base
    }

    /// ### Gets the node name from the base Node2D.
    ///
    /// # Returns
    /// The name of this sprite node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }
}

impl fmt::Display for Sprite2D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Sprite2D({}, texture: {:?}, centered: {}, flip_h: {}, flip_v: {})",
               self.get_name(), self.texture, self.centered, self.flip_h, self.flip_v)
    }
}

impl PartialEq for Sprite2D {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for Sprite2D {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sprite2d_creation() {
        let sprite = Sprite2D::new("TestSprite");
        assert_eq!(sprite.get_name(), "TestSprite");
        assert!(sprite.is_centered());
        assert!(!sprite.is_region_enabled());
        assert!(!sprite.is_flip_h());
        assert!(!sprite.is_flip_v());
        assert_eq!(sprite.get_texture(), None);
        assert_eq!(sprite.get_modulate(), Color::WHITE);
    }

    #[test]
    fn test_sprite2d_texture_management() {
        let mut sprite = Sprite2D::new("Sprite");

        // Initially no texture
        assert_eq!(sprite.get_texture(), None);

        // Set texture
        sprite.set_texture(Some("res://textures/player.png".to_string()));
        assert_eq!(sprite.get_texture(), Some(&"res://textures/player.png".to_string()));

        // Clear texture
        sprite.set_texture(None);
        assert_eq!(sprite.get_texture(), None);
    }

    #[test]
    fn test_sprite2d_region_functionality() {
        let mut sprite = Sprite2D::new("Sprite");

        // Initially region disabled
        assert!(!sprite.is_region_enabled());
        assert_eq!(sprite.get_region_rect(), Rect2::new(0.0, 0.0, 0.0, 0.0));

        // Enable region and set rectangle
        sprite.set_region_enabled(true);
        sprite.set_region_rect(Rect2::new(32.0, 64.0, 32.0, 32.0));

        assert!(sprite.is_region_enabled());
        assert_eq!(sprite.get_region_rect(), Rect2::new(32.0, 64.0, 32.0, 32.0));

        // Disable region
        sprite.set_region_enabled(false);
        assert!(!sprite.is_region_enabled());
    }

    #[test]
    fn test_sprite2d_flipping() {
        let mut sprite = Sprite2D::new("Sprite");

        // Initially no flipping
        assert!(!sprite.is_flip_h());
        assert!(!sprite.is_flip_v());

        // Enable horizontal flip
        sprite.set_flip_h(true);
        assert!(sprite.is_flip_h());
        assert!(!sprite.is_flip_v());

        // Enable vertical flip
        sprite.set_flip_v(true);
        assert!(sprite.is_flip_h());
        assert!(sprite.is_flip_v());

        // Disable horizontal flip
        sprite.set_flip_h(false);
        assert!(!sprite.is_flip_h());
        assert!(sprite.is_flip_v());

        // Disable vertical flip
        sprite.set_flip_v(false);
        assert!(!sprite.is_flip_h());
        assert!(!sprite.is_flip_v());
    }

    #[test]
    fn test_sprite2d_centering() {
        let mut sprite = Sprite2D::new("Sprite");

        // Initially centered
        assert!(sprite.is_centered());

        // Disable centering
        sprite.set_centered(false);
        assert!(!sprite.is_centered());

        // Re-enable centering
        sprite.set_centered(true);
        assert!(sprite.is_centered());
    }

    #[test]
    fn test_sprite2d_offset() {
        let mut sprite = Sprite2D::new("Sprite");

        // Initially zero offset
        assert_eq!(sprite.get_offset(), Vector2::ZERO);

        // Set offset
        sprite.set_offset(Vector2::new(10.0, -5.0));
        assert_eq!(sprite.get_offset(), Vector2::new(10.0, -5.0));

        // Reset offset
        sprite.set_offset(Vector2::ZERO);
        assert_eq!(sprite.get_offset(), Vector2::ZERO);
    }

    #[test]
    fn test_sprite2d_modulation() {
        let mut sprite = Sprite2D::new("Sprite");

        // Initially white (no modulation)
        assert_eq!(sprite.get_modulate(), Color::WHITE);

        // Set red tint with transparency
        let red_tint = Color::new(1.0, 0.5, 0.5, 0.8);
        sprite.set_modulate(red_tint);
        assert_eq!(sprite.get_modulate(), red_tint);

        // Reset to white
        sprite.set_modulate(Color::WHITE);
        assert_eq!(sprite.get_modulate(), Color::WHITE);
    }

    #[test]
    fn test_sprite2d_texture_filtering() {
        let mut sprite = Sprite2D::new("Sprite");

        // Initially linear filtering
        assert_eq!(sprite.get_texture_filter(), TextureFilter::Linear);

        // Set nearest filtering
        sprite.set_texture_filter(TextureFilter::Nearest);
        assert_eq!(sprite.get_texture_filter(), TextureFilter::Nearest);

        // Back to linear
        sprite.set_texture_filter(TextureFilter::Linear);
        assert_eq!(sprite.get_texture_filter(), TextureFilter::Linear);
    }

    #[test]
    fn test_sprite2d_texture_repeat() {
        let mut sprite = Sprite2D::new("Sprite");

        // Initially disabled
        assert_eq!(sprite.get_texture_repeat(), TextureRepeat::Disabled);

        // Enable repeat
        sprite.set_texture_repeat(TextureRepeat::Enabled);
        assert_eq!(sprite.get_texture_repeat(), TextureRepeat::Enabled);

        // Set mirror repeat
        sprite.set_texture_repeat(TextureRepeat::Mirror);
        assert_eq!(sprite.get_texture_repeat(), TextureRepeat::Mirror);

        // Back to disabled
        sprite.set_texture_repeat(TextureRepeat::Disabled);
        assert_eq!(sprite.get_texture_repeat(), TextureRepeat::Disabled);
    }

    #[test]
    fn test_sprite2d_base_access() {
        let mut sprite = Sprite2D::new("Sprite");

        // Test base access
        assert_eq!(sprite.base().base().get_name(), "Sprite");

        // Test mutable base access
        sprite.base_mut().set_position(Vector2::new(100.0, 200.0));
        assert_eq!(sprite.base().get_position(), Vector2::new(100.0, 200.0));
    }

    #[test]
    fn test_sprite2d_equality() {
        let sprite1 = Sprite2D::new("Sprite1");
        let sprite2 = Sprite2D::new("Sprite2");
        let sprite1_clone = sprite1.clone();

        // Same sprite should be equal
        assert_eq!(sprite1, sprite1_clone);

        // Different sprites should not be equal
        assert_ne!(sprite1, sprite2);
    }

    #[test]
    fn test_sprite2d_display() {
        let mut sprite = Sprite2D::new("TestSprite");
        sprite.set_texture(Some("test.png".to_string()));
        sprite.set_flip_h(true);

        let display_str = format!("{}", sprite);
        assert!(display_str.contains("TestSprite"));
        assert!(display_str.contains("test.png"));
        assert!(display_str.contains("flip_h: true"));
    }

    #[test]
    fn test_sprite2d_complex_configuration() {
        let mut sprite = Sprite2D::new("ComplexSprite");

        // Configure all properties
        sprite.set_texture(Some("res://sprites/character.png".to_string()));
        sprite.set_region_enabled(true);
        sprite.set_region_rect(Rect2::new(64.0, 128.0, 64.0, 64.0));
        sprite.set_flip_h(true);
        sprite.set_flip_v(false);
        sprite.set_centered(false);
        sprite.set_offset(Vector2::new(32.0, 16.0));
        sprite.set_modulate(Color::new(0.8, 1.0, 0.9, 0.95));
        sprite.set_texture_filter(TextureFilter::Nearest);
        sprite.set_texture_repeat(TextureRepeat::Mirror);

        // Verify all properties
        assert_eq!(sprite.get_texture(), Some(&"res://sprites/character.png".to_string()));
        assert!(sprite.is_region_enabled());
        assert_eq!(sprite.get_region_rect(), Rect2::new(64.0, 128.0, 64.0, 64.0));
        assert!(sprite.is_flip_h());
        assert!(!sprite.is_flip_v());
        assert!(!sprite.is_centered());
        assert_eq!(sprite.get_offset(), Vector2::new(32.0, 16.0));
        assert_eq!(sprite.get_modulate(), Color::new(0.8, 1.0, 0.9, 0.95));
        assert_eq!(sprite.get_texture_filter(), TextureFilter::Nearest);
        assert_eq!(sprite.get_texture_repeat(), TextureRepeat::Mirror);
    }
}
