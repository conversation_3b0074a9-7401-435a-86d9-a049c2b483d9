//! Timer implementation for time-based events and delays.
//!
//! This module provides the Timer class that extends Node with time-based
//! functionality including timeouts, repeating timers, and pause support.
//! It maintains full compatibility with <PERSON><PERSON>'s Timer class while providing
//! efficient time management and signal emission for game development.

use std::fmt;
use crate::core::scene::Node;
use crate::core::signal::{Signal, SignalManager, SignalData};

/// ### Timer modes for different timing behaviors.
///
/// Defines how the timer behaves when it reaches timeout.
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum TimerMode {
    /// Timer fires once and stops
    OneShot,
    /// Timer repeats continuously
    Repeating,
}

/// ### Timer for time-based events and delays.
///
/// Timer extends Node with comprehensive time-based functionality,
/// providing timeout events, repeating timers, pause support, and
/// signal emission for game logic. It maintains full compatibility
/// with <PERSON><PERSON>'s Timer class while ensuring precise timing control.
///
/// ## Core Features
///
/// - **Timeout Events**: Emit signals when timer reaches zero
/// - **Timer Modes**: One-shot and repeating timer support
/// - **Pause Control**: Pause and resume timer functionality
/// - **Time Management**: Precise time tracking and updates
/// - **Signal Integration**: Timeout signal emission
/// - **Godot Compatibility**: API matching Godot's Timer class
///
/// ## Timer Properties
///
/// Timer provides comprehensive timing control:
/// - **Wait Time**: Duration before timeout (in seconds)
/// - **Time Left**: Remaining time until timeout
/// - **Autostart**: Whether timer starts automatically
/// - **Paused**: Whether timer is currently paused
/// - **One Shot**: Whether timer stops after first timeout
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::Timer;
/// # use verturion::core::signal::SignalManager;
/// // Create a timer
/// let mut timer = Timer::new("GameTimer");
/// let mut signal_manager = SignalManager::new();
///
/// // Configure timer
/// timer.set_wait_time(5.0); // 5 second timer
/// timer.set_one_shot(true);
/// timer.set_autostart(true);
///
/// // Register timeout signal
/// signal_manager.register_signal(timer.get_timeout_signal().clone());
///
/// // Start the timer
/// timer.start(&mut signal_manager);
///
/// assert!(timer.is_active());
/// assert_eq!(timer.get_wait_time(), 5.0);
/// ```
#[derive(Debug, Clone)]
pub struct Timer {
    /// Base Node functionality
    base: Node,
    /// Timer duration in seconds
    wait_time: f64,
    /// Remaining time until timeout
    time_left: f64,
    /// Whether timer is currently active
    active: bool,
    /// Whether timer is paused
    paused: bool,
    /// Timer mode (one-shot or repeating)
    mode: TimerMode,
    /// Whether timer starts automatically
    autostart: bool,
    /// Signal emitted when timer times out
    timeout_signal: Signal,
}

impl Timer {
    /// ### Creates a new Timer with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this timer node
    ///
    /// # Returns
    /// A new Timer instance with default timing properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Timer;
    /// let timer = Timer::new("GameTimer");
    /// assert_eq!(timer.get_name(), "GameTimer");
    /// assert_eq!(timer.get_wait_time(), 1.0);
    /// assert!(!timer.is_active());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        let node = Node::new(name);
        let node_id = node.get_id();
        Self {
            base: node,
            wait_time: 1.0,
            time_left: 0.0,
            active: false,
            paused: false,
            mode: TimerMode::OneShot,
            autostart: false,
            timeout_signal: Signal::new("timeout", node_id),
        }
    }

    /// ### Gets the wait time in seconds.
    ///
    /// # Returns
    /// The timer duration in seconds.
    #[inline]
    pub fn get_wait_time(&self) -> f64 {
        self.wait_time
    }

    /// ### Sets the wait time in seconds.
    ///
    /// # Parameters
    /// - `time`: The new timer duration in seconds
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Timer;
    /// let mut timer = Timer::new("Timer");
    /// timer.set_wait_time(3.5);
    /// assert_eq!(timer.get_wait_time(), 3.5);
    /// ```
    #[inline]
    pub fn set_wait_time(&mut self, time: f64) {
        self.wait_time = time.max(0.0);
    }

    /// ### Gets the remaining time until timeout.
    ///
    /// # Returns
    /// The time left in seconds, or 0.0 if timer is not active.
    #[inline]
    pub fn get_time_left(&self) -> f64 {
        if self.active && !self.paused {
            self.time_left
        } else {
            0.0
        }
    }

    /// ### Checks if the timer is currently active.
    ///
    /// # Returns
    /// True if the timer is running, false otherwise.
    #[inline]
    pub fn is_active(&self) -> bool {
        self.active
    }

    /// ### Checks if the timer is paused.
    ///
    /// # Returns
    /// True if the timer is paused, false otherwise.
    #[inline]
    pub fn is_paused(&self) -> bool {
        self.paused
    }

    /// ### Gets the timer mode.
    ///
    /// # Returns
    /// The current timer mode (OneShot or Repeating).
    #[inline]
    pub fn get_mode(&self) -> TimerMode {
        self.mode
    }

    /// ### Sets the timer mode.
    ///
    /// # Parameters
    /// - `mode`: The new timer mode
    #[inline]
    pub fn set_mode(&mut self, mode: TimerMode) {
        self.mode = mode;
    }

    /// ### Checks if timer is in one-shot mode.
    ///
    /// # Returns
    /// True if timer is one-shot, false if repeating.
    #[inline]
    pub fn is_one_shot(&self) -> bool {
        matches!(self.mode, TimerMode::OneShot)
    }

    /// ### Sets the one-shot mode.
    ///
    /// # Parameters
    /// - `one_shot`: Whether timer should be one-shot
    #[inline]
    pub fn set_one_shot(&mut self, one_shot: bool) {
        self.mode = if one_shot {
            TimerMode::OneShot
        } else {
            TimerMode::Repeating
        };
    }

    /// ### Checks if autostart is enabled.
    ///
    /// # Returns
    /// True if timer starts automatically, false otherwise.
    #[inline]
    pub fn is_autostart(&self) -> bool {
        self.autostart
    }

    /// ### Sets the autostart mode.
    ///
    /// # Parameters
    /// - `autostart`: Whether timer should start automatically
    #[inline]
    pub fn set_autostart(&mut self, autostart: bool) {
        self.autostart = autostart;
    }

    /// ### Gets the timeout signal.
    ///
    /// # Returns
    /// A reference to the timeout signal.
    #[inline]
    pub fn get_timeout_signal(&self) -> &Signal {
        &self.timeout_signal
    }

    /// ### Starts the timer.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for timeout signal registration
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Timer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut timer = Timer::new("Timer");
    /// let mut manager = SignalManager::new();
    /// timer.start(&mut manager);
    /// assert!(timer.is_active());
    /// ```
    #[inline]
    pub fn start(&mut self, _signal_manager: &mut SignalManager) {
        self.time_left = self.wait_time;
        self.active = true;
        self.paused = false;
    }

    /// ### Starts the timer with a custom duration.
    ///
    /// # Parameters
    /// - `time`: Custom duration in seconds
    /// - `signal_manager`: The signal manager for timeout signal registration
    #[inline]
    pub fn start_with_time(&mut self, time: f64, _signal_manager: &mut SignalManager) {
        self.time_left = time.max(0.0);
        self.active = true;
        self.paused = false;
    }

    /// ### Stops the timer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Timer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut timer = Timer::new("Timer");
    /// let mut manager = SignalManager::new();
    /// timer.start(&mut manager);
    /// timer.stop();
    /// assert!(!timer.is_active());
    /// ```
    #[inline]
    pub fn stop(&mut self) {
        self.active = false;
        self.paused = false;
        self.time_left = 0.0;
    }

    /// ### Pauses the timer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Timer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut timer = Timer::new("Timer");
    /// let mut manager = SignalManager::new();
    /// timer.start(&mut manager);
    /// timer.pause();
    /// assert!(timer.is_paused());
    /// ```
    #[inline]
    pub fn pause(&mut self) {
        if self.active {
            self.paused = true;
        }
    }

    /// ### Resumes the timer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Timer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut timer = Timer::new("Timer");
    /// let mut manager = SignalManager::new();
    /// timer.start(&mut manager);
    /// timer.pause();
    /// timer.resume();
    /// assert!(!timer.is_paused());
    /// ```
    #[inline]
    pub fn resume(&mut self) {
        if self.active {
            self.paused = false;
        }
    }

    /// ### Updates the timer with delta time.
    ///
    /// This should be called every frame to update the timer.
    ///
    /// # Parameters
    /// - `delta`: Time elapsed since last update in seconds
    /// - `signal_manager`: The signal manager for timeout signal emission
    ///
    /// # Returns
    /// True if the timer timed out this frame, false otherwise.
    #[inline]
    pub fn update(&mut self, delta: f64, signal_manager: &mut SignalManager) -> bool {
        if !self.active || self.paused {
            return false;
        }

        self.time_left -= delta;

        if self.time_left <= 0.0 {
            // Timer has timed out
            signal_manager.emit(self.timeout_signal.id(), SignalData::empty());

            match self.mode {
                TimerMode::OneShot => {
                    self.active = false;
                    self.time_left = 0.0;
                }
                TimerMode::Repeating => {
                    // Restart the timer
                    self.time_left = self.wait_time;
                }
            }

            true
        } else {
            false
        }
    }

    /// ### Provides access to the base Node functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node.
    #[inline]
    pub fn base(&self) -> &Node {
        &self.base
    }

    /// ### Provides mutable access to the base Node functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node {
        &mut self.base
    }

    /// ### Gets the node name from the base Node.
    ///
    /// # Returns
    /// The name of this timer node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.get_name()
    }
}

impl fmt::Display for Timer {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Timer({}, wait: {:.2}s, left: {:.2}s, active: {}, mode: {:?})",
               self.get_name(), self.wait_time, self.time_left, self.active, self.mode)
    }
}

impl PartialEq for Timer {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for Timer {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::signal::SignalManager;

    #[test]
    fn test_timer_creation() {
        let timer = Timer::new("TestTimer");
        assert_eq!(timer.get_name(), "TestTimer");
        assert_eq!(timer.get_wait_time(), 1.0);
        assert_eq!(timer.get_time_left(), 0.0);
        assert!(!timer.is_active());
        assert!(!timer.is_paused());
        assert!(timer.is_one_shot());
        assert!(!timer.is_autostart());
    }

    #[test]
    fn test_timer_wait_time() {
        let mut timer = Timer::new("Timer");

        // Initially 1.0 second
        assert_eq!(timer.get_wait_time(), 1.0);

        // Set custom wait time
        timer.set_wait_time(5.5);
        assert_eq!(timer.get_wait_time(), 5.5);

        // Test negative time (should be clamped to 0)
        timer.set_wait_time(-1.0);
        assert_eq!(timer.get_wait_time(), 0.0);
    }

    #[test]
    fn test_timer_mode() {
        let mut timer = Timer::new("Timer");

        // Initially one-shot
        assert_eq!(timer.get_mode(), TimerMode::OneShot);
        assert!(timer.is_one_shot());

        // Set repeating mode
        timer.set_mode(TimerMode::Repeating);
        assert_eq!(timer.get_mode(), TimerMode::Repeating);
        assert!(!timer.is_one_shot());

        // Set one-shot via boolean
        timer.set_one_shot(true);
        assert_eq!(timer.get_mode(), TimerMode::OneShot);
        assert!(timer.is_one_shot());

        // Set repeating via boolean
        timer.set_one_shot(false);
        assert_eq!(timer.get_mode(), TimerMode::Repeating);
        assert!(!timer.is_one_shot());
    }

    #[test]
    fn test_timer_autostart() {
        let mut timer = Timer::new("Timer");

        // Initially no autostart
        assert!(!timer.is_autostart());

        // Enable autostart
        timer.set_autostart(true);
        assert!(timer.is_autostart());

        // Disable autostart
        timer.set_autostart(false);
        assert!(!timer.is_autostart());
    }

    #[test]
    fn test_timer_start_stop() {
        let mut timer = Timer::new("Timer");
        let mut signal_manager = SignalManager::new();

        // Initially not active
        assert!(!timer.is_active());
        assert_eq!(timer.get_time_left(), 0.0);

        // Start timer
        timer.set_wait_time(3.0);
        timer.start(&mut signal_manager);
        assert!(timer.is_active());
        assert!(!timer.is_paused());
        assert_eq!(timer.get_time_left(), 3.0);

        // Stop timer
        timer.stop();
        assert!(!timer.is_active());
        assert!(!timer.is_paused());
        assert_eq!(timer.get_time_left(), 0.0);
    }

    #[test]
    fn test_timer_start_with_time() {
        let mut timer = Timer::new("Timer");
        let mut signal_manager = SignalManager::new();

        // Start with custom time
        timer.start_with_time(2.5, &mut signal_manager);
        assert!(timer.is_active());
        assert_eq!(timer.get_time_left(), 2.5);

        // Test negative time (should be clamped)
        timer.start_with_time(-1.0, &mut signal_manager);
        assert!(timer.is_active());
        assert_eq!(timer.get_time_left(), 0.0);
    }

    #[test]
    fn test_timer_pause_resume() {
        let mut timer = Timer::new("Timer");
        let mut signal_manager = SignalManager::new();

        // Start timer
        timer.set_wait_time(5.0);
        timer.start(&mut signal_manager);
        assert!(timer.is_active());
        assert!(!timer.is_paused());

        // Pause timer
        timer.pause();
        assert!(timer.is_active());
        assert!(timer.is_paused());
        assert_eq!(timer.get_time_left(), 0.0); // Should return 0 when paused

        // Resume timer
        timer.resume();
        assert!(timer.is_active());
        assert!(!timer.is_paused());
        assert_eq!(timer.get_time_left(), 5.0);

        // Test pause when not active (should not change state)
        timer.stop();
        timer.pause();
        assert!(!timer.is_active());
        assert!(!timer.is_paused());
    }

    #[test]
    fn test_timer_update_one_shot() {
        let mut timer = Timer::new("Timer");
        let mut signal_manager = SignalManager::new();

        // Register timeout signal
        signal_manager.register_signal(timer.get_timeout_signal().clone());

        // Set up one-shot timer
        timer.set_wait_time(2.0);
        timer.set_one_shot(true);
        timer.start(&mut signal_manager);

        // Update with partial time
        let timed_out = timer.update(1.0, &mut signal_manager);
        assert!(!timed_out);
        assert!(timer.is_active());
        assert_eq!(timer.get_time_left(), 1.0);

        // Update to timeout
        let timed_out = timer.update(1.5, &mut signal_manager);
        assert!(timed_out);
        assert!(!timer.is_active()); // Should stop after one-shot
        assert_eq!(timer.get_time_left(), 0.0);

        // Further updates should not trigger timeout
        let timed_out = timer.update(1.0, &mut signal_manager);
        assert!(!timed_out);
    }

    #[test]
    fn test_timer_update_repeating() {
        let mut timer = Timer::new("Timer");
        let mut signal_manager = SignalManager::new();

        // Register timeout signal
        signal_manager.register_signal(timer.get_timeout_signal().clone());

        // Set up repeating timer
        timer.set_wait_time(1.0);
        timer.set_one_shot(false);
        timer.start(&mut signal_manager);

        // First timeout
        let timed_out = timer.update(1.2, &mut signal_manager);
        assert!(timed_out);
        assert!(timer.is_active()); // Should remain active
        assert_eq!(timer.get_time_left(), 1.0); // Should restart

        // Second timeout
        let timed_out = timer.update(1.0, &mut signal_manager);
        assert!(timed_out);
        assert!(timer.is_active());
        assert_eq!(timer.get_time_left(), 1.0);
    }

    #[test]
    fn test_timer_update_paused() {
        let mut timer = Timer::new("Timer");
        let mut signal_manager = SignalManager::new();

        // Start and pause timer
        timer.set_wait_time(2.0);
        timer.start(&mut signal_manager);
        timer.pause();

        // Update while paused (should not progress)
        let timed_out = timer.update(5.0, &mut signal_manager);
        assert!(!timed_out);
        assert!(timer.is_active());
        assert!(timer.is_paused());

        // Resume and update
        timer.resume();
        let timed_out = timer.update(1.0, &mut signal_manager);
        assert!(!timed_out);
        assert_eq!(timer.get_time_left(), 1.0);
    }

    #[test]
    fn test_timer_timeout_signal() {
        let timer = Timer::new("Timer");
        let timeout_signal = timer.get_timeout_signal();

        assert_eq!(timeout_signal.name(), "timeout");
        assert_eq!(timeout_signal.owner(), timer.base().get_id());
    }

    #[test]
    fn test_timer_base_access() {
        let mut timer = Timer::new("Timer");

        // Test base access
        assert_eq!(timer.base().get_name(), "Timer");

        // Test mutable base access
        timer.base_mut().set_name("NewTimer");
        assert_eq!(timer.get_name(), "NewTimer");
    }

    #[test]
    fn test_timer_equality() {
        let timer1 = Timer::new("Timer1");
        let timer2 = Timer::new("Timer2");
        let timer1_clone = timer1.clone();

        // Same timer should be equal
        assert_eq!(timer1, timer1_clone);

        // Different timers should not be equal
        assert_ne!(timer1, timer2);
    }

    #[test]
    fn test_timer_display() {
        let mut timer = Timer::new("TestTimer");
        timer.set_wait_time(3.5);
        timer.set_mode(TimerMode::Repeating);

        let display_str = format!("{}", timer);
        assert!(display_str.contains("TestTimer"));
        assert!(display_str.contains("wait: 3.50s"));
        assert!(display_str.contains("active: false"));
        assert!(display_str.contains("Repeating"));
    }

    #[test]
    fn test_timer_complex_scenario() {
        let mut timer = Timer::new("GameTimer");
        let mut signal_manager = SignalManager::new();

        // Configure timer
        timer.set_wait_time(1.5);
        timer.set_one_shot(false);
        timer.set_autostart(true);

        // Register signal
        signal_manager.register_signal(timer.get_timeout_signal().clone());

        // Start timer
        timer.start(&mut signal_manager);

        // Simulate multiple timeouts
        let mut timeout_count = 0;
        for _ in 0..5 {
            if timer.update(1.6, &mut signal_manager) {
                timeout_count += 1;
            }
        }

        assert_eq!(timeout_count, 5);
        assert!(timer.is_active()); // Should still be active (repeating)

        // Pause and verify no more timeouts
        timer.pause();
        let timed_out = timer.update(10.0, &mut signal_manager);
        assert!(!timed_out);

        // Resume and get one more timeout
        timer.resume();
        let timed_out = timer.update(2.0, &mut signal_manager);
        assert!(timed_out);
    }
}
