//! Node3D implementation for 3D spatial nodes with transform capabilities.
//!
//! This module provides the Node3D class that extends the base Node with 3D spatial
//! transformation capabilities including position, rotation, and scale in 3D space.
//! It maintains compatibility with Godot's Node3D class.

use std::fmt;
use crate::core::math::{Vector3, Basis, Quaternion};
use crate::core::scene::Node;

/// ### 3D spatial node with transformation capabilities.
///
/// Node3D extends the base Node class with 3D spatial transformation support,
/// providing position, rotation, and scale properties for 3D scene management.
/// It maintains compatibility with <PERSON><PERSON>'s Node3D class while ensuring
/// efficient transformation calculations.
///
/// ## Core Features
///
/// - **3D Transformation**: Position, rotation, and scale in 3D space
/// - **Basis Matrix**: 3D rotation and scale representation
/// - **Quaternion Support**: Smooth rotation interpolation
/// - **Godot Compatibility**: API matching <PERSON><PERSON>'s Node3D class
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::Node3D;
/// # use verturion::core::math::Vector3;
/// // Create a 3D node
/// let mut mesh = Node3D::new("MeshInstance");
///
/// // Set transformation properties
/// mesh.set_position(Vector3::new(1.0, 2.0, 3.0));
/// mesh.set_scale(Vector3::new(2.0, 2.0, 2.0));
///
/// assert_eq!(mesh.get_position(), Vector3::new(1.0, 2.0, 3.0));
/// ```
#[derive(Debug, Clone)]
pub struct Node3D {
    /// Base node functionality
    base: Node,
    /// Local position in 3D space
    position: Vector3,
    /// Rotation basis matrix
    basis: Basis,
    /// Scale factors for X, Y, and Z axes
    scale: Vector3,
}

impl Node3D {
    /// ### Creates a new Node3D with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this node
    ///
    /// # Returns
    /// A new Node3D instance with identity transform.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node3D;
    /// let node = Node3D::new("Player");
    /// assert_eq!(node.get_name(), "Player");
    /// assert_eq!(node.get_position(), Vector3::ZERO);
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            base: Node::new(name),
            position: Vector3::ZERO,
            basis: Basis::IDENTITY,
            scale: Vector3::ONE,
        }
    }

    /// ### Gets the local position of this node.
    ///
    /// # Returns
    /// The current position as a Vector3.
    #[inline]
    pub fn get_position(&self) -> Vector3 {
        self.position
    }

    /// ### Sets the local position of this node.
    ///
    /// # Parameters
    /// - `position`: The new position as a Vector3
    #[inline]
    pub fn set_position(&mut self, position: Vector3) {
        self.position = position;
    }

    /// ### Gets the rotation basis matrix.
    ///
    /// # Returns
    /// The current basis matrix.
    #[inline]
    pub fn get_basis(&self) -> Basis {
        self.basis
    }

    /// ### Sets the rotation basis matrix.
    ///
    /// # Parameters
    /// - `basis`: The new basis matrix
    #[inline]
    pub fn set_basis(&mut self, basis: Basis) {
        self.basis = basis;
    }

    /// ### Gets the scale factors.
    ///
    /// # Returns
    /// The current scale as a Vector3.
    #[inline]
    pub fn get_scale(&self) -> Vector3 {
        self.scale
    }

    /// ### Sets the scale factors.
    ///
    /// # Parameters
    /// - `scale`: The new scale as a Vector3
    #[inline]
    pub fn set_scale(&mut self, scale: Vector3) {
        self.scale = scale;
    }

    /// ### Sets rotation from Euler angles.
    ///
    /// # Parameters
    /// - `euler`: Euler angles in radians (x, y, z)
    #[inline]
    pub fn set_rotation(&mut self, euler: Vector3) {
        self.basis = Basis::from_euler(euler);
    }

    /// ### Sets rotation from a quaternion.
    ///
    /// # Parameters
    /// - `quaternion`: The rotation quaternion
    #[inline]
    pub fn set_quaternion(&mut self, quaternion: Quaternion) {
        self.basis = Basis::from_quaternion(quaternion);
    }

    /// ### Translates the node by the given offset.
    ///
    /// # Parameters
    /// - `offset`: The translation offset as a Vector3
    #[inline]
    pub fn translate(&mut self, offset: Vector3) {
        self.position = self.position + offset;
    }

    /// ### Provides access to the base Node functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node.
    #[inline]
    pub fn base(&self) -> &Node {
        &self.base
    }

    /// ### Provides mutable access to the base Node functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node {
        &mut self.base
    }

    /// ### Gets the node name from the base Node.
    ///
    /// # Returns
    /// The name of this node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.get_name()
    }
}

impl fmt::Display for Node3D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Node3D({}, pos: {}, scale: {})",
               self.base.get_name(), self.position, self.scale)
    }
}

impl PartialEq for Node3D {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for Node3D {}
