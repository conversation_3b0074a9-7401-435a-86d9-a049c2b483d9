//! AudioStreamPlayer2D implementation for 2D spatial audio.
//!
//! This module provides the AudioStreamPlayer2D class that extends Node2D with
//! spatial audio functionality including distance attenuation, stereo panning,
//! audio bus routing, and signal emission. It maintains full compatibility
//! with Godot's AudioStreamPlayer2D class while providing efficient audio management.

use std::fmt;
use crate::core::scene::nodes::Node2D;
use crate::core::math::Vector2;
use crate::core::signal::{Signal, SignalManager, SignalData};

/// ### Audio attenuation models for distance-based volume control.
///
/// Defines how audio volume decreases with distance from the listener.
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq)]
pub enum AttenuationModel {
    /// No distance attenuation
    Disabled,
    /// Inverse distance attenuation (realistic)
    InverseDistance,
    /// Inverse square distance attenuation (more realistic)
    InverseSquareDistance,
    /// Logarithmic distance attenuation
    Logarithmic,
}

/// ### AudioStreamPlayer2D for 2D spatial audio playback.
///
/// AudioStreamPlayer2D extends Node2D with comprehensive spatial audio
/// functionality, providing distance-based attenuation, stereo panning,
/// audio bus routing, and signal emission for audio events. It maintains
/// full compatibility with Godot's AudioStreamPlayer2D class while ensuring
/// efficient audio processing and spatial positioning.
///
/// ## Core Features
///
/// - **Spatial Audio**: Position-based audio with distance attenuation
/// - **Stereo Panning**: Automatic left/right channel balancing
/// - **Audio Bus Routing**: Route audio through specific buses
/// - **Volume Control**: Master volume and attenuation settings
/// - **Pitch Control**: Real-time pitch adjustment
/// - **Signal Integration**: Audio event signal emission
/// - **Godot Compatibility**: API matching Godot's AudioStreamPlayer2D
///
/// ## Audio Properties
///
/// AudioStreamPlayer2D provides comprehensive audio control:
/// - **Stream**: Audio resource to play
/// - **Volume**: Master volume in decibels
/// - **Pitch**: Playback speed multiplier
/// - **Playing**: Current playback state
/// - **Autoplay**: Whether to start playing automatically
/// - **Max Distance**: Maximum audible distance
/// - **Attenuation**: Distance-based volume reduction model
/// - **Audio Bus**: Target audio bus for routing
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
/// # use verturion::core::math::Vector2;
/// # use verturion::core::signal::SignalManager;
/// // Create an audio player
/// let mut player = AudioStreamPlayer2D::new("SoundEffect");
/// let mut signal_manager = SignalManager::new();
///
/// // Configure audio
/// player.set_volume_db(-10.0); // Reduce volume
/// player.set_pitch_scale(1.2); // Increase pitch
/// player.set_max_distance(500.0);
/// player.set_position(Vector2::new(100.0, 50.0));
///
/// // Register signals
/// signal_manager.register_signal(player.get_finished_signal().clone());
///
/// // Play audio
/// player.play(&mut signal_manager);
///
/// assert!(player.is_playing());
/// assert_eq!(player.get_volume_db(), -10.0);
/// ```
#[derive(Debug, Clone)]
pub struct AudioStreamPlayer2D {
    /// Base Node2D functionality
    base: Node2D,
    /// Audio stream resource path
    stream: Option<String>,
    /// Volume in decibels (-80 to 24)
    volume_db: f32,
    /// Pitch scale multiplier (0.01 to 4.0)
    pitch_scale: f32,
    /// Whether audio is currently playing
    playing: bool,
    /// Whether to start playing automatically
    autoplay: bool,
    /// Maximum audible distance
    max_distance: f32,
    /// Distance attenuation model
    attenuation: AttenuationModel,
    /// Audio bus name for routing
    bus: String,
    /// Current playback position in seconds
    playback_position: f32,
    /// Stream length in seconds (if known)
    stream_length: f32,
    /// Signal emitted when playback finishes
    finished_signal: Signal,
}

impl AudioStreamPlayer2D {
    /// ### Creates a new AudioStreamPlayer2D with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this audio player node
    ///
    /// # Returns
    /// A new AudioStreamPlayer2D instance with default audio properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
    /// let player = AudioStreamPlayer2D::new("BackgroundMusic");
    /// assert_eq!(player.get_name(), "BackgroundMusic");
    /// assert_eq!(player.get_volume_db(), 0.0);
    /// assert!(!player.is_playing());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        let node2d = Node2D::new(name);
        let node_id = node2d.base().get_id();
        Self {
            base: node2d,
            stream: None,
            volume_db: 0.0,
            pitch_scale: 1.0,
            playing: false,
            autoplay: false,
            max_distance: 2000.0,
            attenuation: AttenuationModel::InverseDistance,
            bus: "Master".to_string(),
            playback_position: 0.0,
            stream_length: 0.0,
            finished_signal: Signal::new("finished", node_id),
        }
    }

    /// ### Gets the audio stream path.
    ///
    /// # Returns
    /// The current audio stream path, or None if no stream is set.
    #[inline]
    pub fn get_stream(&self) -> Option<&String> {
        self.stream.as_ref()
    }

    /// ### Sets the audio stream.
    ///
    /// # Parameters
    /// - `stream`: The audio stream path, or None to clear the stream
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
    /// let mut player = AudioStreamPlayer2D::new("Player");
    /// player.set_stream(Some("res://audio/music.ogg".to_string()));
    /// assert_eq!(player.get_stream(), Some(&"res://audio/music.ogg".to_string()));
    /// ```
    #[inline]
    pub fn set_stream(&mut self, stream: Option<String>) {
        let has_stream = stream.is_some();
        self.stream = stream;
        if has_stream {
            // In a real implementation, we would load the stream and get its length
            self.stream_length = 10.0; // Placeholder length
        } else {
            self.stream_length = 0.0;
        }
    }

    /// ### Gets the volume in decibels.
    ///
    /// # Returns
    /// The current volume in decibels (-80 to 24).
    #[inline]
    pub fn get_volume_db(&self) -> f32 {
        self.volume_db
    }

    /// ### Sets the volume in decibels.
    ///
    /// # Parameters
    /// - `volume_db`: The new volume in decibels (-80 to 24)
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
    /// let mut player = AudioStreamPlayer2D::new("Player");
    /// player.set_volume_db(-12.0);
    /// assert_eq!(player.get_volume_db(), -12.0);
    /// ```
    #[inline]
    pub fn set_volume_db(&mut self, volume_db: f32) {
        self.volume_db = volume_db.clamp(-80.0, 24.0);
    }

    /// ### Gets the pitch scale.
    ///
    /// # Returns
    /// The current pitch scale multiplier (0.01 to 4.0).
    #[inline]
    pub fn get_pitch_scale(&self) -> f32 {
        self.pitch_scale
    }

    /// ### Sets the pitch scale.
    ///
    /// # Parameters
    /// - `pitch_scale`: The new pitch scale multiplier (0.01 to 4.0)
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
    /// let mut player = AudioStreamPlayer2D::new("Player");
    /// player.set_pitch_scale(1.5);
    /// assert_eq!(player.get_pitch_scale(), 1.5);
    /// ```
    #[inline]
    pub fn set_pitch_scale(&mut self, pitch_scale: f32) {
        self.pitch_scale = pitch_scale.clamp(0.01, 4.0);
    }

    /// ### Checks if audio is currently playing.
    ///
    /// # Returns
    /// True if audio is playing, false otherwise.
    #[inline]
    pub fn is_playing(&self) -> bool {
        self.playing
    }

    /// ### Checks if autoplay is enabled.
    ///
    /// # Returns
    /// True if autoplay is enabled, false otherwise.
    #[inline]
    pub fn is_autoplay(&self) -> bool {
        self.autoplay
    }

    /// ### Sets the autoplay mode.
    ///
    /// # Parameters
    /// - `autoplay`: Whether to start playing automatically
    #[inline]
    pub fn set_autoplay(&mut self, autoplay: bool) {
        self.autoplay = autoplay;
    }

    /// ### Gets the maximum audible distance.
    ///
    /// # Returns
    /// The maximum distance at which audio is audible.
    #[inline]
    pub fn get_max_distance(&self) -> f32 {
        self.max_distance
    }

    /// ### Sets the maximum audible distance.
    ///
    /// # Parameters
    /// - `distance`: The new maximum audible distance
    #[inline]
    pub fn set_max_distance(&mut self, distance: f32) {
        self.max_distance = distance.max(0.0);
    }

    /// ### Gets the attenuation model.
    ///
    /// # Returns
    /// The current distance attenuation model.
    #[inline]
    pub fn get_attenuation(&self) -> AttenuationModel {
        self.attenuation
    }

    /// ### Sets the attenuation model.
    ///
    /// # Parameters
    /// - `attenuation`: The new distance attenuation model
    #[inline]
    pub fn set_attenuation(&mut self, attenuation: AttenuationModel) {
        self.attenuation = attenuation;
    }

    /// ### Gets the audio bus name.
    ///
    /// # Returns
    /// The current audio bus name for routing.
    #[inline]
    pub fn get_bus(&self) -> &str {
        &self.bus
    }

    /// ### Sets the audio bus.
    ///
    /// # Parameters
    /// - `bus`: The new audio bus name for routing
    #[inline]
    pub fn set_bus(&mut self, bus: String) {
        self.bus = bus;
    }

    /// ### Gets the current playback position.
    ///
    /// # Returns
    /// The current playback position in seconds.
    #[inline]
    pub fn get_playback_position(&self) -> f32 {
        self.playback_position
    }

    /// ### Sets the playback position.
    ///
    /// # Parameters
    /// - `position`: The new playback position in seconds
    #[inline]
    pub fn set_playback_position(&mut self, position: f32) {
        self.playback_position = position.clamp(0.0, self.stream_length);
    }

    /// ### Gets the stream length.
    ///
    /// # Returns
    /// The total length of the audio stream in seconds.
    #[inline]
    pub fn get_stream_length(&self) -> f32 {
        self.stream_length
    }

    /// ### Gets the finished signal.
    ///
    /// # Returns
    /// A reference to the finished signal.
    #[inline]
    pub fn get_finished_signal(&self) -> &Signal {
        &self.finished_signal
    }

    /// ### Starts audio playback.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal registration
    /// - `from_position`: Optional starting position in seconds
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
    /// # use verturion::core::signal::SignalManager;
    /// let mut player = AudioStreamPlayer2D::new("Player");
    /// let mut manager = SignalManager::new();
    /// player.set_stream(Some("res://audio/sound.wav".to_string()));
    /// player.play(&mut manager);
    /// assert!(player.is_playing());
    /// ```
    #[inline]
    pub fn play(&mut self, _signal_manager: &mut SignalManager) {
        if self.stream.is_some() {
            self.playing = true;
            self.playback_position = 0.0;
        }
    }

    /// ### Starts audio playback from a specific position.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal registration
    /// - `from_position`: Starting position in seconds
    #[inline]
    pub fn play_from_position(&mut self, _signal_manager: &mut SignalManager, from_position: f32) {
        if self.stream.is_some() {
            self.playing = true;
            self.set_playback_position(from_position);
        }
    }

    /// ### Stops audio playback.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
    /// # use verturion::core::signal::SignalManager;
    /// let mut player = AudioStreamPlayer2D::new("Player");
    /// let mut manager = SignalManager::new();
    /// player.play(&mut manager);
    /// player.stop();
    /// assert!(!player.is_playing());
    /// ```
    #[inline]
    pub fn stop(&mut self) {
        self.playing = false;
        self.playback_position = 0.0;
    }

    /// ### Pauses audio playback.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
    /// # use verturion::core::signal::SignalManager;
    /// let mut player = AudioStreamPlayer2D::new("Player");
    /// let mut manager = SignalManager::new();
    /// player.play(&mut manager);
    /// player.pause();
    /// assert!(!player.is_playing());
    /// ```
    #[inline]
    pub fn pause(&mut self) {
        self.playing = false;
    }

    /// ### Resumes audio playback.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
    /// # use verturion::core::signal::SignalManager;
    /// let mut player = AudioStreamPlayer2D::new("Player");
    /// let mut manager = SignalManager::new();
    /// player.set_stream(Some("res://audio/sound.wav".to_string()));
    /// player.play(&mut manager);
    /// player.pause();
    /// player.resume(&mut manager);
    /// assert!(player.is_playing());
    /// ```
    #[inline]
    pub fn resume(&mut self, _signal_manager: &mut SignalManager) {
        if self.stream.is_some() {
            self.playing = true;
        }
    }

    /// ### Updates the audio player with delta time.
    ///
    /// This should be called every frame to update playback position and
    /// emit finished signals when audio completes.
    ///
    /// # Parameters
    /// - `delta`: Time elapsed since last update in seconds
    /// - `signal_manager`: The signal manager for finished signal emission
    ///
    /// # Returns
    /// True if the audio finished this frame, false otherwise.
    #[inline]
    pub fn update(&mut self, delta: f32, signal_manager: &mut SignalManager) -> bool {
        if !self.playing || self.stream.is_none() {
            return false;
        }

        // Update playback position
        self.playback_position += delta * self.pitch_scale;

        // Check if audio has finished
        if self.playback_position >= self.stream_length {
            self.playing = false;
            self.playback_position = self.stream_length;

            // Emit finished signal
            signal_manager.emit(self.finished_signal.id(), SignalData::empty());
            true
        } else {
            false
        }
    }

    /// ### Calculates volume attenuation based on distance to listener.
    ///
    /// # Parameters
    /// - `listener_position`: Position of the audio listener
    ///
    /// # Returns
    /// Volume multiplier (0.0 to 1.0) based on distance and attenuation model.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::AudioStreamPlayer2D;
    /// # use verturion::core::math::Vector2;
    /// let mut player = AudioStreamPlayer2D::new("Player");
    /// player.set_position(Vector2::new(100.0, 0.0));
    /// player.set_max_distance(200.0);
    ///
    /// let listener_pos = Vector2::new(0.0, 0.0);
    /// let attenuation = player.calculate_attenuation(listener_pos);
    /// assert!(attenuation > 0.0 && attenuation <= 1.0);
    /// ```
    #[inline]
    pub fn calculate_attenuation(&self, listener_position: Vector2) -> f32 {
        let distance = self.get_position().distance_to(listener_position);

        if distance >= self.max_distance {
            return 0.0;
        }

        match self.attenuation {
            AttenuationModel::Disabled => 1.0,
            AttenuationModel::InverseDistance => {
                if distance <= 1.0 {
                    1.0
                } else {
                    1.0 / distance
                }
            }
            AttenuationModel::InverseSquareDistance => {
                if distance <= 1.0 {
                    1.0
                } else {
                    1.0 / (distance * distance)
                }
            }
            AttenuationModel::Logarithmic => {
                if distance <= 1.0 {
                    1.0
                } else {
                    1.0 - (distance.ln() / self.max_distance.ln())
                }
            }
        }
    }

    /// ### Calculates stereo panning based on position relative to listener.
    ///
    /// # Parameters
    /// - `listener_position`: Position of the audio listener
    ///
    /// # Returns
    /// Panning value (-1.0 = full left, 0.0 = center, 1.0 = full right).
    #[inline]
    pub fn calculate_panning(&self, listener_position: Vector2) -> f32 {
        let relative_pos = self.get_position() - listener_position;

        // Simple panning based on X offset
        let max_pan_distance = self.max_distance * 0.5;
        if max_pan_distance <= 0.0 {
            return 0.0;
        }

        (relative_pos.x / max_pan_distance).clamp(-1.0, 1.0)
    }

    /// ### Provides access to the base Node2D functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node2D.
    #[inline]
    pub fn base(&self) -> &Node2D {
        &self.base
    }

    /// ### Provides mutable access to the base Node2D functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node2D.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node2D {
        &mut self.base
    }

    /// ### Gets the node name from the base Node2D.
    ///
    /// # Returns
    /// The name of this audio player node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }

    /// ### Gets the position from the base Node2D.
    ///
    /// # Returns
    /// The current position of the audio source.
    #[inline]
    pub fn get_position(&self) -> Vector2 {
        self.base.get_position()
    }

    /// ### Sets the position in the base Node2D.
    ///
    /// # Parameters
    /// - `position`: The new position for the audio source
    #[inline]
    pub fn set_position(&mut self, position: Vector2) {
        self.base.set_position(position);
    }
}

impl fmt::Display for AudioStreamPlayer2D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "AudioStreamPlayer2D({}, stream: {:?}, volume: {:.1}dB, playing: {})",
               self.get_name(),
               self.stream.as_ref().map(|s| s.as_str()).unwrap_or("None"),
               self.volume_db,
               self.playing)
    }
}

impl PartialEq for AudioStreamPlayer2D {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for AudioStreamPlayer2D {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::signal::SignalManager;

    #[test]
    fn test_audio_player_creation() {
        let player = AudioStreamPlayer2D::new("TestAudio");
        assert_eq!(player.get_name(), "TestAudio");
        assert_eq!(player.get_stream(), None);
        assert_eq!(player.get_volume_db(), 0.0);
        assert_eq!(player.get_pitch_scale(), 1.0);
        assert!(!player.is_playing());
        assert!(!player.is_autoplay());
        assert_eq!(player.get_max_distance(), 2000.0);
        assert_eq!(player.get_attenuation(), AttenuationModel::InverseDistance);
        assert_eq!(player.get_bus(), "Master");
    }

    #[test]
    fn test_audio_stream_management() {
        let mut player = AudioStreamPlayer2D::new("Player");

        // Initially no stream
        assert_eq!(player.get_stream(), None);
        assert_eq!(player.get_stream_length(), 0.0);

        // Set stream
        player.set_stream(Some("res://audio/music.ogg".to_string()));
        assert_eq!(player.get_stream(), Some(&"res://audio/music.ogg".to_string()));
        assert_eq!(player.get_stream_length(), 10.0); // Placeholder length

        // Clear stream
        player.set_stream(None);
        assert_eq!(player.get_stream(), None);
        assert_eq!(player.get_stream_length(), 0.0);
    }

    #[test]
    fn test_volume_control() {
        let mut player = AudioStreamPlayer2D::new("Player");

        // Initially 0 dB
        assert_eq!(player.get_volume_db(), 0.0);

        // Set valid volume
        player.set_volume_db(-12.5);
        assert_eq!(player.get_volume_db(), -12.5);

        // Test clamping (too low)
        player.set_volume_db(-100.0);
        assert_eq!(player.get_volume_db(), -80.0);

        // Test clamping (too high)
        player.set_volume_db(50.0);
        assert_eq!(player.get_volume_db(), 24.0);
    }

    #[test]
    fn test_pitch_control() {
        let mut player = AudioStreamPlayer2D::new("Player");

        // Initially 1.0
        assert_eq!(player.get_pitch_scale(), 1.0);

        // Set valid pitch
        player.set_pitch_scale(1.5);
        assert_eq!(player.get_pitch_scale(), 1.5);

        // Test clamping (too low)
        player.set_pitch_scale(0.005);
        assert_eq!(player.get_pitch_scale(), 0.01);

        // Test clamping (too high)
        player.set_pitch_scale(10.0);
        assert_eq!(player.get_pitch_scale(), 4.0);
    }

    #[test]
    fn test_autoplay() {
        let mut player = AudioStreamPlayer2D::new("Player");

        // Initially disabled
        assert!(!player.is_autoplay());

        // Enable autoplay
        player.set_autoplay(true);
        assert!(player.is_autoplay());

        // Disable autoplay
        player.set_autoplay(false);
        assert!(!player.is_autoplay());
    }

    #[test]
    fn test_distance_settings() {
        let mut player = AudioStreamPlayer2D::new("Player");

        // Initially 2000.0
        assert_eq!(player.get_max_distance(), 2000.0);

        // Set custom distance
        player.set_max_distance(500.0);
        assert_eq!(player.get_max_distance(), 500.0);

        // Test negative distance (should be clamped to 0)
        player.set_max_distance(-100.0);
        assert_eq!(player.get_max_distance(), 0.0);
    }

    #[test]
    fn test_attenuation_model() {
        let mut player = AudioStreamPlayer2D::new("Player");

        // Initially InverseDistance
        assert_eq!(player.get_attenuation(), AttenuationModel::InverseDistance);

        // Test all attenuation models
        player.set_attenuation(AttenuationModel::Disabled);
        assert_eq!(player.get_attenuation(), AttenuationModel::Disabled);

        player.set_attenuation(AttenuationModel::InverseSquareDistance);
        assert_eq!(player.get_attenuation(), AttenuationModel::InverseSquareDistance);

        player.set_attenuation(AttenuationModel::Logarithmic);
        assert_eq!(player.get_attenuation(), AttenuationModel::Logarithmic);
    }

    #[test]
    fn test_bus_routing() {
        let mut player = AudioStreamPlayer2D::new("Player");

        // Initially "Master"
        assert_eq!(player.get_bus(), "Master");

        // Set custom bus
        player.set_bus("SFX".to_string());
        assert_eq!(player.get_bus(), "SFX");

        // Set another bus
        player.set_bus("Music".to_string());
        assert_eq!(player.get_bus(), "Music");
    }

    #[test]
    fn test_playback_position() {
        let mut player = AudioStreamPlayer2D::new("Player");
        player.set_stream(Some("res://audio/test.wav".to_string()));

        // Initially 0.0
        assert_eq!(player.get_playback_position(), 0.0);

        // Set valid position
        player.set_playback_position(5.0);
        assert_eq!(player.get_playback_position(), 5.0);

        // Test clamping (beyond stream length)
        player.set_playback_position(20.0);
        assert_eq!(player.get_playback_position(), 10.0); // Stream length is 10.0

        // Test negative position (should be clamped to 0)
        player.set_playback_position(-5.0);
        assert_eq!(player.get_playback_position(), 0.0);
    }

    #[test]
    fn test_playback_control() {
        let mut player = AudioStreamPlayer2D::new("Player");
        let mut signal_manager = SignalManager::new();

        // Initially not playing
        assert!(!player.is_playing());

        // Cannot play without stream
        player.play(&mut signal_manager);
        assert!(!player.is_playing());

        // Set stream and play
        player.set_stream(Some("res://audio/test.wav".to_string()));
        player.play(&mut signal_manager);
        assert!(player.is_playing());
        assert_eq!(player.get_playback_position(), 0.0);

        // Pause
        player.pause();
        assert!(!player.is_playing());

        // Resume
        player.resume(&mut signal_manager);
        assert!(player.is_playing());

        // Stop
        player.stop();
        assert!(!player.is_playing());
        assert_eq!(player.get_playback_position(), 0.0);
    }

    #[test]
    fn test_play_from_position() {
        let mut player = AudioStreamPlayer2D::new("Player");
        let mut signal_manager = SignalManager::new();

        player.set_stream(Some("res://audio/test.wav".to_string()));

        // Play from specific position
        player.play_from_position(&mut signal_manager, 3.0);
        assert!(player.is_playing());
        assert_eq!(player.get_playback_position(), 3.0);

        // Play from position beyond stream length (should be clamped)
        player.play_from_position(&mut signal_manager, 15.0);
        assert!(player.is_playing());
        assert_eq!(player.get_playback_position(), 10.0);
    }

    #[test]
    fn test_update_and_finished_signal() {
        let mut player = AudioStreamPlayer2D::new("Player");
        let mut signal_manager = SignalManager::new();

        // Register finished signal
        signal_manager.register_signal(player.get_finished_signal().clone());

        player.set_stream(Some("res://audio/test.wav".to_string()));
        player.play(&mut signal_manager);

        // Update with partial time
        let finished = player.update(5.0, &mut signal_manager);
        assert!(!finished);
        assert!(player.is_playing());
        assert_eq!(player.get_playback_position(), 5.0);

        // Update to finish
        let finished = player.update(6.0, &mut signal_manager);
        assert!(finished);
        assert!(!player.is_playing());
        assert_eq!(player.get_playback_position(), 10.0);

        // Further updates should not trigger finished
        let finished = player.update(1.0, &mut signal_manager);
        assert!(!finished);
    }

    #[test]
    fn test_update_with_pitch() {
        let mut player = AudioStreamPlayer2D::new("Player");
        let mut signal_manager = SignalManager::new();

        player.set_stream(Some("res://audio/test.wav".to_string()));
        player.set_pitch_scale(2.0); // Double speed
        player.play(&mut signal_manager);

        // Update with pitch scaling
        let finished = player.update(3.0, &mut signal_manager);
        assert!(!finished);
        assert_eq!(player.get_playback_position(), 6.0); // 3.0 * 2.0 pitch

        // Should finish sooner due to higher pitch
        let finished = player.update(2.5, &mut signal_manager);
        assert!(finished);
        assert!(!player.is_playing());
    }

    #[test]
    fn test_update_not_playing() {
        let mut player = AudioStreamPlayer2D::new("Player");
        let mut signal_manager = SignalManager::new();

        player.set_stream(Some("res://audio/test.wav".to_string()));

        // Update when not playing (should not progress)
        let finished = player.update(5.0, &mut signal_manager);
        assert!(!finished);
        assert_eq!(player.get_playback_position(), 0.0);
    }

    #[test]
    fn test_attenuation_calculation() {
        let mut player = AudioStreamPlayer2D::new("Player");
        player.set_position(Vector2::new(100.0, 0.0));
        player.set_max_distance(200.0);

        let listener_pos = Vector2::new(0.0, 0.0);

        // Test disabled attenuation
        player.set_attenuation(AttenuationModel::Disabled);
        assert_eq!(player.calculate_attenuation(listener_pos), 1.0);

        // Test inverse distance
        player.set_attenuation(AttenuationModel::InverseDistance);
        let attenuation = player.calculate_attenuation(listener_pos);
        assert!((attenuation - 1.0 / 100.0).abs() < 0.001);

        // Test beyond max distance (should be 0)
        let far_listener = Vector2::new(300.0, 0.0);
        assert_eq!(player.calculate_attenuation(far_listener), 0.0);
    }

    #[test]
    fn test_panning_calculation() {
        let mut player = AudioStreamPlayer2D::new("Player");
        player.set_max_distance(200.0);

        let listener_pos = Vector2::new(0.0, 0.0);

        // Test center position (no panning)
        player.set_position(Vector2::new(0.0, 0.0));
        assert_eq!(player.calculate_panning(listener_pos), 0.0);

        // Test right position (positive panning)
        player.set_position(Vector2::new(50.0, 0.0));
        let panning = player.calculate_panning(listener_pos);
        assert!(panning > 0.0 && panning <= 1.0);

        // Test left position (negative panning)
        player.set_position(Vector2::new(-50.0, 0.0));
        let panning = player.calculate_panning(listener_pos);
        assert!(panning < 0.0 && panning >= -1.0);
    }

    #[test]
    fn test_finished_signal() {
        let player = AudioStreamPlayer2D::new("Player");
        let finished_signal = player.get_finished_signal();

        assert_eq!(finished_signal.name(), "finished");
        assert_eq!(finished_signal.owner(), player.base().base().get_id());
    }

    #[test]
    fn test_equality() {
        let player1 = AudioStreamPlayer2D::new("Player1");
        let player2 = AudioStreamPlayer2D::new("Player2");
        let player1_clone = player1.clone();

        // Same player should be equal
        assert_eq!(player1, player1_clone);

        // Different players should not be equal
        assert_ne!(player1, player2);
    }

    #[test]
    fn test_display() {
        let mut player = AudioStreamPlayer2D::new("TestPlayer");
        player.set_stream(Some("res://audio/test.ogg".to_string()));
        player.set_volume_db(-6.5);

        let display_str = format!("{}", player);
        assert!(display_str.contains("TestPlayer"));
        assert!(display_str.contains("res://audio/test.ogg"));
        assert!(display_str.contains("-6.5dB"));
        assert!(display_str.contains("playing: false"));
    }
}
