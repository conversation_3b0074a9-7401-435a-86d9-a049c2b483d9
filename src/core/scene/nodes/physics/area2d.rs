//! Area2D implementation for 2D area detection and trigger zones.
//!
//! This module provides the Area2D class that extends Node2D with area detection
//! functionality for trigger zones, sensors, and area-based interactions. It maintains
//! full compatibility with <PERSON>ot's Area2D class while providing efficient area
//! detection and signal emission for game development.

use std::fmt;
use std::collections::HashSet;
use crate::core::math::Vector2;
use crate::core::variant::Color;
use crate::core::scene::nodes::Node2D;

/// ### Area2D space override modes for physics influence.
///
/// Defines how the area affects physics bodies within it.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum SpaceOverride {
    /// No physics override
    Disabled,
    /// Combine with default physics
    Combine,
    /// Replace default physics
    Replace,
    /// Combine and replace default physics
    CombineReplace,
}

/// ### 2D area detection for trigger zones and sensors.
///
/// Area2D extends Node2D with comprehensive area detection functionality,
/// providing trigger zones, sensor areas, physics influence, and signal emission
/// for area-based interactions. It maintains full compatibility with <PERSON><PERSON>'s
/// Area2D class while ensuring efficient area detection and event handling.
///
/// ## Core Features
///
/// - **Area Detection**: Detect when bodies enter/exit the area
/// - **Trigger Zones**: Create interactive trigger areas
/// - **Physics Influence**: Modify gravity and damping within the area
/// - **Signal Emission**: Emit signals for area events
/// - **Collision Layers**: Layer-based collision detection
/// - **Monitoring**: Enable/disable area monitoring
/// - **Godot Compatibility**: API matching Godot's Area2D class
///
/// ## Area Properties
///
/// Area2D provides comprehensive area management:
/// - **Monitoring**: Whether the area detects other bodies
/// - **Monitorable**: Whether other areas can detect this area
/// - **Collision Layers**: Which layers this area exists on
/// - **Collision Mask**: Which layers this area can detect
/// - **Space Override**: How the area affects physics
/// - **Gravity**: Custom gravity within the area
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::physics::{Area2D, SpaceOverride};
/// # use verturion::core::math::Vector2;
/// // Create a trigger area
/// let mut trigger = Area2D::new("TriggerZone");
///
/// // Configure area properties
/// trigger.set_monitoring(true);
/// trigger.set_monitorable(true);
/// trigger.set_collision_layer(1);
/// trigger.set_collision_mask(2);
///
/// // Set custom gravity
/// trigger.set_space_override_mode(SpaceOverride::Replace);
/// trigger.set_gravity_vector(Vector2::new(0.0, -500.0));
///
/// assert!(trigger.is_monitoring());
/// assert!(trigger.is_monitorable());
/// ```
#[derive(Debug, Clone)]
pub struct Area2D {
    /// Base Node2D functionality
    base: Node2D,
    /// Whether this area monitors other bodies
    monitoring: bool,
    /// Whether this area can be monitored by others
    monitorable: bool,
    /// Collision layer bitmask (which layers this area exists on)
    collision_layer: u32,
    /// Collision mask bitmask (which layers this area can detect)
    collision_mask: u32,
    /// Space override mode for physics influence
    space_override_mode: SpaceOverride,
    /// Custom gravity vector
    gravity_vector: Vector2,
    /// Gravity strength multiplier
    gravity: f32,
    /// Linear damping within the area
    linear_damp: f32,
    /// Angular damping within the area
    angular_damp: f32,
    /// Audio bus override for spatial audio
    audio_bus_override: bool,
    /// Audio bus name
    audio_bus_name: String,
    /// Priority for overlapping areas
    priority: i32,
    /// Bodies currently inside this area
    overlapping_bodies: HashSet<u64>, // Using node IDs
    /// Areas currently overlapping this area
    overlapping_areas: HashSet<u64>, // Using node IDs
}

impl Area2D {
    /// ### Creates a new Area2D with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this area node
    ///
    /// # Returns
    /// A new Area2D instance with default area properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::physics::Area2D;
    /// let area = Area2D::new("TriggerArea");
    /// assert_eq!(area.get_name(), "TriggerArea");
    /// assert!(area.is_monitoring());
    /// assert!(area.is_monitorable());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            base: Node2D::new(name),
            monitoring: true,
            monitorable: true,
            collision_layer: 1,
            collision_mask: 1,
            space_override_mode: SpaceOverride::Disabled,
            gravity_vector: Vector2::new(0.0, 98.0),
            gravity: 1.0,
            linear_damp: 0.1,
            angular_damp: 1.0,
            audio_bus_override: false,
            audio_bus_name: "Master".to_string(),
            priority: 0,
            overlapping_bodies: HashSet::new(),
            overlapping_areas: HashSet::new(),
        }
    }

    /// ### Checks if the area is monitoring other bodies.
    ///
    /// # Returns
    /// True if monitoring is enabled, false otherwise.
    #[inline]
    pub fn is_monitoring(&self) -> bool {
        self.monitoring
    }

    /// ### Sets the monitoring state.
    ///
    /// # Parameters
    /// - `monitoring`: Whether to enable monitoring
    #[inline]
    pub fn set_monitoring(&mut self, monitoring: bool) {
        self.monitoring = monitoring;
        if !monitoring {
            self.overlapping_bodies.clear();
            self.overlapping_areas.clear();
        }
    }

    /// ### Checks if the area is monitorable by others.
    ///
    /// # Returns
    /// True if monitorable, false otherwise.
    #[inline]
    pub fn is_monitorable(&self) -> bool {
        self.monitorable
    }

    /// ### Sets the monitorable state.
    ///
    /// # Parameters
    /// - `monitorable`: Whether this area can be monitored by others
    #[inline]
    pub fn set_monitorable(&mut self, monitorable: bool) {
        self.monitorable = monitorable;
    }

    /// ### Gets the collision layer.
    ///
    /// # Returns
    /// The current collision layer bitmask.
    #[inline]
    pub fn get_collision_layer(&self) -> u32 {
        self.collision_layer
    }

    /// ### Sets the collision layer.
    ///
    /// # Parameters
    /// - `layer`: The new collision layer bitmask
    #[inline]
    pub fn set_collision_layer(&mut self, layer: u32) {
        self.collision_layer = layer;
    }

    /// ### Gets the collision mask.
    ///
    /// # Returns
    /// The current collision mask bitmask.
    #[inline]
    pub fn get_collision_mask(&self) -> u32 {
        self.collision_mask
    }

    /// ### Sets the collision mask.
    ///
    /// # Parameters
    /// - `mask`: The new collision mask bitmask
    #[inline]
    pub fn set_collision_mask(&mut self, mask: u32) {
        self.collision_mask = mask;
    }

    /// ### Gets the space override mode.
    ///
    /// # Returns
    /// The current space override mode.
    #[inline]
    pub fn get_space_override_mode(&self) -> SpaceOverride {
        self.space_override_mode
    }

    /// ### Sets the space override mode.
    ///
    /// # Parameters
    /// - `mode`: The new space override mode
    #[inline]
    pub fn set_space_override_mode(&mut self, mode: SpaceOverride) {
        self.space_override_mode = mode;
    }

    /// ### Gets the gravity vector.
    ///
    /// # Returns
    /// The current gravity vector.
    #[inline]
    pub fn get_gravity_vector(&self) -> Vector2 {
        self.gravity_vector
    }

    /// ### Sets the gravity vector.
    ///
    /// # Parameters
    /// - `gravity`: The new gravity vector
    #[inline]
    pub fn set_gravity_vector(&mut self, gravity: Vector2) {
        self.gravity_vector = gravity;
    }

    /// ### Gets the gravity strength.
    ///
    /// # Returns
    /// The current gravity multiplier.
    #[inline]
    pub fn get_gravity(&self) -> f32 {
        self.gravity
    }

    /// ### Sets the gravity strength.
    ///
    /// # Parameters
    /// - `gravity`: The new gravity multiplier
    #[inline]
    pub fn set_gravity(&mut self, gravity: f32) {
        self.gravity = gravity;
    }

    /// ### Gets the linear damping.
    ///
    /// # Returns
    /// The current linear damping value.
    #[inline]
    pub fn get_linear_damp(&self) -> f32 {
        self.linear_damp
    }

    /// ### Sets the linear damping.
    ///
    /// # Parameters
    /// - `damp`: The new linear damping value
    #[inline]
    pub fn set_linear_damp(&mut self, damp: f32) {
        self.linear_damp = damp.max(0.0);
    }

    /// ### Gets the angular damping.
    ///
    /// # Returns
    /// The current angular damping value.
    #[inline]
    pub fn get_angular_damp(&self) -> f32 {
        self.angular_damp
    }

    /// ### Sets the angular damping.
    ///
    /// # Parameters
    /// - `damp`: The new angular damping value
    #[inline]
    pub fn set_angular_damp(&mut self, damp: f32) {
        self.angular_damp = damp.max(0.0);
    }

    /// ### Gets the priority.
    ///
    /// # Returns
    /// The current area priority.
    #[inline]
    pub fn get_priority(&self) -> i32 {
        self.priority
    }

    /// ### Sets the priority.
    ///
    /// # Parameters
    /// - `priority`: The new area priority
    #[inline]
    pub fn set_priority(&mut self, priority: i32) {
        self.priority = priority;
    }

    /// ### Gets the count of overlapping bodies.
    ///
    /// # Returns
    /// The number of bodies currently overlapping this area.
    #[inline]
    pub fn get_overlapping_body_count(&self) -> usize {
        self.overlapping_bodies.len()
    }

    /// ### Gets the count of overlapping areas.
    ///
    /// # Returns
    /// The number of areas currently overlapping this area.
    #[inline]
    pub fn get_overlapping_area_count(&self) -> usize {
        self.overlapping_areas.len()
    }

    /// ### Checks if a specific body is overlapping.
    ///
    /// # Parameters
    /// - `body_id`: The ID of the body to check
    ///
    /// # Returns
    /// True if the body is overlapping, false otherwise.
    #[inline]
    pub fn has_overlapping_body(&self, body_id: u64) -> bool {
        self.overlapping_bodies.contains(&body_id)
    }

    /// ### Checks if a specific area is overlapping.
    ///
    /// # Parameters
    /// - `area_id`: The ID of the area to check
    ///
    /// # Returns
    /// True if the area is overlapping, false otherwise.
    #[inline]
    pub fn has_overlapping_area(&self, area_id: u64) -> bool {
        self.overlapping_areas.contains(&area_id)
    }

    /// ### Simulates a body entering the area.
    ///
    /// # Parameters
    /// - `body_id`: The ID of the body entering
    #[inline]
    pub fn _on_body_entered(&mut self, body_id: u64) {
        if self.monitoring {
            self.overlapping_bodies.insert(body_id);
            // In a real implementation, this would emit a signal
        }
    }

    /// ### Simulates a body exiting the area.
    ///
    /// # Parameters
    /// - `body_id`: The ID of the body exiting
    #[inline]
    pub fn _on_body_exited(&mut self, body_id: u64) {
        if self.monitoring {
            self.overlapping_bodies.remove(&body_id);
            // In a real implementation, this would emit a signal
        }
    }

    /// ### Simulates an area entering this area.
    ///
    /// # Parameters
    /// - `area_id`: The ID of the area entering
    #[inline]
    pub fn _on_area_entered(&mut self, area_id: u64) {
        if self.monitoring {
            self.overlapping_areas.insert(area_id);
            // In a real implementation, this would emit a signal
        }
    }

    /// ### Simulates an area exiting this area.
    ///
    /// # Parameters
    /// - `area_id`: The ID of the area exiting
    #[inline]
    pub fn _on_area_exited(&mut self, area_id: u64) {
        if self.monitoring {
            self.overlapping_areas.remove(&area_id);
            // In a real implementation, this would emit a signal
        }
    }

    /// ### Provides access to the base Node2D functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node2D.
    #[inline]
    pub fn base(&self) -> &Node2D {
        &self.base
    }

    /// ### Provides mutable access to the base Node2D functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node2D.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node2D {
        &mut self.base
    }

    /// ### Gets the node name from the base Node2D.
    ///
    /// # Returns
    /// The name of this area node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }
}

impl fmt::Display for Area2D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Area2D({}, monitoring: {}, bodies: {}, areas: {})",
               self.get_name(), self.monitoring,
               self.overlapping_bodies.len(), self.overlapping_areas.len())
    }
}

impl PartialEq for Area2D {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for Area2D {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_area2d_creation() {
        let area = Area2D::new("TestArea");
        assert_eq!(area.get_name(), "TestArea");
        assert!(area.is_monitoring());
        assert!(area.is_monitorable());
        assert_eq!(area.get_collision_layer(), 1);
        assert_eq!(area.get_collision_mask(), 1);
        assert_eq!(area.get_space_override_mode(), SpaceOverride::Disabled);
        assert_eq!(area.get_gravity_vector(), Vector2::new(0.0, 98.0));
        assert_eq!(area.get_gravity(), 1.0);
        assert_eq!(area.get_priority(), 0);
        assert_eq!(area.get_overlapping_body_count(), 0);
        assert_eq!(area.get_overlapping_area_count(), 0);
    }

    #[test]
    fn test_area2d_monitoring() {
        let mut area = Area2D::new("Area");

        // Initially monitoring
        assert!(area.is_monitoring());

        // Disable monitoring
        area.set_monitoring(false);
        assert!(!area.is_monitoring());

        // Re-enable monitoring
        area.set_monitoring(true);
        assert!(area.is_monitoring());
    }

    #[test]
    fn test_area2d_monitorable() {
        let mut area = Area2D::new("Area");

        // Initially monitorable
        assert!(area.is_monitorable());

        // Disable monitorable
        area.set_monitorable(false);
        assert!(!area.is_monitorable());

        // Re-enable monitorable
        area.set_monitorable(true);
        assert!(area.is_monitorable());
    }

    #[test]
    fn test_area2d_collision_layers() {
        let mut area = Area2D::new("Area");

        // Initially layer 1
        assert_eq!(area.get_collision_layer(), 1);
        assert_eq!(area.get_collision_mask(), 1);

        // Set custom layer and mask
        area.set_collision_layer(4); // Binary: 100
        area.set_collision_mask(6);  // Binary: 110

        assert_eq!(area.get_collision_layer(), 4);
        assert_eq!(area.get_collision_mask(), 6);
    }

    #[test]
    fn test_area2d_space_override() {
        let mut area = Area2D::new("Area");

        // Initially disabled
        assert_eq!(area.get_space_override_mode(), SpaceOverride::Disabled);

        // Test all override modes
        area.set_space_override_mode(SpaceOverride::Combine);
        assert_eq!(area.get_space_override_mode(), SpaceOverride::Combine);

        area.set_space_override_mode(SpaceOverride::Replace);
        assert_eq!(area.get_space_override_mode(), SpaceOverride::Replace);

        area.set_space_override_mode(SpaceOverride::CombineReplace);
        assert_eq!(area.get_space_override_mode(), SpaceOverride::CombineReplace);

        area.set_space_override_mode(SpaceOverride::Disabled);
        assert_eq!(area.get_space_override_mode(), SpaceOverride::Disabled);
    }

    #[test]
    fn test_area2d_gravity_properties() {
        let mut area = Area2D::new("Area");

        // Initially default gravity
        assert_eq!(area.get_gravity_vector(), Vector2::new(0.0, 98.0));
        assert_eq!(area.get_gravity(), 1.0);

        // Set custom gravity
        area.set_gravity_vector(Vector2::new(10.0, -200.0));
        area.set_gravity(1.5);

        assert_eq!(area.get_gravity_vector(), Vector2::new(10.0, -200.0));
        assert_eq!(area.get_gravity(), 1.5);
    }

    #[test]
    fn test_area2d_damping_properties() {
        let mut area = Area2D::new("Area");

        // Initially default damping
        assert_eq!(area.get_linear_damp(), 0.1);
        assert_eq!(area.get_angular_damp(), 1.0);

        // Set custom damping
        area.set_linear_damp(0.5);
        area.set_angular_damp(2.0);

        assert_eq!(area.get_linear_damp(), 0.5);
        assert_eq!(area.get_angular_damp(), 2.0);

        // Test negative damping (should be clamped to 0)
        area.set_linear_damp(-1.0);
        area.set_angular_damp(-0.5);

        assert_eq!(area.get_linear_damp(), 0.0);
        assert_eq!(area.get_angular_damp(), 0.0);
    }

    #[test]
    fn test_area2d_priority() {
        let mut area = Area2D::new("Area");

        // Initially priority 0
        assert_eq!(area.get_priority(), 0);

        // Set custom priority
        area.set_priority(10);
        assert_eq!(area.get_priority(), 10);

        // Set negative priority
        area.set_priority(-5);
        assert_eq!(area.get_priority(), -5);
    }

    #[test]
    fn test_area2d_body_overlap_tracking() {
        let mut area = Area2D::new("Area");

        // Initially no overlapping bodies
        assert_eq!(area.get_overlapping_body_count(), 0);
        assert!(!area.has_overlapping_body(123));

        // Simulate body entering
        area._on_body_entered(123);
        assert_eq!(area.get_overlapping_body_count(), 1);
        assert!(area.has_overlapping_body(123));

        // Add another body
        area._on_body_entered(456);
        assert_eq!(area.get_overlapping_body_count(), 2);
        assert!(area.has_overlapping_body(123));
        assert!(area.has_overlapping_body(456));

        // Remove first body
        area._on_body_exited(123);
        assert_eq!(area.get_overlapping_body_count(), 1);
        assert!(!area.has_overlapping_body(123));
        assert!(area.has_overlapping_body(456));

        // Remove second body
        area._on_body_exited(456);
        assert_eq!(area.get_overlapping_body_count(), 0);
        assert!(!area.has_overlapping_body(456));
    }

    #[test]
    fn test_area2d_area_overlap_tracking() {
        let mut area = Area2D::new("Area");

        // Initially no overlapping areas
        assert_eq!(area.get_overlapping_area_count(), 0);
        assert!(!area.has_overlapping_area(789));

        // Simulate area entering
        area._on_area_entered(789);
        assert_eq!(area.get_overlapping_area_count(), 1);
        assert!(area.has_overlapping_area(789));

        // Add another area
        area._on_area_entered(101112);
        assert_eq!(area.get_overlapping_area_count(), 2);
        assert!(area.has_overlapping_area(789));
        assert!(area.has_overlapping_area(101112));

        // Remove first area
        area._on_area_exited(789);
        assert_eq!(area.get_overlapping_area_count(), 1);
        assert!(!area.has_overlapping_area(789));
        assert!(area.has_overlapping_area(101112));

        // Remove second area
        area._on_area_exited(101112);
        assert_eq!(area.get_overlapping_area_count(), 0);
        assert!(!area.has_overlapping_area(101112));
    }

    #[test]
    fn test_area2d_monitoring_disables_tracking() {
        let mut area = Area2D::new("Area");

        // Add some overlapping bodies and areas
        area._on_body_entered(123);
        area._on_area_entered(456);
        assert_eq!(area.get_overlapping_body_count(), 1);
        assert_eq!(area.get_overlapping_area_count(), 1);

        // Disable monitoring (should clear overlaps)
        area.set_monitoring(false);
        assert_eq!(area.get_overlapping_body_count(), 0);
        assert_eq!(area.get_overlapping_area_count(), 0);

        // Try to add overlaps while monitoring is disabled (should not work)
        area._on_body_entered(789);
        area._on_area_entered(101112);
        assert_eq!(area.get_overlapping_body_count(), 0);
        assert_eq!(area.get_overlapping_area_count(), 0);

        // Re-enable monitoring
        area.set_monitoring(true);
        area._on_body_entered(789);
        assert_eq!(area.get_overlapping_body_count(), 1);
    }

    #[test]
    fn test_area2d_base_access() {
        let mut area = Area2D::new("Area");

        // Test base access
        assert_eq!(area.base().base().get_name(), "Area");

        // Test mutable base access
        area.base_mut().set_position(Vector2::new(100.0, 200.0));
        assert_eq!(area.base().get_position(), Vector2::new(100.0, 200.0));
    }

    #[test]
    fn test_area2d_equality() {
        let area1 = Area2D::new("Area1");
        let area2 = Area2D::new("Area2");
        let area1_clone = area1.clone();

        // Same area should be equal
        assert_eq!(area1, area1_clone);

        // Different areas should not be equal
        assert_ne!(area1, area2);
    }

    #[test]
    fn test_area2d_display() {
        let mut area = Area2D::new("TestArea");
        area.set_monitoring(true);
        area._on_body_entered(123);
        area._on_area_entered(456);

        let display_str = format!("{}", area);
        assert!(display_str.contains("TestArea"));
        assert!(display_str.contains("monitoring: true"));
        assert!(display_str.contains("bodies: 1"));
        assert!(display_str.contains("areas: 1"));
    }

    #[test]
    fn test_area2d_complex_configuration() {
        let mut area = Area2D::new("ComplexArea");

        // Configure all properties
        area.set_monitoring(true);
        area.set_monitorable(false);
        area.set_collision_layer(8);
        area.set_collision_mask(12);
        area.set_space_override_mode(SpaceOverride::Replace);
        area.set_gravity_vector(Vector2::new(50.0, -300.0));
        area.set_gravity(2.0);
        area.set_linear_damp(0.8);
        area.set_angular_damp(1.5);
        area.set_priority(5);
        area.base_mut().set_position(Vector2::new(400.0, 300.0));

        // Verify all properties
        assert!(area.is_monitoring());
        assert!(!area.is_monitorable());
        assert_eq!(area.get_collision_layer(), 8);
        assert_eq!(area.get_collision_mask(), 12);
        assert_eq!(area.get_space_override_mode(), SpaceOverride::Replace);
        assert_eq!(area.get_gravity_vector(), Vector2::new(50.0, -300.0));
        assert_eq!(area.get_gravity(), 2.0);
        assert_eq!(area.get_linear_damp(), 0.8);
        assert_eq!(area.get_angular_damp(), 1.5);
        assert_eq!(area.get_priority(), 5);
        assert_eq!(area.base().get_position(), Vector2::new(400.0, 300.0));
    }
}
