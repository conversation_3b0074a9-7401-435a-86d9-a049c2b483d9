//! CollisionShape2D implementation for 2D physics collision detection.
//!
//! This module provides the CollisionShape2D class that extends Node2D with
//! collision shape functionality for 2D physics systems. It maintains full
//! compatibility with Godot's CollisionShape2D class while providing efficient
//! collision detection and physics integration.

use std::fmt;
use crate::core::math::{Vector2, Rect2};
use crate::core::variant::Color;
use crate::core::scene::nodes::Node2D;

/// ### 2D collision shape types for physics detection.
///
/// Defines the different types of collision shapes available.
#[derive(Debug, Clone, PartialEq)]
pub enum Shape2D {
    /// Rectangular collision shape
    Rectangle { size: Vector2 },
    /// Circular collision shape
    Circle { radius: f32 },
    /// Capsule collision shape
    Capsule { height: f32, radius: f32 },
    /// Convex polygon collision shape
    ConvexPolygon { points: Vec<Vector2> },
    /// Line collision shape
    Line { point_a: Vector2, point_b: Vector2 },
}

/// ### 2D collision shape for physics detection and response.
///
/// CollisionShape2D extends Node2D with collision shape functionality,
/// providing shape definition, collision detection, and physics integration
/// for 2D physics systems. It maintains full compatibility with Godot's
/// CollisionShape2D class while ensuring efficient collision processing.
///
/// ## Core Features
///
/// - **Shape Definition**: Support for various 2D collision shapes
/// - **Physics Integration**: Seamless integration with physics bodies
/// - **Collision Detection**: Efficient collision detection algorithms
/// - **Debug Visualization**: Optional shape visualization for debugging
/// - **One-Way Collision**: Support for one-way collision platforms
/// - **Godot Compatibility**: API matching Godot's CollisionShape2D class
///
/// ## Shape Types
///
/// CollisionShape2D supports multiple collision shapes:
/// - **Rectangle**: Axis-aligned rectangular collision
/// - **Circle**: Circular collision with radius
/// - **Capsule**: Capsule shape with height and radius
/// - **ConvexPolygon**: Custom convex polygon shapes
/// - **Line**: Line segment collision for platforms
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::physics::{CollisionShape2D, Shape2D};
/// # use verturion::core::math::Vector2;
/// // Create a collision shape
/// let mut collision = CollisionShape2D::new("PlayerCollision");
///
/// // Set rectangular collision shape
/// collision.set_shape(Some(Shape2D::Rectangle {
///     size: Vector2::new(32.0, 64.0)
/// }));
///
/// // Configure collision properties
/// collision.set_disabled(false);
/// collision.set_one_way_collision(false);
///
/// assert!(!collision.is_disabled());
/// assert!(collision.get_shape().is_some());
/// ```
#[derive(Debug, Clone)]
pub struct CollisionShape2D {
    /// Base Node2D functionality
    base: Node2D,
    /// Collision shape definition
    shape: Option<Shape2D>,
    /// Whether the collision shape is disabled
    disabled: bool,
    /// Whether this is a one-way collision (platform)
    one_way_collision: bool,
    /// One-way collision margin
    one_way_collision_margin: f32,
    /// Whether to show debug visualization
    debug_color: Option<Color>,
}

impl CollisionShape2D {
    /// ### Creates a new CollisionShape2D with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this collision shape node
    ///
    /// # Returns
    /// A new CollisionShape2D instance with default collision properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::physics::CollisionShape2D;
    /// let collision = CollisionShape2D::new("Collision");
    /// assert_eq!(collision.get_name(), "Collision");
    /// assert!(!collision.is_disabled());
    /// assert!(collision.get_shape().is_none());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            base: Node2D::new(name),
            shape: None,
            disabled: false,
            one_way_collision: false,
            one_way_collision_margin: 1.0,
            debug_color: None,
        }
    }

    /// ### Gets the collision shape.
    ///
    /// # Returns
    /// The current collision shape, or None if no shape is set.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::physics::CollisionShape2D;
    /// let collision = CollisionShape2D::new("Collision");
    /// assert!(collision.get_shape().is_none());
    /// ```
    #[inline]
    pub fn get_shape(&self) -> Option<&Shape2D> {
        self.shape.as_ref()
    }

    /// ### Sets the collision shape.
    ///
    /// # Parameters
    /// - `shape`: The new collision shape, or None to clear the shape
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::physics::{CollisionShape2D, Shape2D};
    /// # use verturion::core::math::Vector2;
    /// let mut collision = CollisionShape2D::new("Collision");
    /// collision.set_shape(Some(Shape2D::Circle { radius: 16.0 }));
    /// assert!(collision.get_shape().is_some());
    /// ```
    #[inline]
    pub fn set_shape(&mut self, shape: Option<Shape2D>) {
        self.shape = shape;
    }

    /// ### Checks if the collision shape is disabled.
    ///
    /// # Returns
    /// True if the collision shape is disabled, false otherwise.
    #[inline]
    pub fn is_disabled(&self) -> bool {
        self.disabled
    }

    /// ### Sets the disabled state of the collision shape.
    ///
    /// # Parameters
    /// - `disabled`: Whether the collision shape should be disabled
    #[inline]
    pub fn set_disabled(&mut self, disabled: bool) {
        self.disabled = disabled;
    }

    /// ### Checks if one-way collision is enabled.
    ///
    /// # Returns
    /// True if one-way collision is enabled, false otherwise.
    #[inline]
    pub fn is_one_way_collision(&self) -> bool {
        self.one_way_collision
    }

    /// ### Sets the one-way collision mode.
    ///
    /// # Parameters
    /// - `one_way`: Whether to enable one-way collision
    #[inline]
    pub fn set_one_way_collision(&mut self, one_way: bool) {
        self.one_way_collision = one_way;
    }

    /// ### Gets the one-way collision margin.
    ///
    /// # Returns
    /// The current one-way collision margin.
    #[inline]
    pub fn get_one_way_collision_margin(&self) -> f32 {
        self.one_way_collision_margin
    }

    /// ### Sets the one-way collision margin.
    ///
    /// # Parameters
    /// - `margin`: The new one-way collision margin
    #[inline]
    pub fn set_one_way_collision_margin(&mut self, margin: f32) {
        self.one_way_collision_margin = margin.max(0.0);
    }

    /// ### Gets the debug color.
    ///
    /// # Returns
    /// The current debug color, or None if debug visualization is disabled.
    #[inline]
    pub fn get_debug_color(&self) -> Option<Color> {
        self.debug_color
    }

    /// ### Sets the debug color for visualization.
    ///
    /// # Parameters
    /// - `color`: The debug color, or None to disable debug visualization
    #[inline]
    pub fn set_debug_color(&mut self, color: Option<Color>) {
        self.debug_color = color;
    }

    /// ### Gets the bounding rectangle of the collision shape.
    ///
    /// # Returns
    /// The bounding rectangle, or None if no shape is set.
    #[inline]
    pub fn get_bounding_rect(&self) -> Option<Rect2> {
        match &self.shape {
            Some(Shape2D::Rectangle { size }) => {
                let pos = self.base.get_position();
                Some(Rect2::new(pos.x - size.x / 2.0, pos.y - size.y / 2.0, size.x, size.y))
            }
            Some(Shape2D::Circle { radius }) => {
                let pos = self.base.get_position();
                Some(Rect2::new(pos.x - radius, pos.y - radius, radius * 2.0, radius * 2.0))
            }
            Some(Shape2D::Capsule { height, radius }) => {
                let pos = self.base.get_position();
                let total_height = height + radius * 2.0;
                let width = radius * 2.0;
                Some(Rect2::new(pos.x - width / 2.0, pos.y - total_height / 2.0, width, total_height))
            }
            Some(Shape2D::ConvexPolygon { points }) => {
                if points.is_empty() {
                    return None;
                }
                let pos = self.base.get_position();
                let mut min_x = f32::INFINITY;
                let mut min_y = f32::INFINITY;
                let mut max_x = f32::NEG_INFINITY;
                let mut max_y = f32::NEG_INFINITY;

                for point in points {
                    let world_point = pos + *point;
                    min_x = min_x.min(world_point.x);
                    min_y = min_y.min(world_point.y);
                    max_x = max_x.max(world_point.x);
                    max_y = max_y.max(world_point.y);
                }

                Some(Rect2::new(min_x, min_y, max_x - min_x, max_y - min_y))
            }
            Some(Shape2D::Line { point_a, point_b }) => {
                let pos = self.base.get_position();
                let world_a = pos + *point_a;
                let world_b = pos + *point_b;
                let min_x = world_a.x.min(world_b.x);
                let min_y = world_a.y.min(world_b.y);
                let max_x = world_a.x.max(world_b.x);
                let max_y = world_a.y.max(world_b.y);
                Some(Rect2::new(min_x, min_y, max_x - min_x, max_y - min_y))
            }
            None => None,
        }
    }

    /// ### Provides access to the base Node2D functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node2D.
    #[inline]
    pub fn base(&self) -> &Node2D {
        &self.base
    }

    /// ### Provides mutable access to the base Node2D functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node2D.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node2D {
        &mut self.base
    }

    /// ### Gets the node name from the base Node2D.
    ///
    /// # Returns
    /// The name of this collision shape node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }
}

impl fmt::Display for CollisionShape2D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "CollisionShape2D({}, shape: {:?}, disabled: {}, one_way: {})",
               self.get_name(),
               self.shape.as_ref().map(|_| "Some").unwrap_or("None"),
               self.disabled,
               self.one_way_collision)
    }
}

impl PartialEq for CollisionShape2D {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for CollisionShape2D {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_collision_shape2d_creation() {
        let collision = CollisionShape2D::new("TestCollision");
        assert_eq!(collision.get_name(), "TestCollision");
        assert!(collision.get_shape().is_none());
        assert!(!collision.is_disabled());
        assert!(!collision.is_one_way_collision());
        assert_eq!(collision.get_one_way_collision_margin(), 1.0);
        assert!(collision.get_debug_color().is_none());
    }

    #[test]
    fn test_collision_shape2d_rectangle_shape() {
        let mut collision = CollisionShape2D::new("Collision");

        // Set rectangle shape
        let rect_shape = Shape2D::Rectangle { size: Vector2::new(64.0, 32.0) };
        collision.set_shape(Some(rect_shape.clone()));

        assert!(collision.get_shape().is_some());
        if let Some(Shape2D::Rectangle { size }) = collision.get_shape() {
            assert_eq!(*size, Vector2::new(64.0, 32.0));
        } else {
            panic!("Expected rectangle shape");
        }
    }

    #[test]
    fn test_collision_shape2d_circle_shape() {
        let mut collision = CollisionShape2D::new("Collision");

        // Set circle shape
        let circle_shape = Shape2D::Circle { radius: 25.0 };
        collision.set_shape(Some(circle_shape));

        assert!(collision.get_shape().is_some());
        if let Some(Shape2D::Circle { radius }) = collision.get_shape() {
            assert_eq!(*radius, 25.0);
        } else {
            panic!("Expected circle shape");
        }
    }

    #[test]
    fn test_collision_shape2d_capsule_shape() {
        let mut collision = CollisionShape2D::new("Collision");

        // Set capsule shape
        let capsule_shape = Shape2D::Capsule { height: 40.0, radius: 15.0 };
        collision.set_shape(Some(capsule_shape));

        assert!(collision.get_shape().is_some());
        if let Some(Shape2D::Capsule { height, radius }) = collision.get_shape() {
            assert_eq!(*height, 40.0);
            assert_eq!(*radius, 15.0);
        } else {
            panic!("Expected capsule shape");
        }
    }

    #[test]
    fn test_collision_shape2d_polygon_shape() {
        let mut collision = CollisionShape2D::new("Collision");

        // Set polygon shape
        let points = vec![
            Vector2::new(-10.0, -10.0),
            Vector2::new(10.0, -10.0),
            Vector2::new(10.0, 10.0),
            Vector2::new(-10.0, 10.0),
        ];
        let polygon_shape = Shape2D::ConvexPolygon { points: points.clone() };
        collision.set_shape(Some(polygon_shape));

        assert!(collision.get_shape().is_some());
        if let Some(Shape2D::ConvexPolygon { points: shape_points }) = collision.get_shape() {
            assert_eq!(shape_points.len(), 4);
            assert_eq!(shape_points[0], Vector2::new(-10.0, -10.0));
        } else {
            panic!("Expected polygon shape");
        }
    }

    #[test]
    fn test_collision_shape2d_line_shape() {
        let mut collision = CollisionShape2D::new("Collision");

        // Set line shape
        let line_shape = Shape2D::Line {
            point_a: Vector2::new(-20.0, 0.0),
            point_b: Vector2::new(20.0, 0.0)
        };
        collision.set_shape(Some(line_shape));

        assert!(collision.get_shape().is_some());
        if let Some(Shape2D::Line { point_a, point_b }) = collision.get_shape() {
            assert_eq!(*point_a, Vector2::new(-20.0, 0.0));
            assert_eq!(*point_b, Vector2::new(20.0, 0.0));
        } else {
            panic!("Expected line shape");
        }
    }

    #[test]
    fn test_collision_shape2d_disabled_state() {
        let mut collision = CollisionShape2D::new("Collision");

        // Initially enabled
        assert!(!collision.is_disabled());

        // Disable collision
        collision.set_disabled(true);
        assert!(collision.is_disabled());

        // Re-enable collision
        collision.set_disabled(false);
        assert!(!collision.is_disabled());
    }

    #[test]
    fn test_collision_shape2d_one_way_collision() {
        let mut collision = CollisionShape2D::new("Collision");

        // Initially not one-way
        assert!(!collision.is_one_way_collision());
        assert_eq!(collision.get_one_way_collision_margin(), 1.0);

        // Enable one-way collision
        collision.set_one_way_collision(true);
        assert!(collision.is_one_way_collision());

        // Set margin
        collision.set_one_way_collision_margin(2.5);
        assert_eq!(collision.get_one_way_collision_margin(), 2.5);

        // Test negative margin (should be clamped to 0)
        collision.set_one_way_collision_margin(-1.0);
        assert_eq!(collision.get_one_way_collision_margin(), 0.0);

        // Disable one-way collision
        collision.set_one_way_collision(false);
        assert!(!collision.is_one_way_collision());
    }

    #[test]
    fn test_collision_shape2d_debug_color() {
        let mut collision = CollisionShape2D::new("Collision");

        // Initially no debug color
        assert!(collision.get_debug_color().is_none());

        // Set debug color
        let debug_color = Color::new(1.0, 0.0, 0.0, 0.5);
        collision.set_debug_color(Some(debug_color));
        assert_eq!(collision.get_debug_color(), Some(debug_color));

        // Clear debug color
        collision.set_debug_color(None);
        assert!(collision.get_debug_color().is_none());
    }

    #[test]
    fn test_collision_shape2d_bounding_rect() {
        let mut collision = CollisionShape2D::new("Collision");
        collision.base_mut().set_position(Vector2::new(100.0, 50.0));

        // No shape - no bounding rect
        assert!(collision.get_bounding_rect().is_none());

        // Rectangle shape
        collision.set_shape(Some(Shape2D::Rectangle { size: Vector2::new(40.0, 20.0) }));
        let rect = collision.get_bounding_rect().unwrap();
        assert_eq!(rect, Rect2::new(80.0, 40.0, 40.0, 20.0)); // Centered around position

        // Circle shape
        collision.set_shape(Some(Shape2D::Circle { radius: 15.0 }));
        let rect = collision.get_bounding_rect().unwrap();
        assert_eq!(rect, Rect2::new(85.0, 35.0, 30.0, 30.0)); // Centered around position
    }

    #[test]
    fn test_collision_shape2d_base_access() {
        let mut collision = CollisionShape2D::new("Collision");

        // Test base access
        assert_eq!(collision.base().base().get_name(), "Collision");

        // Test mutable base access
        collision.base_mut().set_position(Vector2::new(200.0, 150.0));
        assert_eq!(collision.base().get_position(), Vector2::new(200.0, 150.0));
    }

    #[test]
    fn test_collision_shape2d_equality() {
        let collision1 = CollisionShape2D::new("Collision1");
        let collision2 = CollisionShape2D::new("Collision2");
        let collision1_clone = collision1.clone();

        // Same collision should be equal
        assert_eq!(collision1, collision1_clone);

        // Different collisions should not be equal
        assert_ne!(collision1, collision2);
    }

    #[test]
    fn test_collision_shape2d_display() {
        let mut collision = CollisionShape2D::new("TestCollision");
        collision.set_shape(Some(Shape2D::Circle { radius: 10.0 }));
        collision.set_disabled(true);
        collision.set_one_way_collision(true);

        let display_str = format!("{}", collision);
        assert!(display_str.contains("TestCollision"));
        assert!(display_str.contains("shape: Some"));
        assert!(display_str.contains("disabled: true"));
        assert!(display_str.contains("one_way: true"));
    }

    #[test]
    fn test_collision_shape2d_complex_configuration() {
        let mut collision = CollisionShape2D::new("ComplexCollision");

        // Configure all properties
        collision.set_shape(Some(Shape2D::Capsule { height: 50.0, radius: 20.0 }));
        collision.set_disabled(false);
        collision.set_one_way_collision(true);
        collision.set_one_way_collision_margin(3.0);
        collision.set_debug_color(Some(Color::new(0.0, 1.0, 0.0, 0.7)));
        collision.base_mut().set_position(Vector2::new(300.0, 200.0));

        // Verify all properties
        assert!(collision.get_shape().is_some());
        assert!(!collision.is_disabled());
        assert!(collision.is_one_way_collision());
        assert_eq!(collision.get_one_way_collision_margin(), 3.0);
        assert_eq!(collision.get_debug_color(), Some(Color::new(0.0, 1.0, 0.0, 0.7)));
        assert_eq!(collision.base().get_position(), Vector2::new(300.0, 200.0));
    }
}
