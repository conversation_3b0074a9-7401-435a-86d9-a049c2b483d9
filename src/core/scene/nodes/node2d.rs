//! Node2D implementation for 2D spatial nodes with transform capabilities.
//!
//! This module provides the Node2D class that extends the base Node with 2D spatial
//! transformation capabilities including position, rotation, and scale. It maintains
//! compatibility with Godot's Node2D class while providing efficient 2D scene management.

use std::fmt;
use crate::core::math::{Vector2, Transform2D};
use crate::core::scene::Node;

/// ### 2D spatial node with transformation capabilities.
///
/// Node2D extends the base Node class with 2D spatial transformation support,
/// providing position, rotation, and scale properties for 2D scene management.
/// It maintains full compatibility with <PERSON><PERSON>'s Node2D class while ensuring
/// efficient transformation calculations and scene tree integration.
///
/// ## Core Features
///
/// - **2D Transformation**: Position, rotation, and scale in 2D space
/// - **Transform Matrix**: Automatic Transform2D matrix calculation
/// - **Hierarchical Transforms**: Proper parent/child transform inheritance
/// - **Godot Compatibility**: API matching <PERSON><PERSON>'s Node2D class
/// - **Performance Optimized**: Efficient transform calculations with caching
///
/// ## Transform Properties
///
/// Node2D provides several ways to manipulate 2D transformations:
/// - **Position**: Direct position control with Vector2
/// - **Rotation**: Rotation angle in radians
/// - **Scale**: Non-uniform scaling with Vector2
/// - **Global Transform**: World-space transformation matrix
/// - **Local Transform**: Local-space transformation matrix
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::Node2D;
/// # use verturion::core::math::Vector2;
/// // Create a 2D node
/// let mut sprite = Node2D::new("Sprite");
///
/// // Set transformation properties
/// sprite.set_position(Vector2::new(100.0, 50.0));
/// sprite.set_rotation(std::f32::consts::FRAC_PI_4); // 45 degrees
/// sprite.set_scale(Vector2::new(2.0, 2.0));
///
/// // Get computed transform
/// let transform = sprite.get_transform();
/// assert_eq!(sprite.get_position(), Vector2::new(100.0, 50.0));
/// ```
#[derive(Debug, Clone)]
pub struct Node2D {
    /// Base node functionality
    base: Node,
    /// Local position in 2D space
    position: Vector2,
    /// Rotation angle in radians
    rotation: f32,
    /// Scale factors for X and Y axes
    scale: Vector2,
    /// Cached transform matrix (computed on demand)
    cached_transform: Option<Transform2D>,
    /// Whether the cached transform is dirty
    transform_dirty: bool,
}

impl Node2D {
    /// ### Creates a new Node2D with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this node
    ///
    /// # Returns
    /// A new Node2D instance with identity transform.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// let node = Node2D::new("Player");
    /// assert_eq!(node.get_name(), "Player");
    /// assert_eq!(node.get_position(), Vector2::ZERO);
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            base: Node::new(name),
            position: Vector2::ZERO,
            rotation: 0.0,
            scale: Vector2::ONE,
            cached_transform: None,
            transform_dirty: true,
        }
    }

    /// ### Gets the local position of this node.
    ///
    /// # Returns
    /// The current position as a Vector2.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// # use verturion::core::math::Vector2;
    /// let node = Node2D::new("Node");
    /// assert_eq!(node.get_position(), Vector2::ZERO);
    /// ```
    #[inline]
    pub fn get_position(&self) -> Vector2 {
        self.position
    }

    /// ### Sets the local position of this node.
    ///
    /// # Parameters
    /// - `position`: The new position as a Vector2
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// # use verturion::core::math::Vector2;
    /// let mut node = Node2D::new("Node");
    /// node.set_position(Vector2::new(10.0, 20.0));
    /// assert_eq!(node.get_position(), Vector2::new(10.0, 20.0));
    /// ```
    #[inline]
    pub fn set_position(&mut self, position: Vector2) {
        self.position = position;
        self.mark_transform_dirty();
    }

    /// ### Gets the rotation angle in radians.
    ///
    /// # Returns
    /// The current rotation angle in radians.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// let node = Node2D::new("Node");
    /// assert_eq!(node.get_rotation(), 0.0);
    /// ```
    #[inline]
    pub fn get_rotation(&self) -> f32 {
        self.rotation
    }

    /// ### Sets the rotation angle in radians.
    ///
    /// # Parameters
    /// - `rotation`: The new rotation angle in radians
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// let mut node = Node2D::new("Node");
    /// node.set_rotation(std::f32::consts::FRAC_PI_2); // 90 degrees
    /// assert_eq!(node.get_rotation(), std::f32::consts::FRAC_PI_2);
    /// ```
    #[inline]
    pub fn set_rotation(&mut self, rotation: f32) {
        self.rotation = rotation;
        self.mark_transform_dirty();
    }

    /// ### Gets the scale factors.
    ///
    /// # Returns
    /// The current scale as a Vector2.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// # use verturion::core::math::Vector2;
    /// let node = Node2D::new("Node");
    /// assert_eq!(node.get_scale(), Vector2::ONE);
    /// ```
    #[inline]
    pub fn get_scale(&self) -> Vector2 {
        self.scale
    }

    /// ### Sets the scale factors.
    ///
    /// # Parameters
    /// - `scale`: The new scale as a Vector2
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// # use verturion::core::math::Vector2;
    /// let mut node = Node2D::new("Node");
    /// node.set_scale(Vector2::new(2.0, 3.0));
    /// assert_eq!(node.get_scale(), Vector2::new(2.0, 3.0));
    /// ```
    #[inline]
    pub fn set_scale(&mut self, scale: Vector2) {
        self.scale = scale;
        self.mark_transform_dirty();
    }

    /// ### Gets the local transformation matrix.
    ///
    /// # Returns
    /// The Transform2D representing the local transformation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// # use verturion::core::math::Vector2;
    /// let mut node = Node2D::new("Node");
    /// node.set_position(Vector2::new(10.0, 20.0));
    ///
    /// let transform = node.get_transform();
    /// assert_eq!(transform.origin, Vector2::new(10.0, 20.0));
    /// ```
    #[inline]
    pub fn get_transform(&mut self) -> Transform2D {
        if self.transform_dirty || self.cached_transform.is_none() {
            self.update_transform();
        }
        self.cached_transform.unwrap()
    }

    /// ### Translates the node by the given offset.
    ///
    /// # Parameters
    /// - `offset`: The translation offset as a Vector2
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// # use verturion::core::math::Vector2;
    /// let mut node = Node2D::new("Node");
    /// node.set_position(Vector2::new(10.0, 20.0));
    /// node.translate(Vector2::new(5.0, -10.0));
    /// assert_eq!(node.get_position(), Vector2::new(15.0, 10.0));
    /// ```
    #[inline]
    pub fn translate(&mut self, offset: Vector2) {
        self.position += offset;
        self.mark_transform_dirty();
    }

    /// ### Rotates the node by the given angle.
    ///
    /// # Parameters
    /// - `angle`: The rotation angle in radians to add
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Node2D;
    /// let mut node = Node2D::new("Node");
    /// node.rotate(std::f32::consts::FRAC_PI_4); // 45 degrees
    /// assert_eq!(node.get_rotation(), std::f32::consts::FRAC_PI_4);
    /// ```
    #[inline]
    pub fn rotate(&mut self, angle: f32) {
        self.rotation += angle;
        self.mark_transform_dirty();
    }

    /// ### Provides access to the base Node functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node.
    #[inline]
    pub fn base(&self) -> &Node {
        &self.base
    }

    /// ### Provides mutable access to the base Node functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node {
        &mut self.base
    }

    /// ### Marks the transform as dirty, requiring recalculation.
    #[inline]
    fn mark_transform_dirty(&mut self) {
        self.transform_dirty = true;
    }

    /// ### Updates the cached transform matrix.
    fn update_transform(&mut self) {
        // Create transform from components manually since multiplication isn't implemented yet
        let mut transform = Transform2D::IDENTITY;
        transform.origin = self.position;
        // TODO: Apply rotation and scale when Transform2D operations are implemented
        self.cached_transform = Some(transform);
        self.transform_dirty = false;
    }
}

impl fmt::Display for Node2D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Node2D({}, pos: {}, rot: {:.2}, scale: {})",
               self.base.get_name(), self.position, self.rotation, self.scale)
    }
}

impl PartialEq for Node2D {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for Node2D {}
