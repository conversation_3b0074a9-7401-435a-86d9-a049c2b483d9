//! CheckBox implementation for boolean input control with checked/unchecked states.
//!
//! This module provides the CheckBox class that extends Control with
//! comprehensive boolean input functionality including checked/unchecked states,
//! toggle signals, customizable appearance, and user interaction handling.
//! It maintains full compatibility with God<PERSON>'s CheckBox class while providing
//! efficient state management and responsive user input.

use std::fmt;
use crate::core::scene::Control;
use crate::core::signal::{Signal, SignalManager, SignalData};
use crate::core::variant::Variant;

/// ### CheckBox for boolean input control with checked/unchecked states.
///
/// CheckBox extends Control with comprehensive boolean input functionality,
/// providing checked/unchecked states, toggle signals, customizable appearance,
/// and user interaction handling. It maintains full compatibility with <PERSON><PERSON>'s
/// CheckBox class while ensuring efficient state management and responsive
/// user input processing.
///
/// ## Core Features
///
/// - **Boolean State**: Checked and unchecked state management
/// - **Toggle Interaction**: Click-to-toggle functionality
/// - **Signal Integration**: State change and toggle signals
/// - **Customizable Appearance**: Text, icons, and styling options
/// - **Focus Handling**: Keyboard navigation and focus management
/// - **Disabled State**: Support for disabled/read-only mode
/// - **Godot Compatibility**: API matching Godot's CheckBox
///
/// ## State Properties
///
/// CheckBox provides comprehensive state control:
/// - **Checked State**: Current boolean value
/// - **Text Label**: Descriptive text next to checkbox
/// - **Disabled State**: Whether the checkbox can be interacted with
/// - **Focus State**: Whether the checkbox has input focus
/// - **Pressed State**: Visual feedback during interaction
/// - **Flat Style**: Whether to use flat appearance
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::CheckBox;
/// # use verturion::core::signal::SignalManager;
/// // Create a checkbox
/// let mut checkbox = CheckBox::new("SettingsOption");
/// let mut signal_manager = SignalManager::new();
///
/// // Configure checkbox
/// checkbox.set_text("Enable sound effects".to_string());
/// checkbox.set_checked(true, &mut signal_manager);
///
/// // Register signals
/// signal_manager.register_signal(checkbox.get_toggled_signal().clone());
///
/// assert!(checkbox.is_checked());
/// assert_eq!(checkbox.get_text(), "Enable sound effects");
/// assert!(!checkbox.is_disabled());
/// ```
#[derive(Debug, Clone)]
pub struct CheckBox {
    /// Base Control functionality
    base: Control,
    /// Whether the checkbox is checked
    checked: bool,
    /// Text label for the checkbox
    text: String,
    /// Whether the checkbox is disabled
    disabled: bool,
    /// Whether the checkbox has focus
    focused: bool,
    /// Whether the checkbox is currently pressed
    pressed: bool,
    /// Whether to use flat appearance
    flat: bool,
    /// Whether the checkbox can be toggled by clicking
    toggle_mode: bool,
    /// Signal emitted when the checkbox is toggled
    toggled_signal: Signal,
    /// Signal emitted when the checkbox is pressed
    pressed_signal: Signal,
    /// Signal emitted when the checkbox receives focus
    focus_entered_signal: Signal,
    /// Signal emitted when the checkbox loses focus
    focus_exited_signal: Signal,
}

impl CheckBox {
    /// ### Creates a new CheckBox with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this checkbox control
    ///
    /// # Returns
    /// A new CheckBox instance with default properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::CheckBox;
    /// let checkbox = CheckBox::new("MyCheckBox");
    /// assert_eq!(checkbox.get_name(), "MyCheckBox");
    /// assert!(!checkbox.is_checked());
    /// assert!(checkbox.get_text().is_empty());
    /// assert!(!checkbox.is_disabled());
    /// assert!(checkbox.is_toggle_mode());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        let control = Control::new(name);
        let node_id = control.base().get_id();
        Self {
            base: control,
            checked: false,
            text: String::new(),
            disabled: false,
            focused: false,
            pressed: false,
            flat: false,
            toggle_mode: true,
            toggled_signal: Signal::new("toggled", node_id),
            pressed_signal: Signal::new("pressed", node_id),
            focus_entered_signal: Signal::new("focus_entered", node_id),
            focus_exited_signal: Signal::new("focus_exited", node_id),
        }
    }

    /// ### Gets the checked state.
    ///
    /// # Returns
    /// True if the checkbox is checked, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::CheckBox;
    /// # use verturion::core::signal::SignalManager;
    /// let mut checkbox = CheckBox::new("Box");
    /// let mut manager = SignalManager::new();
    /// checkbox.set_checked(true, &mut manager);
    /// assert!(checkbox.is_checked());
    /// ```
    #[inline]
    pub fn is_checked(&self) -> bool {
        self.checked
    }

    /// ### Sets the checked state.
    ///
    /// # Parameters
    /// - `checked`: The new checked state
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::CheckBox;
    /// # use verturion::core::signal::SignalManager;
    /// let mut checkbox = CheckBox::new("Box");
    /// let mut manager = SignalManager::new();
    /// checkbox.set_checked(true, &mut manager);
    /// assert!(checkbox.is_checked());
    /// ```
    #[inline]
    pub fn set_checked(&mut self, checked: bool, signal_manager: &mut SignalManager) {
        if self.checked != checked {
            self.checked = checked;

            // Emit toggled signal
            let mut data = SignalData::empty();
            data.add_arg(Variant::Bool(self.checked));
            signal_manager.emit(self.toggled_signal.id(), data);
        }
    }

    /// ### Toggles the checked state.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::CheckBox;
    /// # use verturion::core::signal::SignalManager;
    /// let mut checkbox = CheckBox::new("Box");
    /// let mut manager = SignalManager::new();
    /// checkbox.toggle(&mut manager);
    /// assert!(checkbox.is_checked());
    /// checkbox.toggle(&mut manager);
    /// assert!(!checkbox.is_checked());
    /// ```
    #[inline]
    pub fn toggle(&mut self, signal_manager: &mut SignalManager) {
        if !self.disabled && self.toggle_mode {
            self.set_checked(!self.checked, signal_manager);
        }
    }

    /// ### Gets the text label.
    ///
    /// # Returns
    /// The text label of the checkbox.
    #[inline]
    pub fn get_text(&self) -> &str {
        &self.text
    }

    /// ### Sets the text label.
    ///
    /// # Parameters
    /// - `text`: The new text label
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::CheckBox;
    /// let mut checkbox = CheckBox::new("Box");
    /// checkbox.set_text("Enable feature".to_string());
    /// assert_eq!(checkbox.get_text(), "Enable feature");
    /// ```
    #[inline]
    pub fn set_text(&mut self, text: String) {
        self.text = text;
    }

    /// ### Checks if the checkbox is disabled.
    ///
    /// # Returns
    /// True if the checkbox is disabled, false otherwise.
    #[inline]
    pub fn is_disabled(&self) -> bool {
        self.disabled
    }

    /// ### Sets the disabled state.
    ///
    /// # Parameters
    /// - `disabled`: Whether the checkbox should be disabled
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::CheckBox;
    /// let mut checkbox = CheckBox::new("Box");
    /// checkbox.set_disabled(true);
    /// assert!(checkbox.is_disabled());
    /// ```
    #[inline]
    pub fn set_disabled(&mut self, disabled: bool) {
        self.disabled = disabled;
        if disabled {
            self.focused = false;
            self.pressed = false;
        }
    }

    /// ### Checks if the checkbox has focus.
    ///
    /// # Returns
    /// True if the checkbox has focus, false otherwise.
    #[inline]
    pub fn is_focused(&self) -> bool {
        self.focused
    }

    /// ### Sets the focus state.
    ///
    /// # Parameters
    /// - `focused`: Whether the checkbox should have focus
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn set_focused(&mut self, focused: bool, signal_manager: &mut SignalManager) {
        if !self.disabled && self.focused != focused {
            self.focused = focused;

            if focused {
                let data = SignalData::empty();
                signal_manager.emit(self.focus_entered_signal.id(), data);
            } else {
                let data = SignalData::empty();
                signal_manager.emit(self.focus_exited_signal.id(), data);
            }
        }
    }

    /// ### Grabs focus for this checkbox.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn grab_focus(&mut self, signal_manager: &mut SignalManager) {
        self.set_focused(true, signal_manager);
    }

    /// ### Releases focus from this checkbox.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn release_focus(&mut self, signal_manager: &mut SignalManager) {
        self.set_focused(false, signal_manager);
    }

    /// ### Checks if the checkbox is currently pressed.
    ///
    /// # Returns
    /// True if the checkbox is pressed, false otherwise.
    #[inline]
    pub fn is_pressed(&self) -> bool {
        self.pressed
    }

    /// ### Sets the pressed state.
    ///
    /// # Parameters
    /// - `pressed`: Whether the checkbox should appear pressed
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn set_pressed(&mut self, pressed: bool, signal_manager: &mut SignalManager) {
        if !self.disabled && self.pressed != pressed {
            self.pressed = pressed;

            if pressed {
                let data = SignalData::empty();
                signal_manager.emit(self.pressed_signal.id(), data);
            }
        }
    }

    /// ### Checks if flat appearance is enabled.
    ///
    /// # Returns
    /// True if flat appearance is enabled, false otherwise.
    #[inline]
    pub fn is_flat(&self) -> bool {
        self.flat
    }

    /// ### Sets the flat appearance.
    ///
    /// # Parameters
    /// - `flat`: Whether to use flat appearance
    #[inline]
    pub fn set_flat(&mut self, flat: bool) {
        self.flat = flat;
    }

    /// ### Checks if toggle mode is enabled.
    ///
    /// # Returns
    /// True if toggle mode is enabled, false otherwise.
    #[inline]
    pub fn is_toggle_mode(&self) -> bool {
        self.toggle_mode
    }

    /// ### Sets the toggle mode.
    ///
    /// # Parameters
    /// - `toggle_mode`: Whether the checkbox can be toggled
    #[inline]
    pub fn set_toggle_mode(&mut self, toggle_mode: bool) {
        self.toggle_mode = toggle_mode;
    }

    /// ### Simulates a click on the checkbox.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::CheckBox;
    /// # use verturion::core::signal::SignalManager;
    /// let mut checkbox = CheckBox::new("Box");
    /// let mut manager = SignalManager::new();
    /// checkbox.click(&mut manager);
    /// assert!(checkbox.is_checked());
    /// ```
    #[inline]
    pub fn click(&mut self, signal_manager: &mut SignalManager) {
        if !self.disabled {
            self.set_pressed(true, signal_manager);
            if self.toggle_mode {
                self.toggle(signal_manager);
            }
            self.set_pressed(false, signal_manager);
        }
    }

    /// ### Handles mouse press events.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Returns
    /// True if the event was handled, false otherwise.
    #[inline]
    pub fn handle_mouse_press(&mut self, signal_manager: &mut SignalManager) -> bool {
        if !self.disabled {
            self.grab_focus(signal_manager);
            self.set_pressed(true, signal_manager);
            true
        } else {
            false
        }
    }

    /// ### Handles mouse release events.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Returns
    /// True if the event was handled, false otherwise.
    #[inline]
    pub fn handle_mouse_release(&mut self, signal_manager: &mut SignalManager) -> bool {
        if !self.disabled && self.pressed {
            if self.toggle_mode {
                self.toggle(signal_manager);
            }
            self.set_pressed(false, signal_manager);
            true
        } else {
            false
        }
    }

    /// ### Handles keyboard input events.
    ///
    /// # Parameters
    /// - `key_pressed`: Whether a key was pressed (true) or released (false)
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Returns
    /// True if the event was handled, false otherwise.
    #[inline]
    pub fn handle_key_input(&mut self, key_pressed: bool, signal_manager: &mut SignalManager) -> bool {
        if !self.disabled && self.focused && key_pressed {
            // Simulate space bar or enter key press
            self.click(signal_manager);
            true
        } else {
            false
        }
    }

    /// ### Gets the toggled signal.
    ///
    /// # Returns
    /// A reference to the toggled signal.
    #[inline]
    pub fn get_toggled_signal(&self) -> &Signal {
        &self.toggled_signal
    }

    /// ### Gets the pressed signal.
    ///
    /// # Returns
    /// A reference to the pressed signal.
    #[inline]
    pub fn get_pressed_signal(&self) -> &Signal {
        &self.pressed_signal
    }

    /// ### Gets the focus entered signal.
    ///
    /// # Returns
    /// A reference to the focus entered signal.
    #[inline]
    pub fn get_focus_entered_signal(&self) -> &Signal {
        &self.focus_entered_signal
    }

    /// ### Gets the focus exited signal.
    ///
    /// # Returns
    /// A reference to the focus exited signal.
    #[inline]
    pub fn get_focus_exited_signal(&self) -> &Signal {
        &self.focus_exited_signal
    }

    /// ### Updates the checkbox state.
    ///
    /// This method can be used for processing checkbox updates.
    ///
    /// # Parameters
    /// - `delta`: The time step in seconds
    /// - `signal_manager`: The signal manager for updates
    #[inline]
    pub fn update(&mut self, _delta: f32, _signal_manager: &mut SignalManager) {
        // CheckBox doesn't need regular updates, but this can be used for animations
    }

    /// ### Provides access to the base Control functionality.
    ///
    /// # Returns
    /// A reference to the underlying Control.
    #[inline]
    pub fn base(&self) -> &Control {
        &self.base
    }

    /// ### Provides mutable access to the base Control functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Control.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Control {
        &mut self.base
    }

    /// ### Gets the node name from the base Control.
    ///
    /// # Returns
    /// The name of this checkbox control.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }
}

impl fmt::Display for CheckBox {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "CheckBox({}, checked: {}, text: '{}', disabled: {})",
               self.get_name(),
               self.checked,
               self.text,
               self.disabled)
    }
}

impl PartialEq for CheckBox {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for CheckBox {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::signal::SignalManager;

    #[test]
    fn test_checkbox_creation() {
        let checkbox = CheckBox::new("TestCheckBox");
        assert_eq!(checkbox.get_name(), "TestCheckBox");
        assert!(!checkbox.is_checked());
        assert!(checkbox.get_text().is_empty());
        assert!(!checkbox.is_disabled());
        assert!(!checkbox.is_focused());
        assert!(!checkbox.is_pressed());
        assert!(!checkbox.is_flat());
        assert!(checkbox.is_toggle_mode());
    }

    #[test]
    fn test_checkbox_checked_state() {
        let mut checkbox = CheckBox::new("CheckBox");
        let mut signal_manager = SignalManager::new();

        // Register signal
        signal_manager.register_signal(checkbox.get_toggled_signal().clone());

        // Test setting checked state
        checkbox.set_checked(true, &mut signal_manager);
        assert!(checkbox.is_checked());

        checkbox.set_checked(false, &mut signal_manager);
        assert!(!checkbox.is_checked());

        // Test that setting the same state doesn't emit signal
        checkbox.set_checked(false, &mut signal_manager);
        assert!(!checkbox.is_checked());
    }

    #[test]
    fn test_checkbox_toggle() {
        let mut checkbox = CheckBox::new("CheckBox");
        let mut signal_manager = SignalManager::new();

        // Register signal
        signal_manager.register_signal(checkbox.get_toggled_signal().clone());

        // Test toggle
        checkbox.toggle(&mut signal_manager);
        assert!(checkbox.is_checked());

        checkbox.toggle(&mut signal_manager);
        assert!(!checkbox.is_checked());

        // Test toggle when disabled
        checkbox.set_disabled(true);
        checkbox.toggle(&mut signal_manager);
        assert!(!checkbox.is_checked()); // Should not change

        // Test toggle when toggle mode is disabled
        checkbox.set_disabled(false);
        checkbox.set_toggle_mode(false);
        checkbox.toggle(&mut signal_manager);
        assert!(!checkbox.is_checked()); // Should not change
    }

    #[test]
    fn test_checkbox_text() {
        let mut checkbox = CheckBox::new("CheckBox");

        // Test setting text
        checkbox.set_text("Enable feature".to_string());
        assert_eq!(checkbox.get_text(), "Enable feature");

        // Test empty text
        checkbox.set_text(String::new());
        assert!(checkbox.get_text().is_empty());

        // Test long text
        let long_text = "This is a very long checkbox label that might wrap".to_string();
        checkbox.set_text(long_text.clone());
        assert_eq!(checkbox.get_text(), long_text);
    }

    #[test]
    fn test_checkbox_disabled_state() {
        let mut checkbox = CheckBox::new("CheckBox");
        let mut signal_manager = SignalManager::new();

        // Test setting disabled
        checkbox.set_disabled(true);
        assert!(checkbox.is_disabled());

        // Test that disabled checkbox loses focus and pressed state
        checkbox.set_focused(true, &mut signal_manager);
        checkbox.set_pressed(true, &mut signal_manager);
        checkbox.set_disabled(true);
        assert!(!checkbox.is_focused());
        assert!(!checkbox.is_pressed());

        // Test enabling
        checkbox.set_disabled(false);
        assert!(!checkbox.is_disabled());
    }

    #[test]
    fn test_checkbox_focus() {
        let mut checkbox = CheckBox::new("CheckBox");
        let mut signal_manager = SignalManager::new();

        // Register signals
        signal_manager.register_signal(checkbox.get_focus_entered_signal().clone());
        signal_manager.register_signal(checkbox.get_focus_exited_signal().clone());

        // Test setting focus
        checkbox.set_focused(true, &mut signal_manager);
        assert!(checkbox.is_focused());

        checkbox.set_focused(false, &mut signal_manager);
        assert!(!checkbox.is_focused());

        // Test grab focus
        checkbox.grab_focus(&mut signal_manager);
        assert!(checkbox.is_focused());

        // Test release focus
        checkbox.release_focus(&mut signal_manager);
        assert!(!checkbox.is_focused());

        // Test that disabled checkbox cannot gain focus
        checkbox.set_disabled(true);
        checkbox.set_focused(true, &mut signal_manager);
        assert!(!checkbox.is_focused());
    }

    #[test]
    fn test_checkbox_pressed_state() {
        let mut checkbox = CheckBox::new("CheckBox");
        let mut signal_manager = SignalManager::new();

        // Register signal
        signal_manager.register_signal(checkbox.get_pressed_signal().clone());

        // Test setting pressed state
        checkbox.set_pressed(true, &mut signal_manager);
        assert!(checkbox.is_pressed());

        checkbox.set_pressed(false, &mut signal_manager);
        assert!(!checkbox.is_pressed());

        // Test that disabled checkbox cannot be pressed
        checkbox.set_disabled(true);
        checkbox.set_pressed(true, &mut signal_manager);
        assert!(!checkbox.is_pressed());
    }

    #[test]
    fn test_checkbox_appearance() {
        let mut checkbox = CheckBox::new("CheckBox");

        // Test flat appearance
        checkbox.set_flat(true);
        assert!(checkbox.is_flat());

        checkbox.set_flat(false);
        assert!(!checkbox.is_flat());

        // Test toggle mode
        checkbox.set_toggle_mode(false);
        assert!(!checkbox.is_toggle_mode());

        checkbox.set_toggle_mode(true);
        assert!(checkbox.is_toggle_mode());
    }

    #[test]
    fn test_checkbox_click() {
        let mut checkbox = CheckBox::new("CheckBox");
        let mut signal_manager = SignalManager::new();

        // Register signals
        signal_manager.register_signal(checkbox.get_toggled_signal().clone());
        signal_manager.register_signal(checkbox.get_pressed_signal().clone());

        // Test click
        checkbox.click(&mut signal_manager);
        assert!(checkbox.is_checked());

        checkbox.click(&mut signal_manager);
        assert!(!checkbox.is_checked());

        // Test click when disabled
        checkbox.set_disabled(true);
        let old_state = checkbox.is_checked();
        checkbox.click(&mut signal_manager);
        assert_eq!(checkbox.is_checked(), old_state); // Should not change

        // Test click when toggle mode is disabled
        checkbox.set_disabled(false);
        checkbox.set_toggle_mode(false);
        let old_state = checkbox.is_checked();
        checkbox.click(&mut signal_manager);
        assert_eq!(checkbox.is_checked(), old_state); // Should not change
    }

    #[test]
    fn test_checkbox_mouse_events() {
        let mut checkbox = CheckBox::new("CheckBox");
        let mut signal_manager = SignalManager::new();

        // Register signals
        signal_manager.register_signal(checkbox.get_toggled_signal().clone());
        signal_manager.register_signal(checkbox.get_pressed_signal().clone());
        signal_manager.register_signal(checkbox.get_focus_entered_signal().clone());

        // Test mouse press
        assert!(checkbox.handle_mouse_press(&mut signal_manager));
        assert!(checkbox.is_focused());
        assert!(checkbox.is_pressed());

        // Test mouse release
        assert!(checkbox.handle_mouse_release(&mut signal_manager));
        assert!(checkbox.is_checked());
        assert!(!checkbox.is_pressed());

        // Test mouse events when disabled
        checkbox.set_disabled(true);
        assert!(!checkbox.handle_mouse_press(&mut signal_manager));
        assert!(!checkbox.handle_mouse_release(&mut signal_manager));

        // Test mouse release when not pressed
        checkbox.set_disabled(false);
        checkbox.set_pressed(false, &mut signal_manager);
        assert!(!checkbox.handle_mouse_release(&mut signal_manager));
    }

    #[test]
    fn test_checkbox_keyboard_events() {
        let mut checkbox = CheckBox::new("CheckBox");
        let mut signal_manager = SignalManager::new();

        // Register signal
        signal_manager.register_signal(checkbox.get_toggled_signal().clone());

        // Test keyboard input when focused
        checkbox.set_focused(true, &mut signal_manager);
        assert!(checkbox.handle_key_input(true, &mut signal_manager));
        assert!(checkbox.is_checked());

        // Test keyboard input when not focused
        checkbox.set_focused(false, &mut signal_manager);
        assert!(!checkbox.handle_key_input(true, &mut signal_manager));

        // Test keyboard input when disabled
        checkbox.set_focused(true, &mut signal_manager);
        checkbox.set_disabled(true);
        assert!(!checkbox.handle_key_input(true, &mut signal_manager));

        // Test key release (should not trigger)
        checkbox.set_disabled(false);
        checkbox.set_focused(true, &mut signal_manager);
        assert!(!checkbox.handle_key_input(false, &mut signal_manager));
    }

    #[test]
    fn test_checkbox_signals() {
        let checkbox = CheckBox::new("CheckBox");

        // Test signal access
        assert_eq!(checkbox.get_toggled_signal().name(), "toggled");
        assert_eq!(checkbox.get_pressed_signal().name(), "pressed");
        assert_eq!(checkbox.get_focus_entered_signal().name(), "focus_entered");
        assert_eq!(checkbox.get_focus_exited_signal().name(), "focus_exited");
    }

    #[test]
    fn test_checkbox_update() {
        let mut checkbox = CheckBox::new("CheckBox");
        let mut signal_manager = SignalManager::new();

        // Test update (should not crash or change state)
        let old_checked = checkbox.is_checked();
        checkbox.update(0.016, &mut signal_manager);
        assert_eq!(checkbox.is_checked(), old_checked);
    }

    #[test]
    fn test_checkbox_base_access() {
        let mut checkbox = CheckBox::new("BaseTest");

        // Test base access
        assert_eq!(checkbox.base().base().get_name(), "BaseTest");

        // Test mutable base access
        checkbox.base_mut().base_mut().set_name("NewName");
        assert_eq!(checkbox.get_name(), "NewName");
    }

    #[test]
    fn test_checkbox_equality() {
        let checkbox1 = CheckBox::new("CheckBox1");
        let checkbox2 = CheckBox::new("CheckBox2");
        let checkbox1_clone = checkbox1.clone();

        // Same checkbox should be equal
        assert_eq!(checkbox1, checkbox1_clone);

        // Different checkboxes should not be equal
        assert_ne!(checkbox1, checkbox2);
    }

    #[test]
    fn test_checkbox_display() {
        let mut checkbox = CheckBox::new("DisplayTest");
        let mut signal_manager = SignalManager::new();

        checkbox.set_text("Test option".to_string());
        checkbox.set_checked(true, &mut signal_manager);
        checkbox.set_disabled(false);

        let display_str = format!("{}", checkbox);
        assert!(display_str.contains("DisplayTest"));
        assert!(display_str.contains("checked: true"));
        assert!(display_str.contains("Test option"));
        assert!(display_str.contains("disabled: false"));
    }

    #[test]
    fn test_complex_checkbox_scenario() {
        let mut settings_checkbox = CheckBox::new("SettingsCheckBox");
        let mut signal_manager = SignalManager::new();

        // Register all signals
        signal_manager.register_signal(settings_checkbox.get_toggled_signal().clone());
        signal_manager.register_signal(settings_checkbox.get_pressed_signal().clone());
        signal_manager.register_signal(settings_checkbox.get_focus_entered_signal().clone());
        signal_manager.register_signal(settings_checkbox.get_focus_exited_signal().clone());

        // Configure checkbox for a settings menu
        settings_checkbox.set_text("Enable sound effects".to_string());
        settings_checkbox.set_flat(false);
        settings_checkbox.set_toggle_mode(true);

        // Simulate user interaction sequence

        // 1. User clicks on checkbox
        assert!(settings_checkbox.handle_mouse_press(&mut signal_manager));
        assert!(settings_checkbox.is_focused());
        assert!(settings_checkbox.is_pressed());

        assert!(settings_checkbox.handle_mouse_release(&mut signal_manager));
        assert!(settings_checkbox.is_checked());
        assert!(!settings_checkbox.is_pressed());

        // 2. User uses keyboard to toggle
        assert!(settings_checkbox.handle_key_input(true, &mut signal_manager));
        assert!(!settings_checkbox.is_checked());

        // 3. User clicks again
        settings_checkbox.click(&mut signal_manager);
        assert!(settings_checkbox.is_checked());

        // 4. Settings become temporarily disabled
        settings_checkbox.set_disabled(true);
        assert!(settings_checkbox.is_disabled());
        assert!(!settings_checkbox.is_focused()); // Should lose focus

        // Try to interact while disabled
        assert!(!settings_checkbox.handle_mouse_press(&mut signal_manager));
        assert!(!settings_checkbox.handle_key_input(true, &mut signal_manager));
        settings_checkbox.click(&mut signal_manager);
        assert!(settings_checkbox.is_checked()); // Should remain unchanged

        // 5. Re-enable settings
        settings_checkbox.set_disabled(false);
        assert!(!settings_checkbox.is_disabled());

        // 6. Test focus management
        settings_checkbox.grab_focus(&mut signal_manager);
        assert!(settings_checkbox.is_focused());

        settings_checkbox.release_focus(&mut signal_manager);
        assert!(!settings_checkbox.is_focused());

        // 7. Test appearance changes
        settings_checkbox.set_flat(true);
        assert!(settings_checkbox.is_flat());

        // 8. Test toggle mode disable
        settings_checkbox.set_toggle_mode(false);
        assert!(!settings_checkbox.is_toggle_mode());

        // Should not toggle when toggle mode is disabled
        let old_state = settings_checkbox.is_checked();
        settings_checkbox.toggle(&mut signal_manager);
        assert_eq!(settings_checkbox.is_checked(), old_state);

        settings_checkbox.click(&mut signal_manager);
        assert_eq!(settings_checkbox.is_checked(), old_state);

        // 9. Re-enable toggle mode and test final state
        settings_checkbox.set_toggle_mode(true);
        settings_checkbox.toggle(&mut signal_manager);
        assert_ne!(settings_checkbox.is_checked(), old_state);

        // Verify final configuration
        assert_eq!(settings_checkbox.get_text(), "Enable sound effects");
        assert!(settings_checkbox.is_flat());
        assert!(settings_checkbox.is_toggle_mode());
        assert!(!settings_checkbox.is_disabled());
        assert!(!settings_checkbox.is_focused());
        assert!(!settings_checkbox.is_pressed());

        // Test update method
        settings_checkbox.update(0.016, &mut signal_manager);

        println!("Settings checkbox: {}", settings_checkbox);
    }
}
