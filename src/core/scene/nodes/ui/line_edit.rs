//! LineEdit implementation for single-line text input with validation and signals.
//!
//! This module provides the LineEdit class that extends Control with
//! comprehensive text input functionality including text validation,
//! placeholder text, input signals, cursor management, and selection
//! handling. It maintains full compatibility with Godot's LineEdit
//! class while providing efficient text processing and input handling.

use std::fmt;
use crate::core::scene::Control;
use crate::core::signal::{Signal, SignalManager, SignalData};
use crate::core::variant::Variant;

/// ### Text alignment modes for LineEdit display.
///
/// Defines how text should be aligned within the LineEdit control.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum TextAlign {
    /// Text aligned to the left
    Left,
    /// Text centered
    Center,
    /// Text aligned to the right
    Right,
}

/// ### Virtual keyboard types for mobile input.
///
/// Defines the type of virtual keyboard to show on mobile devices.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum VirtualKeyboardType {
    /// Default keyboard
    Default,
    /// Multiline text keyboard
    Multiline,
    /// Number keyboard
    Number,
    /// Decimal number keyboard
    NumberDecimal,
    /// Phone number keyboard
    Phone,
    /// Email address keyboard
    EmailAddress,
    /// Password keyboard
    Password,
    /// URL keyboard
    Url,
}

/// ### LineEdit for single-line text input with validation and signals.
///
/// LineEdit extends Control with comprehensive text input functionality,
/// providing text validation, placeholder text, input signals, cursor
/// management, and selection handling. It maintains full compatibility
/// with Godot's LineEdit class while ensuring efficient text processing
/// and responsive input handling.
///
/// ## Core Features
///
/// - **Text Input**: Single-line text editing with cursor support
/// - **Text Validation**: Custom validation patterns and constraints
/// - **Placeholder Text**: Hint text when input is empty
/// - **Input Signals**: Text change and input validation signals
/// - **Selection**: Text selection and clipboard operations
/// - **Cursor Management**: Cursor positioning and movement
/// - **Godot Compatibility**: API matching Godot's LineEdit
///
/// ## Text Properties
///
/// LineEdit provides comprehensive text input control:
/// - **Text Content**: Current text value
/// - **Placeholder**: Hint text for empty input
/// - **Max Length**: Maximum character limit
/// - **Editable**: Whether text can be modified
/// - **Secret**: Whether to hide text (password mode)
/// - **Alignment**: Text alignment within the control
/// - **Selection**: Text selection range
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::LineEdit;
/// # use verturion::core::signal::SignalManager;
/// // Create a line edit
/// let mut line_edit = LineEdit::new("NameInput");
/// let mut signal_manager = SignalManager::new();
///
/// // Configure input
/// line_edit.set_placeholder_text("Enter your name".to_string());
/// line_edit.set_max_length(50);
/// line_edit.set_text_align(TextAlign::Left);
///
/// // Register signals
/// signal_manager.register_signal(line_edit.get_text_changed_signal().clone());
/// signal_manager.register_signal(line_edit.get_text_submitted_signal().clone());
///
/// // Set text
/// line_edit.set_text("John Doe".to_string());
///
/// assert_eq!(line_edit.get_text(), "John Doe");
/// assert_eq!(line_edit.get_placeholder_text(), "Enter your name");
/// assert!(line_edit.is_editable());
/// ```
#[derive(Debug, Clone)]
pub struct LineEdit {
    /// Base Control functionality
    base: Control,
    /// Current text content
    text: String,
    /// Placeholder text shown when empty
    placeholder_text: String,
    /// Maximum text length (0 = unlimited)
    max_length: i32,
    /// Whether the text can be edited
    editable: bool,
    /// Whether to hide text (password mode)
    secret: bool,
    /// Text alignment
    align: TextAlign,
    /// Current cursor position
    cursor_position: i32,
    /// Selection start position (-1 = no selection)
    selection_start: i32,
    /// Selection end position (-1 = no selection)
    selection_end: i32,
    /// Whether to select all text on focus
    select_all_on_focus: bool,
    /// Virtual keyboard type for mobile
    virtual_keyboard_type: VirtualKeyboardType,
    /// Whether to clear button is visible
    clear_button_enabled: bool,
    /// Whether the control is focused
    focused: bool,
    /// Signal emitted when text changes
    text_changed_signal: Signal,
    /// Signal emitted when text is submitted (Enter pressed)
    text_submitted_signal: Signal,
    /// Signal emitted when text change is confirmed
    text_change_rejected_signal: Signal,
}

impl LineEdit {
    /// ### Creates a new LineEdit with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this line edit control
    ///
    /// # Returns
    /// A new LineEdit instance with default text input properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::LineEdit;
    /// let line_edit = LineEdit::new("TextInput");
    /// assert_eq!(line_edit.get_name(), "TextInput");
    /// assert_eq!(line_edit.get_text(), "");
    /// assert!(line_edit.is_editable());
    /// assert!(!line_edit.is_secret());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        let control = Control::new(name);
        let node_id = control.base().get_id();
        Self {
            base: control,
            text: String::new(),
            placeholder_text: String::new(),
            max_length: 0,
            editable: true,
            secret: false,
            align: TextAlign::Left,
            cursor_position: 0,
            selection_start: -1,
            selection_end: -1,
            select_all_on_focus: false,
            virtual_keyboard_type: VirtualKeyboardType::Default,
            clear_button_enabled: false,
            focused: false,
            text_changed_signal: Signal::new("text_changed", node_id),
            text_submitted_signal: Signal::new("text_submitted", node_id),
            text_change_rejected_signal: Signal::new("text_change_rejected", node_id),
        }
    }

    /// ### Gets the current text content.
    ///
    /// # Returns
    /// The current text in the line edit.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::LineEdit;
    /// let mut line_edit = LineEdit::new("Input");
    /// line_edit.set_text("Hello".to_string());
    /// assert_eq!(line_edit.get_text(), "Hello");
    /// ```
    #[inline]
    pub fn get_text(&self) -> &str {
        &self.text
    }

    /// ### Sets the text content.
    ///
    /// # Parameters
    /// - `text`: The new text content
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::LineEdit;
    /// let mut line_edit = LineEdit::new("Input");
    /// line_edit.set_text("New text".to_string());
    /// assert_eq!(line_edit.get_text(), "New text");
    /// ```
    #[inline]
    pub fn set_text(&mut self, text: String) {
        let new_text = if self.max_length > 0 && text.len() > self.max_length as usize {
            text.chars().take(self.max_length as usize).collect()
        } else {
            text
        };

        if new_text != self.text {
            self.text = new_text;
            self.cursor_position = self.text.len() as i32;
            self.clear_selection();
        }
    }

    /// ### Gets the placeholder text.
    ///
    /// # Returns
    /// The placeholder text shown when the input is empty.
    #[inline]
    pub fn get_placeholder_text(&self) -> &str {
        &self.placeholder_text
    }

    /// ### Sets the placeholder text.
    ///
    /// # Parameters
    /// - `text`: The placeholder text to display when empty
    #[inline]
    pub fn set_placeholder_text(&mut self, text: String) {
        self.placeholder_text = text;
    }

    /// ### Gets the maximum text length.
    ///
    /// # Returns
    /// The maximum number of characters allowed (0 = unlimited).
    #[inline]
    pub fn get_max_length(&self) -> i32 {
        self.max_length
    }

    /// ### Sets the maximum text length.
    ///
    /// # Parameters
    /// - `length`: Maximum number of characters (0 = unlimited)
    #[inline]
    pub fn set_max_length(&mut self, length: i32) {
        self.max_length = length.max(0);

        // Truncate existing text if necessary
        if self.max_length > 0 && self.text.len() > self.max_length as usize {
            self.text = self.text.chars().take(self.max_length as usize).collect();
            self.cursor_position = self.text.len() as i32;
            self.clear_selection();
        }
    }

    /// ### Checks if the text is editable.
    ///
    /// # Returns
    /// True if the text can be edited, false otherwise.
    #[inline]
    pub fn is_editable(&self) -> bool {
        self.editable
    }

    /// ### Sets whether the text is editable.
    ///
    /// # Parameters
    /// - `editable`: Whether the text can be edited
    #[inline]
    pub fn set_editable(&mut self, editable: bool) {
        self.editable = editable;
        if !editable {
            self.clear_selection();
        }
    }

    /// ### Checks if the text is in secret mode.
    ///
    /// # Returns
    /// True if text is hidden (password mode), false otherwise.
    #[inline]
    pub fn is_secret(&self) -> bool {
        self.secret
    }

    /// ### Sets whether the text is in secret mode.
    ///
    /// # Parameters
    /// - `secret`: Whether to hide the text (password mode)
    #[inline]
    pub fn set_secret(&mut self, secret: bool) {
        self.secret = secret;
    }

    /// ### Gets the text alignment.
    ///
    /// # Returns
    /// The current text alignment.
    #[inline]
    pub fn get_text_align(&self) -> TextAlign {
        self.align
    }

    /// ### Sets the text alignment.
    ///
    /// # Parameters
    /// - `align`: The text alignment mode
    #[inline]
    pub fn set_text_align(&mut self, align: TextAlign) {
        self.align = align;
    }

    /// ### Gets the current cursor position.
    ///
    /// # Returns
    /// The cursor position in characters from the start.
    #[inline]
    pub fn get_cursor_position(&self) -> i32 {
        self.cursor_position
    }

    /// ### Sets the cursor position.
    ///
    /// # Parameters
    /// - `position`: The new cursor position
    #[inline]
    pub fn set_cursor_position(&mut self, position: i32) {
        self.cursor_position = position.max(0).min(self.text.len() as i32);
        self.clear_selection();
    }

    /// ### Checks if text is selected.
    ///
    /// # Returns
    /// True if there is a text selection, false otherwise.
    #[inline]
    pub fn has_selection(&self) -> bool {
        self.selection_start >= 0 && self.selection_end >= 0 && self.selection_start != self.selection_end
    }

    /// ### Gets the selected text.
    ///
    /// # Returns
    /// The currently selected text, or empty string if no selection.
    #[inline]
    pub fn get_selected_text(&self) -> String {
        if !self.has_selection() {
            return String::new();
        }

        let start = self.selection_start.min(self.selection_end) as usize;
        let end = self.selection_start.max(self.selection_end) as usize;

        self.text.chars().skip(start).take(end - start).collect()
    }

    /// ### Selects text in the specified range.
    ///
    /// # Parameters
    /// - `start`: Start position of selection
    /// - `end`: End position of selection
    #[inline]
    pub fn select(&mut self, start: i32, end: i32) {
        let text_len = self.text.len() as i32;
        self.selection_start = start.max(0).min(text_len);
        self.selection_end = end.max(0).min(text_len);
        self.cursor_position = self.selection_end;
    }

    /// ### Selects all text.
    #[inline]
    pub fn select_all(&mut self) {
        if !self.text.is_empty() {
            self.selection_start = 0;
            self.selection_end = self.text.len() as i32;
            self.cursor_position = self.selection_end;
        }
    }

    /// ### Clears the text selection.
    #[inline]
    pub fn clear_selection(&mut self) {
        self.selection_start = -1;
        self.selection_end = -1;
    }

    /// ### Checks if select all on focus is enabled.
    ///
    /// # Returns
    /// True if text is selected when the control gains focus.
    #[inline]
    pub fn is_select_all_on_focus(&self) -> bool {
        self.select_all_on_focus
    }

    /// ### Sets whether to select all text on focus.
    ///
    /// # Parameters
    /// - `enable`: Whether to select all text when focused
    #[inline]
    pub fn set_select_all_on_focus(&mut self, enable: bool) {
        self.select_all_on_focus = enable;
    }

    /// ### Gets the virtual keyboard type.
    ///
    /// # Returns
    /// The virtual keyboard type for mobile devices.
    #[inline]
    pub fn get_virtual_keyboard_type(&self) -> VirtualKeyboardType {
        self.virtual_keyboard_type
    }

    /// ### Sets the virtual keyboard type.
    ///
    /// # Parameters
    /// - `keyboard_type`: The virtual keyboard type for mobile
    #[inline]
    pub fn set_virtual_keyboard_type(&mut self, keyboard_type: VirtualKeyboardType) {
        self.virtual_keyboard_type = keyboard_type;
    }

    /// ### Checks if the clear button is enabled.
    ///
    /// # Returns
    /// True if the clear button is visible when text is present.
    #[inline]
    pub fn is_clear_button_enabled(&self) -> bool {
        self.clear_button_enabled
    }

    /// ### Sets whether the clear button is enabled.
    ///
    /// # Parameters
    /// - `enabled`: Whether to show the clear button
    #[inline]
    pub fn set_clear_button_enabled(&mut self, enabled: bool) {
        self.clear_button_enabled = enabled;
    }

    /// ### Checks if the control is focused.
    ///
    /// # Returns
    /// True if the control has input focus.
    #[inline]
    pub fn is_focused(&self) -> bool {
        self.focused
    }

    /// ### Gets the text changed signal.
    ///
    /// # Returns
    /// A reference to the text changed signal.
    #[inline]
    pub fn get_text_changed_signal(&self) -> &Signal {
        &self.text_changed_signal
    }

    /// ### Gets the text submitted signal.
    ///
    /// # Returns
    /// A reference to the text submitted signal.
    #[inline]
    pub fn get_text_submitted_signal(&self) -> &Signal {
        &self.text_submitted_signal
    }

    /// ### Gets the text change rejected signal.
    ///
    /// # Returns
    /// A reference to the text change rejected signal.
    #[inline]
    pub fn get_text_change_rejected_signal(&self) -> &Signal {
        &self.text_change_rejected_signal
    }

    /// ### Inserts text at the current cursor position.
    ///
    /// # Parameters
    /// - `text`: The text to insert
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::LineEdit;
    /// # use verturion::core::signal::SignalManager;
    /// let mut line_edit = LineEdit::new("Input");
    /// let mut manager = SignalManager::new();
    /// line_edit.set_text("Hello".to_string());
    /// line_edit.set_cursor_position(5);
    /// line_edit.insert_text_at_cursor(" World".to_string(), &mut manager);
    /// assert_eq!(line_edit.get_text(), "Hello World");
    /// ```
    #[inline]
    pub fn insert_text_at_cursor(&mut self, text: String, signal_manager: &mut SignalManager) {
        if !self.editable {
            return;
        }

        // Remove selected text first if any
        if self.has_selection() {
            self.delete_selection(signal_manager);
        }

        // Check max length constraint
        let available_space = if self.max_length > 0 {
            (self.max_length as usize).saturating_sub(self.text.len())
        } else {
            usize::MAX
        };

        let insert_text = if text.len() > available_space {
            text.chars().take(available_space).collect()
        } else {
            text
        };

        if !insert_text.is_empty() {
            let cursor_pos = self.cursor_position as usize;
            let mut chars: Vec<char> = self.text.chars().collect();

            for (i, ch) in insert_text.chars().enumerate() {
                chars.insert(cursor_pos + i, ch);
            }

            let old_text = self.text.clone();
            self.text = chars.into_iter().collect();
            self.cursor_position += insert_text.len() as i32;

            // Emit text changed signal
            if self.text != old_text {
                let mut data = SignalData::empty();
                data.add_arg(Variant::from(self.text.clone()));
                signal_manager.emit(self.text_changed_signal.id(), data);
            }
        }
    }

    /// ### Deletes the selected text.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Returns
    /// True if text was deleted, false otherwise.
    #[inline]
    pub fn delete_selection(&mut self, signal_manager: &mut SignalManager) -> bool {
        if !self.editable || !self.has_selection() {
            return false;
        }

        let start = self.selection_start.min(self.selection_end) as usize;
        let end = self.selection_start.max(self.selection_end) as usize;

        let mut chars: Vec<char> = self.text.chars().collect();
        chars.drain(start..end);

        let old_text = self.text.clone();
        self.text = chars.into_iter().collect();
        self.cursor_position = start as i32;
        self.clear_selection();

        // Emit text changed signal
        if self.text != old_text {
            let mut data = SignalData::empty();
            data.add_arg(Variant::from(self.text.clone()));
            signal_manager.emit(self.text_changed_signal.id(), data);
        }

        true
    }

    /// ### Deletes a character at the cursor position.
    ///
    /// # Parameters
    /// - `forward`: If true, delete character after cursor; if false, before cursor
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Returns
    /// True if a character was deleted, false otherwise.
    #[inline]
    pub fn delete_char(&mut self, forward: bool, signal_manager: &mut SignalManager) -> bool {
        if !self.editable {
            return false;
        }

        // If there's a selection, delete it instead
        if self.has_selection() {
            return self.delete_selection(signal_manager);
        }

        let cursor_pos = self.cursor_position as usize;
        let mut chars: Vec<char> = self.text.chars().collect();

        let delete_pos = if forward {
            if cursor_pos >= chars.len() {
                return false;
            }
            cursor_pos
        } else {
            if cursor_pos == 0 {
                return false;
            }
            cursor_pos - 1
        };

        let old_text = self.text.clone();
        chars.remove(delete_pos);
        self.text = chars.into_iter().collect();

        if !forward {
            self.cursor_position -= 1;
        }

        // Emit text changed signal
        if self.text != old_text {
            let mut data = SignalData::empty();
            data.add_arg(Variant::from(self.text.clone()));
            signal_manager.emit(self.text_changed_signal.id(), data);
        }

        true
    }

    /// ### Clears all text.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn clear(&mut self, signal_manager: &mut SignalManager) {
        if !self.editable {
            return;
        }

        let old_text = self.text.clone();
        self.text.clear();
        self.cursor_position = 0;
        self.clear_selection();

        // Emit text changed signal
        if !old_text.is_empty() {
            let mut data = SignalData::empty();
            data.add_arg(Variant::from(self.text.clone()));
            signal_manager.emit(self.text_changed_signal.id(), data);
        }
    }

    /// ### Handles focus gain.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn grab_focus(&mut self, _signal_manager: &mut SignalManager) {
        self.focused = true;

        if self.select_all_on_focus && !self.text.is_empty() {
            self.select_all();
        }
    }

    /// ### Handles focus loss.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn release_focus(&mut self, _signal_manager: &mut SignalManager) {
        self.focused = false;
        self.clear_selection();
    }

    /// ### Submits the current text (simulates Enter key press).
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn submit_text(&mut self, signal_manager: &mut SignalManager) {
        let mut data = SignalData::empty();
        data.add_arg(Variant::from(self.text.clone()));
        signal_manager.emit(self.text_submitted_signal.id(), data);
    }

    /// ### Validates the current text content.
    ///
    /// This is a placeholder for custom validation logic.
    /// In a real implementation, this would check against validation patterns.
    ///
    /// # Returns
    /// True if the text is valid, false otherwise.
    #[inline]
    pub fn is_text_valid(&self) -> bool {
        // Basic validation - not empty if required
        // In a real implementation, this would use regex patterns or custom validators
        true
    }

    /// ### Moves the cursor by the specified offset.
    ///
    /// # Parameters
    /// - `offset`: Number of characters to move (negative for left, positive for right)
    /// - `select`: Whether to extend selection while moving
    #[inline]
    pub fn move_cursor(&mut self, offset: i32, select: bool) {
        let new_position = (self.cursor_position + offset).max(0).min(self.text.len() as i32);

        if select {
            if self.selection_start < 0 {
                self.selection_start = self.cursor_position;
            }
            self.selection_end = new_position;
        } else {
            self.clear_selection();
        }

        self.cursor_position = new_position;
    }

    /// ### Gets the display text (with secret characters if enabled).
    ///
    /// # Returns
    /// The text as it should be displayed (with bullets for secret mode).
    #[inline]
    pub fn get_display_text(&self) -> String {
        if self.secret {
            "•".repeat(self.text.len())
        } else {
            self.text.clone()
        }
    }

    /// ### Provides access to the base Control functionality.
    ///
    /// # Returns
    /// A reference to the underlying Control.
    #[inline]
    pub fn base(&self) -> &Control {
        &self.base
    }

    /// ### Provides mutable access to the base Control functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Control.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Control {
        &mut self.base
    }

    /// ### Gets the node name from the base Control.
    ///
    /// # Returns
    /// The name of this line edit control.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }
}

impl fmt::Display for LineEdit {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let display_text = if self.text.is_empty() {
            format!("\"{}\"", self.placeholder_text)
        } else if self.secret {
            "•".repeat(self.text.len())
        } else {
            format!("\"{}\"", self.text)
        };

        write!(f, "LineEdit({}, text: {}, editable: {}, focused: {})",
               self.get_name(),
               display_text,
               self.editable,
               self.focused)
    }
}

impl PartialEq for LineEdit {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for LineEdit {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::signal::SignalManager;

    #[test]
    fn test_line_edit_creation() {
        let line_edit = LineEdit::new("TestInput");
        assert_eq!(line_edit.get_name(), "TestInput");
        assert_eq!(line_edit.get_text(), "");
        assert_eq!(line_edit.get_placeholder_text(), "");
        assert_eq!(line_edit.get_max_length(), 0);
        assert!(line_edit.is_editable());
        assert!(!line_edit.is_secret());
        assert_eq!(line_edit.get_text_align(), TextAlign::Left);
        assert_eq!(line_edit.get_cursor_position(), 0);
        assert!(!line_edit.has_selection());
        assert!(!line_edit.is_select_all_on_focus());
        assert_eq!(line_edit.get_virtual_keyboard_type(), VirtualKeyboardType::Default);
        assert!(!line_edit.is_clear_button_enabled());
        assert!(!line_edit.is_focused());
    }

    #[test]
    fn test_line_edit_text_operations() {
        let mut line_edit = LineEdit::new("Input");

        // Test setting text
        line_edit.set_text("Hello World".to_string());
        assert_eq!(line_edit.get_text(), "Hello World");
        assert_eq!(line_edit.get_cursor_position(), 11);

        // Test display text (normal mode)
        assert_eq!(line_edit.get_display_text(), "Hello World");

        // Test secret mode
        line_edit.set_secret(true);
        assert!(line_edit.is_secret());
        assert_eq!(line_edit.get_display_text(), "•••••••••••");

        line_edit.set_secret(false);
        assert!(!line_edit.is_secret());
    }

    #[test]
    fn test_line_edit_properties() {
        let mut line_edit = LineEdit::new("Input");

        // Test placeholder
        line_edit.set_placeholder_text("Enter text here".to_string());
        assert_eq!(line_edit.get_placeholder_text(), "Enter text here");

        // Test max length
        line_edit.set_max_length(10);
        assert_eq!(line_edit.get_max_length(), 10);

        // Test negative max length clamping
        line_edit.set_max_length(-5);
        assert_eq!(line_edit.get_max_length(), 0);

        // Test editable
        line_edit.set_editable(false);
        assert!(!line_edit.is_editable());

        // Test text alignment
        line_edit.set_text_align(TextAlign::Center);
        assert_eq!(line_edit.get_text_align(), TextAlign::Center);

        line_edit.set_text_align(TextAlign::Right);
        assert_eq!(line_edit.get_text_align(), TextAlign::Right);

        // Test select all on focus
        line_edit.set_select_all_on_focus(true);
        assert!(line_edit.is_select_all_on_focus());

        // Test virtual keyboard type
        line_edit.set_virtual_keyboard_type(VirtualKeyboardType::EmailAddress);
        assert_eq!(line_edit.get_virtual_keyboard_type(), VirtualKeyboardType::EmailAddress);

        // Test clear button
        line_edit.set_clear_button_enabled(true);
        assert!(line_edit.is_clear_button_enabled());
    }

    #[test]
    fn test_line_edit_max_length_enforcement() {
        let mut line_edit = LineEdit::new("Input");
        line_edit.set_max_length(5);

        // Test setting text longer than max length
        line_edit.set_text("Hello World".to_string());
        assert_eq!(line_edit.get_text(), "Hello");
        assert_eq!(line_edit.get_cursor_position(), 5);

        // Test existing text truncation when max length is reduced
        line_edit.set_text("Test".to_string());
        line_edit.set_max_length(2);
        assert_eq!(line_edit.get_text(), "Te");
        assert_eq!(line_edit.get_cursor_position(), 2);
    }

    #[test]
    fn test_line_edit_cursor_operations() {
        let mut line_edit = LineEdit::new("Input");
        line_edit.set_text("Hello".to_string());

        // Test cursor positioning
        line_edit.set_cursor_position(3);
        assert_eq!(line_edit.get_cursor_position(), 3);

        // Test cursor bounds clamping
        line_edit.set_cursor_position(-5);
        assert_eq!(line_edit.get_cursor_position(), 0);

        line_edit.set_cursor_position(100);
        assert_eq!(line_edit.get_cursor_position(), 5);

        // Test cursor movement
        line_edit.set_cursor_position(2);
        line_edit.move_cursor(2, false);
        assert_eq!(line_edit.get_cursor_position(), 4);
        assert!(!line_edit.has_selection());

        // Test cursor movement with selection
        line_edit.set_cursor_position(1);
        line_edit.move_cursor(3, true);
        assert_eq!(line_edit.get_cursor_position(), 4);
        assert!(line_edit.has_selection());
        assert_eq!(line_edit.get_selected_text(), "ell");
    }

    #[test]
    fn test_line_edit_selection() {
        let mut line_edit = LineEdit::new("Input");
        line_edit.set_text("Hello World".to_string());

        // Test manual selection
        line_edit.select(0, 5);
        assert!(line_edit.has_selection());
        assert_eq!(line_edit.get_selected_text(), "Hello");
        assert_eq!(line_edit.get_cursor_position(), 5);

        // Test select all
        line_edit.select_all();
        assert!(line_edit.has_selection());
        assert_eq!(line_edit.get_selected_text(), "Hello World");
        assert_eq!(line_edit.get_cursor_position(), 11);

        // Test clear selection
        line_edit.clear_selection();
        assert!(!line_edit.has_selection());
        assert_eq!(line_edit.get_selected_text(), "");

        // Test selection bounds clamping
        line_edit.select(-5, 100);
        assert!(line_edit.has_selection());
        assert_eq!(line_edit.get_selected_text(), "Hello World");

        // Test reverse selection
        line_edit.select(8, 3);
        assert!(line_edit.has_selection());
        assert_eq!(line_edit.get_selected_text(), "lo Wo");
    }

    #[test]
    fn test_line_edit_text_insertion() {
        let mut line_edit = LineEdit::new("Input");
        let mut signal_manager = SignalManager::new();

        // Register signal
        signal_manager.register_signal(line_edit.get_text_changed_signal().clone());

        // Test basic insertion
        line_edit.set_text("Hello".to_string());
        line_edit.set_cursor_position(5);
        line_edit.insert_text_at_cursor(" World".to_string(), &mut signal_manager);
        assert_eq!(line_edit.get_text(), "Hello World");
        assert_eq!(line_edit.get_cursor_position(), 11);

        // Test insertion with selection (should replace)
        line_edit.select(6, 11);
        line_edit.insert_text_at_cursor("Rust".to_string(), &mut signal_manager);
        assert_eq!(line_edit.get_text(), "Hello Rust");
        assert!(!line_edit.has_selection());

        // Test insertion with max length
        line_edit.set_max_length(12);
        line_edit.set_cursor_position(10);
        line_edit.insert_text_at_cursor(" Programming".to_string(), &mut signal_manager);
        assert_eq!(line_edit.get_text(), "Hello Rust P"); // Truncated to max length

        // Test insertion when not editable
        line_edit.set_editable(false);
        let old_text = line_edit.get_text().to_string();
        line_edit.insert_text_at_cursor("Test".to_string(), &mut signal_manager);
        assert_eq!(line_edit.get_text(), old_text); // Should not change
    }

    #[test]
    fn test_line_edit_text_deletion() {
        let mut line_edit = LineEdit::new("Input");
        let mut signal_manager = SignalManager::new();

        // Register signal
        signal_manager.register_signal(line_edit.get_text_changed_signal().clone());

        line_edit.set_text("Hello World".to_string());

        // Test forward deletion
        line_edit.set_cursor_position(5);
        assert!(line_edit.delete_char(true, &mut signal_manager));
        assert_eq!(line_edit.get_text(), "HelloWorld");
        assert_eq!(line_edit.get_cursor_position(), 5);

        // Test backward deletion
        assert!(line_edit.delete_char(false, &mut signal_manager));
        assert_eq!(line_edit.get_text(), "HellWorld");
        assert_eq!(line_edit.get_cursor_position(), 4);

        // Test deletion at boundaries
        line_edit.set_cursor_position(0);
        assert!(!line_edit.delete_char(false, &mut signal_manager)); // Can't delete before start

        line_edit.set_cursor_position(9);
        assert!(!line_edit.delete_char(true, &mut signal_manager)); // Can't delete after end

        // Test selection deletion
        line_edit.select(0, 4);
        assert!(line_edit.delete_selection(&mut signal_manager));
        assert_eq!(line_edit.get_text(), "World");
        assert!(!line_edit.has_selection());
        assert_eq!(line_edit.get_cursor_position(), 0);

        // Test deletion when not editable
        line_edit.set_editable(false);
        assert!(!line_edit.delete_char(true, &mut signal_manager));
        assert!(!line_edit.delete_selection(&mut signal_manager));
    }

    #[test]
    fn test_line_edit_clear() {
        let mut line_edit = LineEdit::new("Input");
        let mut signal_manager = SignalManager::new();

        // Register signal
        signal_manager.register_signal(line_edit.get_text_changed_signal().clone());

        line_edit.set_text("Hello World".to_string());
        line_edit.select(3, 8);

        // Test clear
        line_edit.clear(&mut signal_manager);
        assert_eq!(line_edit.get_text(), "");
        assert_eq!(line_edit.get_cursor_position(), 0);
        assert!(!line_edit.has_selection());

        // Test clear when not editable
        line_edit.set_text("Test".to_string());
        line_edit.set_editable(false);
        line_edit.clear(&mut signal_manager);
        assert_eq!(line_edit.get_text(), "Test"); // Should not change
    }

    #[test]
    fn test_line_edit_focus_handling() {
        let mut line_edit = LineEdit::new("Input");
        let mut signal_manager = SignalManager::new();

        line_edit.set_text("Hello".to_string());
        line_edit.set_select_all_on_focus(true);

        // Test focus gain
        line_edit.grab_focus(&mut signal_manager);
        assert!(line_edit.is_focused());
        assert!(line_edit.has_selection());
        assert_eq!(line_edit.get_selected_text(), "Hello");

        // Test focus loss
        line_edit.release_focus(&mut signal_manager);
        assert!(!line_edit.is_focused());
        assert!(!line_edit.has_selection());

        // Test focus without select all
        line_edit.set_select_all_on_focus(false);
        line_edit.grab_focus(&mut signal_manager);
        assert!(line_edit.is_focused());
        assert!(!line_edit.has_selection());
    }

    #[test]
    fn test_line_edit_text_submission() {
        let mut line_edit = LineEdit::new("Input");
        let mut signal_manager = SignalManager::new();

        // Register signal
        signal_manager.register_signal(line_edit.get_text_submitted_signal().clone());

        line_edit.set_text("Hello World".to_string());

        // Test text submission
        line_edit.submit_text(&mut signal_manager);
        // Signal should be emitted (tested by signal manager)
    }

    #[test]
    fn test_line_edit_validation() {
        let line_edit = LineEdit::new("Input");

        // Test basic validation (always true in current implementation)
        assert!(line_edit.is_text_valid());
    }

    #[test]
    fn test_line_edit_base_access() {
        let mut line_edit = LineEdit::new("BaseTest");

        // Test base access
        assert_eq!(line_edit.base().base().get_name(), "BaseTest");

        // Test mutable base access
        line_edit.base_mut().base_mut().set_name("NewName");
        assert_eq!(line_edit.get_name(), "NewName");
    }

    #[test]
    fn test_line_edit_equality() {
        let line_edit1 = LineEdit::new("Input1");
        let line_edit2 = LineEdit::new("Input2");
        let line_edit1_clone = line_edit1.clone();

        // Same line edit should be equal
        assert_eq!(line_edit1, line_edit1_clone);

        // Different line edits should not be equal
        assert_ne!(line_edit1, line_edit2);
    }

    #[test]
    fn test_line_edit_display() {
        let mut line_edit = LineEdit::new("DisplayTest");

        // Test empty display
        line_edit.set_placeholder_text("Enter text".to_string());
        let display_str = format!("{}", line_edit);
        assert!(display_str.contains("DisplayTest"));
        assert!(display_str.contains("Enter text"));
        assert!(display_str.contains("editable: true"));
        assert!(display_str.contains("focused: false"));

        // Test with text
        line_edit.set_text("Hello".to_string());
        line_edit.grab_focus(&mut SignalManager::new());
        let display_str = format!("{}", line_edit);
        assert!(display_str.contains("Hello"));
        assert!(display_str.contains("focused: true"));

        // Test secret mode display
        line_edit.set_secret(true);
        let display_str = format!("{}", line_edit);
        assert!(display_str.contains("•••••"));
        assert!(!display_str.contains("Hello"));
    }

    #[test]
    fn test_text_align_modes() {
        // Test all text alignment modes for completeness
        assert_eq!(TextAlign::Left, TextAlign::Left);
        assert_ne!(TextAlign::Left, TextAlign::Center);
        assert_ne!(TextAlign::Center, TextAlign::Right);
    }

    #[test]
    fn test_virtual_keyboard_types() {
        // Test all virtual keyboard types for completeness
        assert_eq!(VirtualKeyboardType::Default, VirtualKeyboardType::Default);
        assert_ne!(VirtualKeyboardType::Default, VirtualKeyboardType::Number);
        assert_ne!(VirtualKeyboardType::EmailAddress, VirtualKeyboardType::Password);
    }

    #[test]
    fn test_complex_line_edit_scenario() {
        let mut line_edit = LineEdit::new("ComplexInput");
        let mut signal_manager = SignalManager::new();

        // Register all signals
        signal_manager.register_signal(line_edit.get_text_changed_signal().clone());
        signal_manager.register_signal(line_edit.get_text_submitted_signal().clone());
        signal_manager.register_signal(line_edit.get_text_change_rejected_signal().clone());

        // Configure line edit for email input
        line_edit.set_placeholder_text("Enter your email".to_string());
        line_edit.set_max_length(50);
        line_edit.set_virtual_keyboard_type(VirtualKeyboardType::EmailAddress);
        line_edit.set_clear_button_enabled(true);
        line_edit.set_select_all_on_focus(true);

        // Simulate user interaction
        line_edit.grab_focus(&mut signal_manager);
        assert!(line_edit.is_focused());

        // Type email address
        line_edit.insert_text_at_cursor("<EMAIL>".to_string(), &mut signal_manager);
        assert_eq!(line_edit.get_text(), "<EMAIL>");

        // Select domain part and replace
        line_edit.select(5, 16);
        assert_eq!(line_edit.get_selected_text(), "example.com");

        line_edit.insert_text_at_cursor("gmail.com".to_string(), &mut signal_manager);
        assert_eq!(line_edit.get_text(), "<EMAIL>");

        // Move cursor and add plus addressing
        line_edit.set_cursor_position(4);
        line_edit.insert_text_at_cursor("+test".to_string(), &mut signal_manager);
        assert_eq!(line_edit.get_text(), "<EMAIL>");

        // Test validation and submission
        assert!(line_edit.is_text_valid());
        line_edit.submit_text(&mut signal_manager);

        // Test clear functionality
        line_edit.clear(&mut signal_manager);
        assert_eq!(line_edit.get_text(), "");
        assert_eq!(line_edit.get_cursor_position(), 0);

        // Verify configuration is maintained
        assert_eq!(line_edit.get_placeholder_text(), "Enter your email");
        assert_eq!(line_edit.get_max_length(), 50);
        assert_eq!(line_edit.get_virtual_keyboard_type(), VirtualKeyboardType::EmailAddress);
        assert!(line_edit.is_clear_button_enabled());
        assert!(line_edit.is_select_all_on_focus());
        assert!(line_edit.is_focused());
    }
}
