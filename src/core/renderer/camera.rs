//! Camera rendering system for Verturion graphics.
//!
//! This module provides camera functionality for 2D rendering including
//! viewport management, coordinate transformations, and camera controls.

use crate::core::math::{Vector2, Transform2D};
use crate::core::scene::nodes::Camera2D;

/// ### 2D camera renderer for viewport management.
pub struct Camera2DRenderer {
    /// Camera position in world space
    position: Vector2,
    /// Camera zoom level
    zoom: Vector2,
    /// Camera rotation in radians
    rotation: f32,
    /// Viewport size in pixels
    viewport_size: Vector2,
    /// Whether the camera is enabled
    enabled: bool,
    /// Camera transform matrix
    transform: Transform2D,
    /// Projection matrix for rendering
    projection_matrix: [[f32; 4]; 4],
}

impl Camera2DRenderer {
    /// ### Creates a new 2D camera renderer.
    pub fn new(viewport_size: Vector2) -> Self {
        let mut camera = Self {
            position: Vector2::ZERO,
            zoom: Vector2::ONE,
            rotation: 0.0,
            viewport_size,
            enabled: true,
            transform: Transform2D::IDENTITY,
            projection_matrix: Self::create_orthographic_matrix(viewport_size),
        };

        camera.update_transform();
        camera
    }

    /// ### Updates the camera from a Camera2D node.
    pub fn update_from_camera2d(&mut self, camera: &Camera2D) {
        self.position = camera.get_position();
        self.zoom = camera.get_zoom();
        self.rotation = 0.0; // Camera2D doesn't have rotation in our implementation
        self.enabled = camera.is_current(); // Use is_current instead of is_enabled

        self.update_transform();
    }

    /// ### Sets the camera position.
    pub fn set_position(&mut self, position: Vector2) {
        self.position = position;
        self.update_transform();
    }

    /// ### Gets the camera position.
    pub fn get_position(&self) -> Vector2 {
        self.position
    }

    /// ### Sets the camera zoom.
    pub fn set_zoom(&mut self, zoom: Vector2) {
        self.zoom = zoom;
        self.update_transform();
    }

    /// ### Gets the camera zoom.
    pub fn get_zoom(&self) -> Vector2 {
        self.zoom
    }

    /// ### Sets the camera rotation.
    pub fn set_rotation(&mut self, rotation: f32) {
        self.rotation = rotation;
        self.update_transform();
    }

    /// ### Gets the camera rotation.
    pub fn get_rotation(&self) -> f32 {
        self.rotation
    }

    /// ### Sets the viewport size.
    pub fn set_viewport_size(&mut self, size: Vector2) {
        self.viewport_size = size;
        self.projection_matrix = Self::create_orthographic_matrix(size);
        self.update_transform();
    }

    /// ### Gets the viewport size.
    pub fn get_viewport_size(&self) -> Vector2 {
        self.viewport_size
    }

    /// ### Checks if the camera is enabled.
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }

    /// ### Sets the camera enabled state.
    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }

    /// ### Gets the camera transform matrix.
    pub fn get_transform(&self) -> &Transform2D {
        &self.transform
    }

    /// ### Gets the projection matrix.
    pub fn get_projection_matrix(&self) -> &[[f32; 4]; 4] {
        &self.projection_matrix
    }

    /// ### Converts world coordinates to screen coordinates.
    pub fn world_to_screen(&self, world_pos: Vector2) -> Vector2 {
        let transformed = self.transform.transform_point(world_pos);
        Vector2::new(
            (transformed.x + self.viewport_size.x * 0.5),
            (self.viewport_size.y * 0.5 - transformed.y),
        )
    }

    /// ### Converts screen coordinates to world coordinates.
    pub fn screen_to_world(&self, screen_pos: Vector2) -> Vector2 {
        let normalized = Vector2::new(
            screen_pos.x - self.viewport_size.x * 0.5,
            self.viewport_size.y * 0.5 - screen_pos.y,
        );

        self.transform.inverse().transform_point(normalized)
    }

    /// ### Updates the camera transform matrix.
    fn update_transform(&mut self) {
        // Create camera transform (inverse of what we want to apply to objects)
        let mut transform = Transform2D::IDENTITY;

        // Apply zoom (inverse)
        transform = transform.scaled(Vector2::new(1.0 / self.zoom.x, 1.0 / self.zoom.y));

        // Apply rotation (inverse)
        transform = transform.rotated(-self.rotation);

        // Apply translation (inverse)
        transform = transform.translated(-self.position);

        self.transform = transform;
    }

    /// ### Creates an orthographic projection matrix.
    fn create_orthographic_matrix(viewport_size: Vector2) -> [[f32; 4]; 4] {
        let left = -viewport_size.x * 0.5;
        let right = viewport_size.x * 0.5;
        let bottom = -viewport_size.y * 0.5;
        let top = viewport_size.y * 0.5;
        let near = -1.0;
        let far = 1.0;

        [
            [2.0 / (right - left), 0.0, 0.0, -(right + left) / (right - left)],
            [0.0, 2.0 / (top - bottom), 0.0, -(top + bottom) / (top - bottom)],
            [0.0, 0.0, -2.0 / (far - near), -(far + near) / (far - near)],
            [0.0, 0.0, 0.0, 1.0],
        ]
    }

    /// ### Gets the camera bounds in world space.
    pub fn get_world_bounds(&self) -> (Vector2, Vector2) {
        let half_size = self.viewport_size * 0.5;
        let top_left = self.screen_to_world(Vector2::new(-half_size.x, -half_size.y));
        let bottom_right = self.screen_to_world(Vector2::new(half_size.x, half_size.y));

        (top_left, bottom_right)
    }

    /// ### Checks if a point is visible in the camera viewport.
    pub fn is_point_visible(&self, world_pos: Vector2) -> bool {
        let screen_pos = self.world_to_screen(world_pos);
        screen_pos.x >= 0.0 && screen_pos.x <= self.viewport_size.x &&
        screen_pos.y >= 0.0 && screen_pos.y <= self.viewport_size.y
    }

    /// ### Checks if a rectangle is visible in the camera viewport.
    pub fn is_rect_visible(&self, world_pos: Vector2, size: Vector2) -> bool {
        let screen_pos = self.world_to_screen(world_pos);
        let screen_size = size * self.zoom;

        // Check if rectangles overlap
        !(screen_pos.x + screen_size.x < 0.0 ||
          screen_pos.x > self.viewport_size.x ||
          screen_pos.y + screen_size.y < 0.0 ||
          screen_pos.y > self.viewport_size.y)
    }
}
