//! Window management for Verturion graphics rendering.
//!
//! This module provides window creation, event handling, and surface management
//! for the graphics rendering system using winit and wgpu.

use winit::{
    application::ApplicationHandler,
    event::{ElementState, KeyEvent, WindowEvent},
    event_loop::{ActiveE<PERSON>Loop, ControlFlow, EventLoop},
    keyboard::{<PERSON>C<PERSON>, PhysicalKey},
    window::{Window as WinitWindow, WindowId},
};
use wgpu::{Device, Queue, Surface, SurfaceConfiguration, TextureFormat};
use std::sync::Arc;

/// ### Window configuration for graphics rendering.
///
/// Defines the initial window properties and rendering settings.
#[derive(Debug, Clone)]
pub struct WindowConfig {
    /// Window title
    pub title: String,
    /// Window width in pixels
    pub width: u32,
    /// Window height in pixels
    pub height: u32,
    /// Whether the window is resizable
    pub resizable: bool,
    /// Whether to enable vsync
    pub vsync: bool,
    /// Background clear color (RGBA)
    pub clear_color: [f32; 4],
}

impl Default for WindowConfig {
    fn default() -> Self {
        Self {
            title: "Verturion Game Engine".to_string(),
            width: 1024,
            height: 768,
            resizable: true,
            vsync: true,
            clear_color: [0.1, 0.1, 0.1, 1.0], // Dark gray
        }
    }
}

/// ### Main window for graphics rendering.
///
/// Manages the window, graphics surface, and basic rendering setup.
pub struct Window {
    /// Winit window handle
    window: Option<Arc<WinitWindow>>,
    /// WGPU surface for rendering
    surface: Option<Surface<'static>>,
    /// WGPU device for graphics operations
    device: Option<Device>,
    /// WGPU queue for command submission
    queue: Option<Queue>,
    /// Surface configuration
    config: Option<SurfaceConfiguration>,
    /// Window configuration
    window_config: WindowConfig,
    /// Whether the window should close
    should_close: bool,
}

impl Window {
    /// ### Creates a new Window with the specified configuration.
    ///
    /// # Parameters
    /// - `config`: Window configuration settings
    ///
    /// # Returns
    /// A new Window instance ready for initialization.
    pub fn new(config: WindowConfig) -> Self {
        Self {
            window: None,
            surface: None,
            device: None,
            queue: None,
            config: None,
            window_config: config,
            should_close: false,
        }
    }

    /// ### Initializes the window and graphics context.
    ///
    /// # Parameters
    /// - `event_loop`: The winit event loop
    ///
    /// # Returns
    /// Result indicating success or failure of initialization.
    pub async fn initialize(&mut self, event_loop: &ActiveEventLoop) -> Result<(), Box<dyn std::error::Error>> {
        // Create window
        let window_attributes = WinitWindow::default_attributes()
            .with_title(&self.window_config.title)
            .with_inner_size(winit::dpi::LogicalSize::new(
                self.window_config.width,
                self.window_config.height,
            ))
            .with_resizable(self.window_config.resizable);

        let window = Arc::new(event_loop.create_window(window_attributes)?);
        
        // Create WGPU instance
        let instance = wgpu::Instance::new(wgpu::InstanceDescriptor {
            backends: wgpu::Backends::PRIMARY,
            ..Default::default()
        });

        // Create surface
        let surface = instance.create_surface(window.clone())?;

        // Request adapter
        let adapter = instance
            .request_adapter(&wgpu::RequestAdapterOptions {
                power_preference: wgpu::PowerPreference::default(),
                compatible_surface: Some(&surface),
                force_fallback_adapter: false,
            })
            .await
            .ok_or("Failed to find suitable adapter")?;

        // Request device and queue
        let (device, queue) = adapter
            .request_device(
                &wgpu::DeviceDescriptor {
                    label: None,
                    required_features: wgpu::Features::empty(),
                    required_limits: wgpu::Limits::default(),
                    memory_hints: wgpu::MemoryHints::default(),
                },
                None,
            )
            .await?;

        // Configure surface
        let surface_caps = surface.get_capabilities(&adapter);
        let surface_format = surface_caps
            .formats
            .iter()
            .find(|f| f.is_srgb())
            .copied()
            .unwrap_or(surface_caps.formats[0]);

        let config = SurfaceConfiguration {
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
            format: surface_format,
            width: self.window_config.width,
            height: self.window_config.height,
            present_mode: if self.window_config.vsync {
                wgpu::PresentMode::AutoVsync
            } else {
                wgpu::PresentMode::AutoNoVsync
            },
            alpha_mode: surface_caps.alpha_modes[0],
            view_formats: vec![],
            desired_maximum_frame_latency: 2,
        };

        surface.configure(&device, &config);

        // Store everything
        self.window = Some(window);
        self.surface = Some(surface);
        self.device = Some(device);
        self.queue = Some(queue);
        self.config = Some(config);

        Ok(())
    }

    /// ### Gets the window handle.
    pub fn window(&self) -> Option<&Arc<WinitWindow>> {
        self.window.as_ref()
    }

    /// ### Gets the WGPU device.
    pub fn device(&self) -> Option<&Device> {
        self.device.as_ref()
    }

    /// ### Gets the WGPU queue.
    pub fn queue(&self) -> Option<&Queue> {
        self.queue.as_ref()
    }

    /// ### Gets the surface configuration.
    pub fn config(&self) -> Option<&SurfaceConfiguration> {
        self.config.as_ref()
    }

    /// ### Gets the window configuration.
    pub fn window_config(&self) -> &WindowConfig {
        &self.window_config
    }

    /// ### Checks if the window should close.
    pub fn should_close(&self) -> bool {
        self.should_close
    }

    /// ### Requests the window to close.
    pub fn request_close(&mut self) {
        self.should_close = true;
    }

    /// ### Handles window resize.
    ///
    /// # Parameters
    /// - `new_size`: The new window size
    pub fn resize(&mut self, new_size: winit::dpi::PhysicalSize<u32>) {
        if let (Some(surface), Some(device), Some(config)) = 
            (&self.surface, &self.device, &mut self.config) {
            if new_size.width > 0 && new_size.height > 0 {
                config.width = new_size.width;
                config.height = new_size.height;
                surface.configure(device, config);
            }
        }
    }

    /// ### Begins a new frame for rendering.
    ///
    /// # Returns
    /// The surface texture for this frame, or None if unavailable.
    pub fn begin_frame(&self) -> Option<wgpu::SurfaceTexture> {
        if let Some(surface) = &self.surface {
            match surface.get_current_texture() {
                Ok(texture) => Some(texture),
                Err(wgpu::SurfaceError::Lost | wgpu::SurfaceError::Outdated) => {
                    // Surface needs to be reconfigured
                    None
                }
                Err(wgpu::SurfaceError::OutOfMemory) => {
                    eprintln!("Out of memory!");
                    None
                }
                Err(wgpu::SurfaceError::Timeout) => {
                    eprintln!("Surface timeout!");
                    None
                }
            }
        } else {
            None
        }
    }
}
