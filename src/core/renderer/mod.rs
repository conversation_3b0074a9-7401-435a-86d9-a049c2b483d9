//! Graphics rendering system for Verturion game engine.
//!
//! This module provides comprehensive graphics rendering capabilities for the Verturion
//! game engine, including window management, 2D rendering, UI element rendering, and
//! integration with the node system. It uses wgpu for cross-platform graphics rendering
//! with support for modern graphics APIs.

pub mod window;
pub mod renderer;
pub mod ui_renderer;
pub mod text_renderer;
pub mod shape_renderer;
pub mod camera;

// Re-export main types
pub use window::{Window, WindowConfig};
pub use renderer::{Render<PERSON>, RenderContext};
pub use ui_renderer::UIRenderer;
pub use text_renderer::TextRenderer;
pub use shape_renderer::ShapeRenderer;
pub use camera::Camera2DRenderer;
