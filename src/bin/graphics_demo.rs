//! Graphics Demo Application
//!
//! This application demonstrates the complete graphics rendering system for the
//! Verturion game engine, showcasing all implemented UI nodes with visual output
//! and interactive functionality.

use winit::{
    application::ApplicationHandler,
    event::{ElementState, KeyEvent, WindowEvent},
    event_loop::{<PERSON><PERSON><PERSON>Loop, ControlFlow, EventLoop},
    keyboard::{<PERSON><PERSON><PERSON>, PhysicalKey},
    window::WindowId,
};

use verturion::core::{
    renderer::{Window, WindowConfig, Renderer},
    scene::{Node, NodePath},
    scene::nodes::{
        Timer, AudioStreamPlayer2D, Camera2D, AnimationPlayer,
        Label, Button, LineEdit, ProgressBar, CheckBox,
        StaticBody2D,
    },
    signal::SignalManager,
    math::Vector2,
    variant::{Color, String as GodotString},
};

/// ### Graphics demo application.
pub struct GraphicsDemo {
    /// Main window for rendering
    window: Window,
    /// Graphics renderer
    renderer: Option<Renderer>,
    /// Root scene node
    root_node: Option<Node>,
    /// Signal manager for events
    signal_manager: SignalManager,
    /// Demo UI nodes
    demo_nodes: DemoNodes,
    /// Whether the application should exit
    should_exit: bool,
}

/// ### Collection of demo UI nodes.
pub struct DemoNodes {
    /// Demo label
    pub label: Label,
    /// Demo button
    pub button: Button,
    /// Demo line edit
    pub line_edit: LineEdit,
    /// Demo progress bar
    pub progress_bar: ProgressBar,
    /// Demo checkbox
    pub checkbox: CheckBox,
    /// Demo timer
    pub timer: Timer,
    /// Demo audio player
    pub audio_player: AudioStreamPlayer2D,
    /// Demo camera
    pub camera: Camera2D,
    /// Demo animation player
    pub animation_player: AnimationPlayer,
    /// Demo static body
    pub static_body: StaticBody2D,
}

impl DemoNodes {
    /// ### Creates a new collection of demo nodes.
    pub fn new() -> Self {
        // Create UI nodes
        let mut label = Label::new("DemoLabel");
        label.set_text(GodotString::from("Welcome to Verturion Graphics Demo!"));
        label.set_font_color(Color::new(1.0, 1.0, 0.0, 1.0)); // Yellow
        label.set_font_size(24);

        let mut button = Button::new("DemoButton");
        button.set_text(GodotString::from("Click Me!"));
        button.set_font_color(Color::WHITE);
        button.set_size(Vector2::new(120.0, 40.0));

        let mut line_edit = LineEdit::new("DemoLineEdit");
        line_edit.set_placeholder_text("Enter text here...".to_string());
        line_edit.set_max_length(50);

        let mut progress_bar = ProgressBar::new("DemoProgressBar");
        progress_bar.set_min_value(0.0);
        progress_bar.set_max_value(100.0);
        progress_bar.set_value(75.0, &mut SignalManager::new());

        let mut checkbox = CheckBox::new("DemoCheckBox");
        checkbox.set_text("Enable graphics effects".to_string());
        checkbox.set_checked(true, &mut SignalManager::new());

        // Create essential nodes
        let mut timer = Timer::new("DemoTimer");
        timer.set_wait_time(2.0);
        timer.set_autostart(true);

        let audio_player = AudioStreamPlayer2D::new("DemoAudio");
        let camera = Camera2D::new("DemoCamera");
        let animation_player = AnimationPlayer::new("DemoAnimationPlayer");
        let static_body = StaticBody2D::new("DemoStaticBody");

        Self {
            label,
            button,
            line_edit,
            progress_bar,
            checkbox,
            timer,
            audio_player,
            camera,
            animation_player,
            static_body,
        }
    }
}

impl GraphicsDemo {
    /// ### Creates a new graphics demo application.
    pub fn new() -> Self {
        let window_config = WindowConfig {
            title: "Verturion Graphics Demo - All Essential Nodes".to_string(),
            width: 1200,
            height: 800,
            resizable: true,
            vsync: true,
            clear_color: [0.2, 0.3, 0.4, 1.0], // Blue-gray background
        };

        let window = Window::new(window_config);
        let demo_nodes = DemoNodes::new();
        let mut signal_manager = SignalManager::new();

        // Register signals for demo nodes
        signal_manager.register_signal(demo_nodes.button.get_pressed_signal().clone());
        signal_manager.register_signal(demo_nodes.checkbox.get_toggled_signal().clone());
        signal_manager.register_signal(demo_nodes.timer.get_timeout_signal().clone());

        Self {
            window,
            renderer: None,
            root_node: None,
            signal_manager,
            demo_nodes,
            should_exit: false,
        }
    }

    /// ### Initializes the graphics demo.
    pub async fn initialize(&mut self, event_loop: &ActiveEventLoop) -> Result<(), Box<dyn std::error::Error>> {
        // Initialize window
        self.window.initialize(event_loop).await?;

        // Create renderer
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        let renderer = Renderer::new(device, queue, config.format).await?;
        self.renderer = Some(renderer);

        // Create root scene node
        let mut root = Node::new("Root");
        
        // Add demo nodes to scene tree (simplified for demo)
        // In a real implementation, you'd properly structure the scene tree
        self.root_node = Some(root);

        println!("Graphics demo initialized successfully!");
        println!("Window size: {}x{}", config.width, config.height);
        println!("Surface format: {:?}", config.format);

        Ok(())
    }

    /// ### Updates the demo state.
    pub fn update(&mut self, delta_time: f32) {
        // Update timer
        self.demo_nodes.timer.update(delta_time, &mut self.signal_manager);

        // Update progress bar (animate it)
        let current_value = self.demo_nodes.progress_bar.get_value();
        let new_value = (current_value + delta_time * 10.0) % 100.0;
        self.demo_nodes.progress_bar.set_value(new_value, &mut self.signal_manager);

        // Update animation player
        self.demo_nodes.animation_player.update(delta_time, &mut self.signal_manager);

        // Process signals
        self.signal_manager.process_pending_signals();
    }

    /// ### Renders the demo.
    pub fn render(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(renderer) = &mut self.renderer {
            renderer.render_frame(&self.window, self.root_node.as_ref())?;
            
            // For now, we'll just print what we're rendering
            self.print_rendering_info();
        }
        Ok(())
    }

    /// ### Prints information about what's being rendered.
    fn print_rendering_info(&self) {
        static mut FRAME_COUNT: u32 = 0;
        unsafe {
            FRAME_COUNT += 1;
            if FRAME_COUNT % 60 == 0 { // Print every 60 frames
                println!("\n=== Rendering Frame {} ===", FRAME_COUNT);
                println!("Label: '{}'", self.demo_nodes.label.get_text().as_str());
                println!("Button: '{}' (pressed: {})", 
                         self.demo_nodes.button.get_text().as_str(),
                         self.demo_nodes.button.is_pressed());
                println!("LineEdit: '{}'", self.demo_nodes.line_edit.get_text());
                println!("ProgressBar: {:.1}%", self.demo_nodes.progress_bar.get_percentage() * 100.0);
                println!("CheckBox: '{}' (checked: {})", 
                         self.demo_nodes.checkbox.get_text(),
                         self.demo_nodes.checkbox.is_checked());
                println!("Timer: {:.2}s remaining", self.demo_nodes.timer.get_time_left());
                println!("Camera: enabled={}", self.demo_nodes.camera.is_enabled());
                println!("AnimationPlayer: playing={}", self.demo_nodes.animation_player.is_playing());
            }
        }
    }

    /// ### Handles window events.
    pub fn handle_window_event(&mut self, event: &WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                self.should_exit = true;
            }
            WindowEvent::Resized(physical_size) => {
                self.window.resize(*physical_size);
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state == ElementState::Pressed {
                    match event.physical_key {
                        PhysicalKey::Code(KeyCode::Escape) => {
                            self.should_exit = true;
                        }
                        PhysicalKey::Code(KeyCode::Space) => {
                            // Simulate button press
                            self.demo_nodes.button.emit_pressed(&mut self.signal_manager);
                            println!("Button pressed via spacebar!");
                        }
                        PhysicalKey::Code(KeyCode::KeyT) => {
                            // Toggle checkbox
                            self.demo_nodes.checkbox.toggle(&mut self.signal_manager);
                            println!("Checkbox toggled via 'T' key!");
                        }
                        _ => {}
                    }
                }
            }
            WindowEvent::MouseInput { button, state, .. } => {
                if *state == ElementState::Pressed {
                    println!("Mouse button {:?} pressed", button);
                    // In a real implementation, you'd check if the mouse is over UI elements
                    // and trigger their interactions
                }
            }
            _ => {}
        }
    }

    /// ### Checks if the application should exit.
    pub fn should_exit(&self) -> bool {
        self.should_exit || self.window.should_close()
    }
}

impl ApplicationHandler for GraphicsDemo {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.renderer.is_none() {
            // Initialize on first resume
            pollster::block_on(async {
                if let Err(e) = self.initialize(event_loop).await {
                    eprintln!("Failed to initialize graphics demo: {}", e);
                    event_loop.exit();
                }
            });
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {
        self.handle_window_event(&event);
        
        if self.should_exit() {
            event_loop.exit();
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        // Update and render
        self.update(1.0 / 60.0); // Assume 60 FPS for now
        
        if let Err(e) = self.render() {
            eprintln!("Render error: {}", e);
        }
        
        // Request redraw
        if let Some(window) = self.window.window() {
            window.request_redraw();
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎮 Verturion Graphics Demo - Starting...");
    println!("======================================");
    println!("Controls:");
    println!("  ESC - Exit application");
    println!("  SPACE - Simulate button press");
    println!("  T - Toggle checkbox");
    println!("  Mouse - Click to interact (future)");
    println!("======================================\n");

    let event_loop = EventLoop::new()?;
    event_loop.set_control_flow(ControlFlow::Poll);

    let mut app = GraphicsDemo::new();
    event_loop.run_app(&mut app)?;

    println!("\n🎮 Verturion Graphics Demo - Exiting...");
    Ok(())
}
