//! Signal System Demo
//!
//! This example demonstrates the comprehensive Godot-compatible signal system
//! including signal emission, connection management, callable targets, and
//! practical game development scenarios with UI interactions and physics events.

use verturion::core::signal::{
    SignalManager, Signal, SignalData, Callable, ConnectionFlags
};
use verturion::core::scene::nodes::ui::But<PERSON>;
use verturion::core::scene::nodes::physics::Area2D;
use verturion::core::scene::node::NodeId;
use verturion::core::variant::Variant;
use std::cell::RefCell;
use std::rc::Rc;

fn main() {
    println!("=== Verturion Signal System Demo ===\n");

    // Demonstrate basic signal functionality
    demo_basic_signals();

    // Demonstrate callable types
    demo_callable_types();

    // Demonstrate button signals
    demo_button_signals();

    // Demonstrate area signals
    demo_area_signals();

    // Demonstrate complex signal scenarios
    demo_complex_scenarios();
}

fn demo_basic_signals() {
    println!("1. Basic Signal System:");

    let mut signal_manager = SignalManager::new();

    // Create a simple signal
    let owner = NodeId::new();
    let health_changed_signal = Signal::new("health_changed", owner);
    let signal_id = health_changed_signal.id();

    println!("   Created signal: {}", health_changed_signal);
    signal_manager.register_signal(health_changed_signal);

    // Create a callable to handle the signal
    let health_display = Rc::new(RefCell::new(String::from("Health: 100")));
    let health_display_clone = health_display.clone();

    let health_callable = Callable::function("update_health_display", move |data| {
        if let Some(new_health) = data.get_arg(0) {
            if let Some(health_value) = new_health.as_int() {
                let mut display = health_display_clone.borrow_mut();
                *display = format!("Health: {}", health_value);
                println!("   Health display updated: {}", *display);
                return Ok(Some(Variant::from(true)));
            }
        }
        Ok(Some(Variant::from(false)))
    });

    // Connect the callable to the signal
    let connection_id = signal_manager.connect(signal_id, health_callable, ConnectionFlags::default())
        .expect("Failed to connect signal");

    println!("   Connected callable with ID: {}", connection_id);

    // Emit the signal with health data
    let mut signal_data = SignalData::empty();
    signal_data.add_arg(Variant::from(75));

    let executed_count = signal_manager.emit(signal_id, signal_data);
    println!("   Signal emitted, {} callables executed", executed_count);
    println!("   Final health display: {}", health_display.borrow());
    println!();
}

fn demo_callable_types() {
    println!("2. Callable Types:");

    let mut signal_manager = SignalManager::new();
    let owner = NodeId::new();
    let test_signal = Signal::new("test_signal", owner);
    let signal_id = test_signal.id();
    signal_manager.register_signal(test_signal);

    // Function callable
    let function_callable = Callable::function("math_function", |data| {
        let sum: i64 = data.args().iter()
            .filter_map(|arg| arg.as_int())
            .sum();
        println!("   Function callable: sum = {}", sum);
        Ok(Some(Variant::from(sum)))
    });

    // Closure callable with captured state
    let multiplier = 3;
    let closure_callable = Callable::closure("multiplier_closure", move |data| {
        if let Some(value) = data.get_arg(0).and_then(|v| v.as_int()) {
            let result = value * multiplier;
            println!("   Closure callable: {} * {} = {}", value, multiplier, result);
            Ok(Some(Variant::from(result)))
        } else {
            Ok(None)
        }
    });

    // Node method callable (placeholder)
    let node_callable = Callable::node_method(NodeId::new(), "on_test_signal");

    // Connect all callables
    signal_manager.connect(signal_id, function_callable, ConnectionFlags::default()).unwrap();
    signal_manager.connect(signal_id, closure_callable, ConnectionFlags::default()).unwrap();
    signal_manager.connect(signal_id, node_callable, ConnectionFlags::default()).unwrap();

    // Emit signal with test data
    let data = SignalData::new(vec![
        Variant::from(10),
        Variant::from(20),
        Variant::from(30),
    ]);

    let executed = signal_manager.emit(signal_id, data);
    println!("   Emitted signal to {} callables (2 successful, 1 failed as expected)", executed);
    println!();
}

fn demo_button_signals() {
    println!("3. Button Signal Integration:");

    let mut signal_manager = SignalManager::new();
    let mut button = Button::new("PlayButton");

    // Register button signals
    signal_manager.register_signal(button.get_pressed_signal().clone());
    signal_manager.register_signal(button.get_released_signal().clone());
    signal_manager.register_signal(button.get_toggled_signal().clone());

    // Create game state to track button interactions
    let game_state = Rc::new(RefCell::new(GameState::new()));

    // Connect to pressed signal
    let game_state_pressed = game_state.clone();
    let pressed_callable = Callable::function("on_button_pressed", move |_| {
        let mut state = game_state_pressed.borrow_mut();
        state.button_press_count += 1;
        println!("   Button pressed! Total presses: {}", state.button_press_count);
        Ok(None)
    });

    // Connect to released signal
    let game_state_released = game_state.clone();
    let released_callable = Callable::function("on_button_released", move |_| {
        let mut state = game_state_released.borrow_mut();
        state.button_release_count += 1;
        println!("   Button released! Total releases: {}", state.button_release_count);
        Ok(None)
    });

    // Connect to toggled signal
    let game_state_toggled = game_state.clone();
    let toggled_callable = Callable::function("on_button_toggled", move |data| {
        if let Some(pressed) = data.get_arg(0).and_then(|v| v.as_bool()) {
            let mut state = game_state_toggled.borrow_mut();
            state.button_toggled = pressed;
            println!("   Button toggled! New state: {}", if pressed { "ON" } else { "OFF" });
        }
        Ok(None)
    });

    // Connect all button signals
    signal_manager.connect(button.get_pressed_signal().id(), pressed_callable, ConnectionFlags::default()).unwrap();
    signal_manager.connect(button.get_released_signal().id(), released_callable, ConnectionFlags::default()).unwrap();
    signal_manager.connect(button.get_toggled_signal().id(), toggled_callable, ConnectionFlags::default()).unwrap();

    // Test normal button presses
    println!("   Testing normal button mode:");
    button.emit_pressed(&mut signal_manager);
    button.emit_pressed(&mut signal_manager);

    // Test toggle mode
    println!("   Testing toggle mode:");
    button.set_toggle_mode(true);
    button.emit_pressed(&mut signal_manager); // Should toggle ON
    button.emit_pressed(&mut signal_manager); // Should toggle OFF

    println!("   Final game state: {:?}", *game_state.borrow());
    println!();
}

fn demo_area_signals() {
    println!("4. Area2D Signal Integration:");

    let mut signal_manager = SignalManager::new();
    let area = Area2D::new("TriggerZone");

    // Create signals for area events (in a real implementation, these would be built-in)
    let owner = area.base().base().get_id();
    let body_entered_signal = Signal::new("body_entered", owner);
    let body_exited_signal = Signal::new("body_exited", owner);
    let area_entered_signal = Signal::new("area_entered", owner);

    let body_entered_id = body_entered_signal.id();
    let body_exited_id = body_exited_signal.id();
    let _area_entered_id = area_entered_signal.id();

    signal_manager.register_signal(body_entered_signal);
    signal_manager.register_signal(body_exited_signal);
    signal_manager.register_signal(area_entered_signal);

    // Track area interactions
    let interaction_log = Rc::new(RefCell::new(Vec::<String>::new()));

    // Body entered handler
    let log_body_entered = interaction_log.clone();
    let body_entered_callable = Callable::function("on_body_entered", move |data| {
        if let Some(body_id) = data.get_arg(0).and_then(|v| v.as_int()) {
            let message = format!("Body {} entered the area", body_id);
            log_body_entered.borrow_mut().push(message.clone());
            println!("   {}", message);
        }
        Ok(None)
    });

    // Body exited handler
    let log_body_exited = interaction_log.clone();
    let body_exited_callable = Callable::function("on_body_exited", move |data| {
        if let Some(body_id) = data.get_arg(0).and_then(|v| v.as_int()) {
            let message = format!("Body {} exited the area", body_id);
            log_body_exited.borrow_mut().push(message.clone());
            println!("   {}", message);
        }
        Ok(None)
    });

    // Connect area signals
    signal_manager.connect(body_entered_id, body_entered_callable, ConnectionFlags::default()).unwrap();
    signal_manager.connect(body_exited_id, body_exited_callable, ConnectionFlags::default()).unwrap();

    // Simulate area interactions
    println!("   Simulating area interactions:");

    // Body enters
    let mut enter_data = SignalData::empty();
    enter_data.add_arg(Variant::from(12345));
    signal_manager.emit(body_entered_id, enter_data);

    // Another body enters
    let mut enter_data2 = SignalData::empty();
    enter_data2.add_arg(Variant::from(67890));
    signal_manager.emit(body_entered_id, enter_data2);

    // First body exits
    let mut exit_data = SignalData::empty();
    exit_data.add_arg(Variant::from(12345));
    signal_manager.emit(body_exited_id, exit_data);

    println!("   Total interactions logged: {}", interaction_log.borrow().len());
    println!();
}

fn demo_complex_scenarios() {
    println!("5. Complex Signal Scenarios:");

    let mut signal_manager = SignalManager::new();

    // Create a game event system
    let owner = NodeId::new();
    let game_event_signal = Signal::new("game_event", owner);
    let signal_id = game_event_signal.id();
    signal_manager.register_signal(game_event_signal);

    // One-shot connection (only fires once)
    let one_shot_callable = Callable::function("one_shot_handler", |data| {
        if let Some(event_type) = data.get_arg(0).and_then(|v| v.as_string()) {
            println!("   One-shot handler: {}", event_type.as_str());
        }
        Ok(None)
    });

    // Persistent connection
    let persistent_callable = Callable::function("persistent_handler", |data| {
        if let Some(event_type) = data.get_arg(0).and_then(|v| v.as_string()) {
            println!("   Persistent handler: {}", event_type.as_str());
        }
        Ok(None)
    });

    // Connect with different flags
    signal_manager.connect(signal_id, one_shot_callable, ConnectionFlags::one_shot()).unwrap();
    signal_manager.connect(signal_id, persistent_callable, ConnectionFlags::default()).unwrap();

    println!("   Connected one-shot and persistent handlers");

    // Emit multiple events
    println!("   Emitting first event:");
    let data1 = SignalData::new(vec![Variant::from("player_died")]);
    signal_manager.emit(signal_id, data1);

    println!("   Emitting second event:");
    let data2 = SignalData::new(vec![Variant::from("level_completed")]);
    signal_manager.emit(signal_id, data2);

    println!("   One-shot handler should only appear in first event");

    // Demonstrate connection cleanup
    let initial_connections = signal_manager.connection_count();
    signal_manager.remove_node_signals(owner);
    let final_connections = signal_manager.connection_count();

    println!("   Cleaned up node signals: {} -> {} connections",
             initial_connections, final_connections);
    println!();
}

// Helper struct for tracking game state
#[derive(Debug)]
struct GameState {
    button_press_count: u32,
    button_release_count: u32,
    button_toggled: bool,
}

impl GameState {
    fn new() -> Self {
        Self {
            button_press_count: 0,
            button_release_count: 0,
            button_toggled: false,
        }
    }
}
