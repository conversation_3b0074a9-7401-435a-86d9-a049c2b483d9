//! Complete Game Systems Demo
//!
//! This example demonstrates the integration of all 8 essential nodes in the Verturion
//! game engine, showcasing a complete 2D game development workflow with UI feedback,
//! physics collision, and boolean input controls.
//!
//! Featured nodes:
//! - Timer: Time-based events and delays
//! - AudioStreamPlayer2D: Spatial audio and sound effects
//! - Camera2D: Viewport management and player following
//! - AnimationPlayer: Character animation and property tweening
//! - LineEdit: User input and text interaction
//! - ProgressBar: Value display with progress indication
//! - StaticBody2D: Immovable physics bodies with collision detection
//! - CheckBox: Boolean input control with toggle functionality

use std::rc::Rc;
use std::cell::RefCell;
use verturion::core::signal::{SignalManager, Callable, ConnectionFlags};
use verturion::core::scene::nodes::{
    Timer, AudioStreamPlayer2D, AttenuationModel, Camera2D,
    AnimationPlayer, Animation, AnimationTrack,
    LineEdit, TextAlign,
    ProgressBar, FillMode,
    StaticBody2D, PhysicsMaterial,
    CheckBox
};
use verturion::core::variant::Variant;
use verturion::core::math::Vector2;

fn main() {
    println!("🎮 Verturion Complete Game Systems Demo");
    println!("========================================");
    println!();

    demo_complete_rpg_game_system();

    println!();
    println!("✅ Complete game systems demo finished successfully!");
    println!("All 8 essential nodes working together seamlessly!");
}

fn demo_complete_rpg_game_system() {
    println!("🏰 Complete RPG Game System Demo:");
    println!("   Demonstrating all 8 essential nodes in a cohesive RPG experience");

    let mut signal_manager = SignalManager::new();

    // === CORE GAME SYSTEMS ===

    // Timer systems
    let mut game_timer = Timer::new("GameTimer");
    let mut respawn_timer = Timer::new("RespawnTimer");
    let mut save_timer = Timer::new("AutoSaveTimer");

    // Audio systems
    let mut ambient_audio = AudioStreamPlayer2D::new("AmbientAudio");
    let mut combat_audio = AudioStreamPlayer2D::new("CombatAudio");
    let mut ui_audio = AudioStreamPlayer2D::new("UIAudio");

    // Camera system
    let mut main_camera = Camera2D::new("MainCamera");

    // Animation systems
    let mut player_animator = AnimationPlayer::new("PlayerAnimator");
    let mut ui_animator = AnimationPlayer::new("UIAnimator");

    // === UI SYSTEMS ===

    // Text input systems
    let mut player_name_input = LineEdit::new("PlayerNameInput");
    let mut chat_input = LineEdit::new("ChatInput");
    let mut command_input = LineEdit::new("CommandInput");

    // Progress tracking systems
    let mut health_bar = ProgressBar::new("HealthBar");
    let mut mana_bar = ProgressBar::new("ManaBar");
    let mut experience_bar = ProgressBar::new("ExperienceBar");
    let mut loading_bar = ProgressBar::new("LoadingBar");

    // Settings and options
    let mut sound_checkbox = CheckBox::new("SoundCheckBox");
    let mut fullscreen_checkbox = CheckBox::new("FullscreenCheckBox");
    let mut autosave_checkbox = CheckBox::new("AutosaveCheckBox");
    let mut pvp_checkbox = CheckBox::new("PvPCheckBox");

    // === PHYSICS SYSTEMS ===

    // Level geometry
    let mut ground_platform = StaticBody2D::new("GroundPlatform");
    let mut wall_left = StaticBody2D::new("WallLeft");
    let mut wall_right = StaticBody2D::new("WallRight");
    let mut treasure_chest = StaticBody2D::new("TreasureChest");

    // === SYSTEM CONFIGURATION ===

    // Configure timers
    game_timer.set_wait_time(60.0); // 1 minute game session
    game_timer.set_one_shot(true);
    respawn_timer.set_wait_time(5.0);
    respawn_timer.set_one_shot(true);
    save_timer.set_wait_time(10.0); // Auto-save every 10 seconds
    save_timer.set_one_shot(false);

    // Configure audio
    ambient_audio.set_stream(Some("res://audio/forest_ambient.ogg".to_string()));
    ambient_audio.set_volume_db(-15.0);
    ambient_audio.set_bus("Ambient".to_string());
    ambient_audio.set_attenuation(AttenuationModel::Disabled);

    combat_audio.set_stream(Some("res://audio/sword_clash.wav".to_string()));
    combat_audio.set_volume_db(-5.0);
    combat_audio.set_bus("SFX".to_string());
    combat_audio.set_max_distance(200.0);
    combat_audio.set_attenuation(AttenuationModel::InverseDistance);

    ui_audio.set_stream(Some("res://audio/ui_click.wav".to_string()));
    ui_audio.set_volume_db(-8.0);
    ui_audio.set_bus("UI".to_string());
    ui_audio.set_attenuation(AttenuationModel::Disabled);

    // Configure camera
    main_camera.set_current(true);
    main_camera.set_zoom(Vector2::new(1.2, 1.2));
    main_camera.set_smoothing_enabled(true);
    main_camera.set_smoothing_speed(2.5);
    main_camera.set_limit_left(-2000.0);
    main_camera.set_limit_right(2000.0);
    main_camera.set_limit_top(-1000.0);
    main_camera.set_limit_bottom(1000.0);

    // Configure player animations
    let mut idle_animation = Animation::new("idle".to_string());
    idle_animation.set_length(2.0);
    idle_animation.set_loop(true);

    let mut idle_track = AnimationTrack::new("sprite.frame".to_string());
    idle_track.add_keyframe(0.0, Variant::Int(0));
    idle_track.add_keyframe(1.0, Variant::Int(1));
    idle_track.add_keyframe(2.0, Variant::Int(0));
    idle_animation.add_track(idle_track);

    let mut run_animation = Animation::new("run".to_string());
    run_animation.set_length(0.6);
    run_animation.set_loop(true);

    let mut run_track = AnimationTrack::new("sprite.frame".to_string());
    run_track.add_keyframe(0.0, Variant::Int(2));
    run_track.add_keyframe(0.15, Variant::Int(3));
    run_track.add_keyframe(0.3, Variant::Int(4));
    run_track.add_keyframe(0.45, Variant::Int(5));
    run_track.add_keyframe(0.6, Variant::Int(2));
    run_animation.add_track(run_track);

    let mut attack_animation = Animation::new("attack".to_string());
    attack_animation.set_length(0.8);
    attack_animation.set_loop(false);

    let mut attack_track = AnimationTrack::new("sprite.frame".to_string());
    attack_track.add_keyframe(0.0, Variant::Int(6));
    attack_track.add_keyframe(0.2, Variant::Int(7));
    attack_track.add_keyframe(0.4, Variant::Int(8));
    attack_track.add_keyframe(0.6, Variant::Int(9));
    attack_track.add_keyframe(0.8, Variant::Int(0));
    attack_animation.add_track(attack_track);

    player_animator.add_animation(idle_animation);
    player_animator.add_animation(run_animation);
    player_animator.add_animation(attack_animation);
    player_animator.set_autoplay("idle".to_string());

    // Configure UI animations
    let mut ui_fade = Animation::new("ui_fade".to_string());
    ui_fade.set_length(1.0);
    ui_fade.set_loop(false);

    let mut fade_track = AnimationTrack::new("modulate.a".to_string());
    fade_track.add_keyframe(0.0, Variant::Float(0.0));
    fade_track.add_keyframe(1.0, Variant::Float(1.0));
    ui_fade.add_track(fade_track);

    ui_animator.add_animation(ui_fade);

    // Configure text inputs
    player_name_input.set_placeholder_text("Enter your hero's name".to_string());
    player_name_input.set_max_length(20);
    player_name_input.set_text_align(TextAlign::Center);
    player_name_input.set_select_all_on_focus(true);

    chat_input.set_placeholder_text("Say something to other players...".to_string());
    chat_input.set_max_length(100);
    chat_input.set_clear_button_enabled(true);

    command_input.set_placeholder_text("Enter command (e.g., /help)".to_string());
    command_input.set_max_length(50);
    command_input.set_secret(false);

    // Configure progress bars
    health_bar.set_min_value(0.0, &mut signal_manager);
    health_bar.set_max_value(100.0, &mut signal_manager);
    health_bar.set_value(100.0, &mut signal_manager);
    health_bar.set_step(1.0);
    health_bar.set_fill_mode(FillMode::LeftToRight);
    health_bar.set_show_percentage(true);

    mana_bar.set_min_value(0.0, &mut signal_manager);
    mana_bar.set_max_value(50.0, &mut signal_manager);
    mana_bar.set_value(50.0, &mut signal_manager);
    mana_bar.set_step(1.0);
    mana_bar.set_fill_mode(FillMode::LeftToRight);
    mana_bar.set_show_percentage(false);

    experience_bar.set_min_value(0.0, &mut signal_manager);
    experience_bar.set_max_value(1000.0, &mut signal_manager);
    experience_bar.set_value(0.0, &mut signal_manager);
    experience_bar.set_step(10.0);
    experience_bar.set_fill_mode(FillMode::LeftToRight);
    experience_bar.set_rounded(true);

    loading_bar.set_min_value(0.0, &mut signal_manager);
    loading_bar.set_max_value(100.0, &mut signal_manager);
    loading_bar.set_value(0.0, &mut signal_manager);
    loading_bar.set_step(1.0);
    loading_bar.set_fill_mode(FillMode::LeftToRight);
    loading_bar.set_show_percentage(true);

    // Configure checkboxes
    sound_checkbox.set_text("Enable sound effects".to_string());
    sound_checkbox.set_checked(true, &mut signal_manager);

    fullscreen_checkbox.set_text("Fullscreen mode".to_string());
    fullscreen_checkbox.set_checked(false, &mut signal_manager);

    autosave_checkbox.set_text("Auto-save enabled".to_string());
    autosave_checkbox.set_checked(true, &mut signal_manager);

    pvp_checkbox.set_text("Player vs Player".to_string());
    pvp_checkbox.set_checked(false, &mut signal_manager);

    // Configure physics bodies
    ground_platform.set_physics_material(PhysicsMaterial::rough());
    ground_platform.set_position(Vector2::new(0.0, 400.0));
    ground_platform.set_collision_layer(1);
    ground_platform.set_collision_mask(2);

    wall_left.set_physics_material(PhysicsMaterial::new());
    wall_left.set_position(Vector2::new(-500.0, 0.0));
    wall_left.set_collision_layer(1);
    wall_left.set_collision_mask(2);

    wall_right.set_physics_material(PhysicsMaterial::new());
    wall_right.set_position(Vector2::new(500.0, 0.0));
    wall_right.set_collision_layer(1);
    wall_right.set_collision_mask(2);

    treasure_chest.set_physics_material(PhysicsMaterial::bouncy());
    treasure_chest.set_position(Vector2::new(200.0, 350.0));
    treasure_chest.set_collision_layer(4);
    treasure_chest.set_collision_mask(2);

    println!("   🎯 All systems configured successfully!");
    println!("   📊 System Status:");
    println!("     - Timers: {} active", 3);
    println!("     - Audio: {} channels", 3);
    println!("     - Camera: {} ({})", main_camera.get_name(), if main_camera.is_current() { "active" } else { "inactive" });
    println!("     - Animations: {} player, {} UI", 3, 1);
    println!("     - Text Inputs: {} fields", 3);
    println!("     - Progress Bars: {} indicators", 4);
    println!("     - Checkboxes: {} options", 4);
    println!("     - Physics Bodies: {} static bodies", 4);

    println!();
    println!("   🎮 Starting complete RPG game simulation...");
    println!();

    // === GAME STATE ===

    let game_state = Rc::new(RefCell::new(CompleteGameState::new()));

    // === SIGNAL REGISTRATION ===

    // Register all signals
    signal_manager.register_signal(game_timer.get_timeout_signal().clone());
    signal_manager.register_signal(respawn_timer.get_timeout_signal().clone());
    signal_manager.register_signal(save_timer.get_timeout_signal().clone());
    signal_manager.register_signal(player_animator.get_animation_finished_signal().clone());
    signal_manager.register_signal(ui_animator.get_animation_finished_signal().clone());
    signal_manager.register_signal(player_name_input.get_text_submitted_signal().clone());
    signal_manager.register_signal(chat_input.get_text_submitted_signal().clone());
    signal_manager.register_signal(command_input.get_text_submitted_signal().clone());
    signal_manager.register_signal(health_bar.get_value_changed_signal().clone());
    signal_manager.register_signal(health_bar.get_progress_empty_signal().clone());
    signal_manager.register_signal(experience_bar.get_progress_complete_signal().clone());
    signal_manager.register_signal(sound_checkbox.get_toggled_signal().clone());
    signal_manager.register_signal(autosave_checkbox.get_toggled_signal().clone());
    signal_manager.register_signal(ground_platform.get_body_entered_signal().clone());
    signal_manager.register_signal(treasure_chest.get_body_entered_signal().clone());

    // === SIGNAL CONNECTIONS ===

    // Game timer - end game
    let state_game = game_state.clone();
    let game_ended = Callable::function("on_game_ended", move |_| {
        let mut state = state_game.borrow_mut();
        state.game_active = false;
        state.final_score = state.score + state.level * 100;
        println!("   🏆 Game session completed! Final score: {}", state.final_score);
        Ok(None)
    });

    // Auto-save timer
    let state_save = game_state.clone();
    let auto_save = Callable::function("on_auto_save", move |_| {
        let state = state_save.borrow();
        println!("   💾 Auto-save: Level {}, Score {}, Health {}",
                 state.level, state.score, state.health);
        Ok(None)
    });

    // Player name input
    let state_name = game_state.clone();
    let name_entered = Callable::function("on_name_entered", move |data| {
        let args = data.args();
        if let Some(Variant::String(name)) = args.get(0) {
            let mut state = state_name.borrow_mut();
            state.player_name = name.clone();
            state.score += 50;
            println!("   👤 Welcome, {}! (+50 points)", name);
        }
        Ok(None)
    });

    // Chat input
    let state_chat = game_state.clone();
    let chat_sent = Callable::function("on_chat_sent", move |data| {
        let args = data.args();
        if let Some(Variant::String(message)) = args.get(0) {
            let mut state = state_chat.borrow_mut();
            state.messages_sent += 1;
            state.score += 10;
            println!("   💬 [{}]: {} (+10 points)", state.player_name, message);
        }
        Ok(None)
    });

    // Command input
    let state_cmd = game_state.clone();
    let command_executed = Callable::function("on_command_executed", move |data| {
        let args = data.args();
        if let Some(Variant::String(command)) = args.get(0) {
            let mut state = state_cmd.borrow_mut();
            match command.as_str() {
                "/help" => {
                    println!("   📖 Available commands: /help, /stats, /heal, /levelup");
                    state.score += 5;
                }
                "/stats" => {
                    println!("   📊 Stats - Level: {}, Health: {}, Mana: {}, Score: {}",
                             state.level, state.health, state.mana, state.score);
                }
                "/heal" => {
                    state.health = (state.health + 25).min(100);
                    state.mana = (state.mana - 10).max(0);
                    println!("   ❤️ Healed! Health: {}, Mana: {}", state.health, state.mana);
                }
                "/levelup" => {
                    if state.experience >= 100 {
                        state.level += 1;
                        state.experience = 0;
                        state.health = 100;
                        state.mana = 50;
                        println!("   ⭐ Level up! Now level {}", state.level);
                    } else {
                        println!("   ❌ Not enough experience (need 100, have {})", state.experience);
                    }
                }
                _ => {
                    println!("   ❓ Unknown command: {}", command);
                }
            }
        }
        Ok(None)
    });

    // Health bar empty (player death)
    let state_death = game_state.clone();
    let player_died = Callable::function("on_player_died", move |_| {
        let mut state = state_death.borrow_mut();
        state.deaths += 1;
        state.health = 100;
        state.respawning = true;
        println!("   💀 Player died! Respawning... (Death #{}) ", state.deaths);
        Ok(None)
    });

    // Experience bar complete (level up)
    let state_levelup = game_state.clone();
    let level_up = Callable::function("on_level_up", move |_| {
        let mut state = state_levelup.borrow_mut();
        state.level += 1;
        state.health = 100;
        state.mana = 50;
        state.score += 200;
        println!("   ⭐ Level up! Now level {} (+200 points)", state.level);
        Ok(None)
    });

    // Sound checkbox toggle
    let sound_toggled = Callable::function("on_sound_toggled", move |data| {
        let args = data.args();
        if let Some(Variant::Bool(enabled)) = args.get(0) {
            println!("   🔊 Sound effects: {}", if *enabled { "enabled" } else { "disabled" });
        }
        Ok(None)
    });

    // Auto-save checkbox toggle
    let autosave_toggled = Callable::function("on_autosave_toggled", move |data| {
        let args = data.args();
        if let Some(Variant::Bool(enabled)) = args.get(0) {
            println!("   💾 Auto-save: {}", if *enabled { "enabled" } else { "disabled" });
        }
        Ok(None)
    });

    // Ground collision
    let state_ground = game_state.clone();
    let ground_collision = Callable::function("on_ground_collision", move |_| {
        let mut state = state_ground.borrow_mut();
        state.on_ground = true;
        println!("   🏃 Player landed on ground");
        Ok(None)
    });

    // Treasure collision
    let state_treasure = game_state.clone();
    let treasure_collision = Callable::function("on_treasure_collision", move |_| {
        let mut state = state_treasure.borrow_mut();
        state.treasures_found += 1;
        state.score += 100;
        state.experience += 25;
        println!("   💰 Treasure found! (+100 points, +25 XP) Total: {}", state.treasures_found);
        Ok(None)
    });

    // Connect all signals
    signal_manager.connect(game_timer.get_timeout_signal().id(), game_ended, ConnectionFlags::default()).unwrap();
    signal_manager.connect(save_timer.get_timeout_signal().id(), auto_save, ConnectionFlags::default()).unwrap();
    signal_manager.connect(player_name_input.get_text_submitted_signal().id(), name_entered, ConnectionFlags::default()).unwrap();
    signal_manager.connect(chat_input.get_text_submitted_signal().id(), chat_sent, ConnectionFlags::default()).unwrap();
    signal_manager.connect(command_input.get_text_submitted_signal().id(), command_executed, ConnectionFlags::default()).unwrap();
    signal_manager.connect(health_bar.get_progress_empty_signal().id(), player_died, ConnectionFlags::default()).unwrap();
    signal_manager.connect(experience_bar.get_progress_complete_signal().id(), level_up, ConnectionFlags::default()).unwrap();
    signal_manager.connect(sound_checkbox.get_toggled_signal().id(), sound_toggled, ConnectionFlags::default()).unwrap();
    signal_manager.connect(autosave_checkbox.get_toggled_signal().id(), autosave_toggled, ConnectionFlags::default()).unwrap();
    signal_manager.connect(ground_platform.get_body_entered_signal().id(), ground_collision, ConnectionFlags::default()).unwrap();
    signal_manager.connect(treasure_chest.get_body_entered_signal().id(), treasure_collision, ConnectionFlags::default()).unwrap();

    // === START GAME SYSTEMS ===

    game_timer.start(&mut signal_manager);
    save_timer.start(&mut signal_manager);
    player_animator.play(&mut signal_manager, None); // Use autoplay
    ui_animator.play(&mut signal_manager, Some("ui_fade".to_string()));
    ambient_audio.play(&mut signal_manager);

    println!("   🚀 All systems started! Beginning RPG adventure...");
    println!();

    // === GAME SIMULATION LOOP ===

    let mut total_time = 0.0;
    let delta = 0.5;
    let mut player_position = Vector2::new(0.0, 300.0);
    let mut last_action_time = 0.0;

    // Define game events
    let game_events = vec![
        (1.0, "character_creation"),
        (3.0, "first_movement"),
        (5.0, "combat_encounter"),
        (8.0, "treasure_hunt"),
        (12.0, "social_interaction"),
        (15.0, "settings_change"),
        (18.0, "command_usage"),
        (22.0, "level_progression"),
        (25.0, "final_challenge"),
    ];

    let mut event_index = 0;

    while total_time < 30.0 && game_state.borrow().game_active {
        total_time += delta;

        // Process game events
        if event_index < game_events.len() && total_time >= game_events[event_index].0 {
            let (_, event_type) = &game_events[event_index];

            match *event_type {
                "character_creation" => {
                    println!("   🎭 Character Creation Phase");
                    player_name_input.grab_focus(&mut signal_manager);
                    player_name_input.insert_text_at_cursor("Dragonslayer".to_string(), &mut signal_manager);
                    player_name_input.submit_text(&mut signal_manager);
                    ui_audio.play(&mut signal_manager);
                }
                "first_movement" => {
                    println!("   🚶 First Movement");
                    player_position = Vector2::new(100.0, 300.0);
                    main_camera.set_position(player_position);
                    player_animator.play(&mut signal_manager, Some("run".to_string()));
                    ground_platform.add_collision(1001, &mut signal_manager); // Player ID
                }
                "combat_encounter" => {
                    println!("   ⚔️ Combat Encounter");
                    player_animator.play(&mut signal_manager, Some("attack".to_string()));
                    combat_audio.set_position(player_position);
                    combat_audio.play(&mut signal_manager);

                    // Take damage
                    let current_health = health_bar.get_value();
                    health_bar.set_value(current_health - 30.0, &mut signal_manager);

                    // Use mana
                    let current_mana = mana_bar.get_value();
                    mana_bar.set_value(current_mana - 15.0, &mut signal_manager);

                    // Gain experience
                    let current_exp = experience_bar.get_value();
                    experience_bar.set_value(current_exp + 40.0, &mut signal_manager);

                    main_camera.shake(15.0, 1.0);
                }
                "treasure_hunt" => {
                    println!("   🗺️ Treasure Hunt");
                    player_position = Vector2::new(200.0, 350.0);
                    main_camera.set_position(player_position);
                    treasure_chest.add_collision(1001, &mut signal_manager);

                    // Update loading bar for treasure opening
                    for i in 0..=100 {
                        loading_bar.set_value(i as f64, &mut signal_manager);
                    }
                }
                "social_interaction" => {
                    println!("   💬 Social Interaction");
                    chat_input.grab_focus(&mut signal_manager);
                    chat_input.insert_text_at_cursor("Hello fellow adventurers!".to_string(), &mut signal_manager);
                    chat_input.submit_text(&mut signal_manager);
                    chat_input.clear(&mut signal_manager);

                    chat_input.insert_text_at_cursor("Anyone want to party up?".to_string(), &mut signal_manager);
                    chat_input.submit_text(&mut signal_manager);
                    ui_audio.play(&mut signal_manager);
                }
                "settings_change" => {
                    println!("   ⚙️ Settings Configuration");
                    sound_checkbox.toggle(&mut signal_manager);
                    fullscreen_checkbox.toggle(&mut signal_manager);
                    autosave_checkbox.toggle(&mut signal_manager);
                    pvp_checkbox.toggle(&mut signal_manager);
                }
                "command_usage" => {
                    println!("   🖥️ Command Usage");
                    let commands = vec!["/help", "/stats", "/heal", "/levelup"];
                    for cmd in commands {
                        command_input.grab_focus(&mut signal_manager);
                        command_input.insert_text_at_cursor(cmd.to_string(), &mut signal_manager);
                        command_input.submit_text(&mut signal_manager);
                        command_input.clear(&mut signal_manager);
                    }
                }
                "level_progression" => {
                    println!("   📈 Level Progression");
                    // Fill experience bar to trigger level up
                    experience_bar.fill(&mut signal_manager);

                    // Restore health and mana
                    health_bar.fill(&mut signal_manager);
                    mana_bar.fill(&mut signal_manager);
                }
                "final_challenge" => {
                    println!("   🏆 Final Challenge");
                    player_animator.play(&mut signal_manager, Some("attack".to_string()));

                    // Epic battle simulation
                    for _ in 0..3 {
                        let current_health = health_bar.get_value();
                        health_bar.set_value(current_health - 20.0, &mut signal_manager);

                        let current_exp = experience_bar.get_value();
                        experience_bar.set_value(current_exp + 30.0, &mut signal_manager);

                        main_camera.shake(25.0, 0.8);
                        combat_audio.play(&mut signal_manager);
                    }
                }
                _ => {}
            }
            event_index += 1;
        }

        // Update all systems
        game_timer.update(delta, &mut signal_manager);
        respawn_timer.update(delta, &mut signal_manager);
        save_timer.update(delta, &mut signal_manager);

        main_camera.update(delta as f32, Some(player_position));
        player_animator.update(delta as f32, &mut signal_manager);
        ui_animator.update(delta as f32, &mut signal_manager);

        ambient_audio.update(delta as f32, &mut signal_manager);
        combat_audio.update(delta as f32, &mut signal_manager);
        ui_audio.update(delta as f32, &mut signal_manager);

        ground_platform.update(delta as f32, &mut signal_manager);
        wall_left.update(delta as f32, &mut signal_manager);
        wall_right.update(delta as f32, &mut signal_manager);
        treasure_chest.update(delta as f32, &mut signal_manager);

        // Update game state
        let mut state = game_state.borrow_mut();
        state.play_time = total_time;

        // Periodic updates
        if total_time - last_action_time > 3.0 {
            // Regenerate mana slowly
            let current_mana = mana_bar.get_value();
            if current_mana < 50.0 {
                mana_bar.set_value(current_mana + 2.0, &mut signal_manager);
            }

            // Add some experience over time
            let current_exp = experience_bar.get_value();
            if current_exp < 1000.0 {
                experience_bar.set_value(current_exp + 5.0, &mut signal_manager);
            }

            last_action_time = total_time;
        }
    }

    // === FINAL STATISTICS ===

    let final_state = game_state.borrow();
    println!();
    println!("   📊 Final Game Statistics:");
    println!("     Player Name: '{}'", final_state.player_name);
    println!("     Play Time: {:.1}s", final_state.play_time);
    println!("     Level: {}", final_state.level);
    println!("     Final Score: {}", final_state.final_score);
    println!("     Health: {:.0}/100", health_bar.get_value());
    println!("     Mana: {:.0}/50", mana_bar.get_value());
    println!("     Experience: {:.0}/1000", experience_bar.get_value());
    println!("     Messages Sent: {}", final_state.messages_sent);
    println!("     Treasures Found: {}", final_state.treasures_found);
    println!("     Deaths: {}", final_state.deaths);
    println!("     On Ground: {}", final_state.on_ground);

    println!();
    println!("   🎛️ System Final States:");
    println!("     Camera Position: {}", main_camera.get_position());
    println!("     Camera Zoom: {}", main_camera.get_zoom());
    println!("     Current Animation: {:?}", player_animator.get_current_animation());
    println!("     Sound Effects: {}", sound_checkbox.is_checked());
    println!("     Fullscreen: {}", fullscreen_checkbox.is_checked());
    println!("     Auto-save: {}", autosave_checkbox.is_checked());
    println!("     PvP Mode: {}", pvp_checkbox.is_checked());
    println!("     Ground Collisions: {}", ground_platform.get_collision_count());
    println!("     Treasure Collisions: {}", treasure_chest.get_collision_count());

    println!();
    println!("   🎉 Complete RPG game system demo finished!");
    println!("   All 8 essential nodes demonstrated comprehensive game development capabilities!");
}

// Game state structure for the complete demo
#[derive(Debug)]
struct CompleteGameState {
    player_name: String,
    play_time: f64,
    level: i32,
    health: i32,
    mana: i32,
    experience: i32,
    score: i32,
    final_score: i32,
    messages_sent: i32,
    treasures_found: i32,
    deaths: i32,
    game_active: bool,
    respawning: bool,
    on_ground: bool,
}

impl CompleteGameState {
    fn new() -> Self {
        Self {
            player_name: String::new(),
            play_time: 0.0,
            level: 1,
            health: 100,
            mana: 50,
            experience: 0,
            score: 0,
            final_score: 0,
            messages_sent: 0,
            treasures_found: 0,
            deaths: 0,
            game_active: true,
            respawning: false,
            on_ground: false,
        }
    }
}
