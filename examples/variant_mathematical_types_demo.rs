use verturion::core::variant::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>};
use verturion::core::math::{Vector3, AABB, Projection, Quaternion, Basis, Plane};

fn main() {
    println!("=== Variant Mathematical Types Integration Demo ===\n");

    // Demonstrate AABB integration
    println!("--- AABB (Axis-Aligned Bounding Box) ---");
    let aabb = AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 30.0);
    let aabb_variant = Variant::from(aabb);

    println!("Original AABB: {}", aabb);
    println!("AABB as Variant: {}", aabb_variant);
    println!("Is AABB: {}", aabb_variant.is_aabb());
    println!("Extracted AABB: {:?}", aabb_variant.as_aabb());

    // Demonstrate Projection integration
    println!("\n--- Projection (4x4 Matrix) ---");
    let projection = Projection::perspective(1.0, 1.0, 0.1, 100.0);
    let proj_variant = Variant::from(projection);

    println!("Projection as Variant: {}", proj_variant);
    println!("Is Projection: {}", proj_variant.is_projection());
    println!("Extracted Projection available: {}", proj_variant.as_projection().is_some());

    // Demonstrate Quaternion integration
    println!("\n--- Quaternion (Rotation) ---");
    let quaternion = Quaternion::from_axis_angle(Vector3::UP, std::f32::consts::PI / 4.0);
    let quat_variant = Variant::from(quaternion);

    println!("Quaternion as Variant: {}", quat_variant);
    println!("Is Quaternion: {}", quat_variant.is_quaternion());
    println!("Extracted Quaternion: {:?}", quat_variant.as_quaternion());

    // Demonstrate Basis integration
    println!("\n--- Basis (3x3 Matrix) ---");
    let basis = Basis::from_scale(Vector3::new(2.0, 2.0, 2.0));
    let basis_variant = Variant::from(basis);

    println!("Basis as Variant: {}", basis_variant);
    println!("Is Basis: {}", basis_variant.is_basis());
    println!("Extracted Basis available: {}", basis_variant.as_basis().is_some());

    // Demonstrate Plane integration
    println!("\n--- Plane (3D Plane) ---");
    let plane = Plane::new(Vector3::UP, 5.0);
    let plane_variant = Variant::from(plane);

    println!("Plane as Variant: {}", plane_variant);
    println!("Is Plane: {}", plane_variant.is_plane());
    println!("Extracted Plane: {:?}", plane_variant.as_plane());

    // Demonstrate mathematical types in collections
    println!("\n--- Mathematical Types in Collections ---");

    // Array of mathematical types
    let mut math_array = Array::new();
    math_array.push_back(aabb_variant.clone());
    math_array.push_back(proj_variant.clone());
    math_array.push_back(quat_variant.clone());
    math_array.push_back(basis_variant.clone());
    math_array.push_back(plane_variant.clone());

    println!("Array with {} mathematical types:", math_array.size());
    for (i, item) in math_array.iter().enumerate() {
        if item.is_aabb() {
            println!("  [{}]: AABB", i);
        } else if item.is_projection() {
            println!("  [{}]: Projection", i);
        } else if item.is_quaternion() {
            println!("  [{}]: Quaternion", i);
        } else if item.is_basis() {
            println!("  [{}]: Basis", i);
        } else if item.is_plane() {
            println!("  [{}]: Plane", i);
        }
    }

    // Dictionary with mathematical types as keys and values
    let mut math_dict = Dictionary::new();
    math_dict.set("bounding_box".into(), aabb_variant.clone());
    math_dict.set("camera_projection".into(), proj_variant.clone());
    math_dict.set("rotation".into(), quat_variant.clone());
    math_dict.set("transformation".into(), basis_variant.clone());
    math_dict.set("ground_plane".into(), plane_variant.clone());

    println!("\nDictionary with mathematical types:");
    for (key, value) in math_dict.iter() {
        if let Some(key_str) = key.as_string() {
            if value.is_aabb() {
                println!("  {}: AABB", key_str);
            } else if value.is_projection() {
                println!("  {}: Projection", key_str);
            } else if value.is_quaternion() {
                println!("  {}: Quaternion", key_str);
            } else if value.is_basis() {
                println!("  {}: Basis", key_str);
            } else if value.is_plane() {
                println!("  {}: Plane", key_str);
            }
        }
    }

    // Demonstrate type safety
    println!("\n--- Type Safety Demonstration ---");
    let vector3_variant = Variant::from(Vector3::new(1.0, 2.0, 3.0));

    println!("Vector3 variant is AABB: {}", vector3_variant.is_aabb());
    println!("Vector3 variant is Vector3: {}", vector3_variant.is_vector3());
    println!("AABB variant is Vector3: {}", aabb_variant.is_vector3());
    println!("AABB variant is AABB: {}", aabb_variant.is_aabb());

    // Demonstrate hash map usage
    println!("\n--- Hash Map Usage ---");
    use std::collections::HashMap;

    let mut variant_map: HashMap<Variant, Variant> = HashMap::new();
    variant_map.insert(aabb_variant.clone(), "collision_bounds".into());
    variant_map.insert(quat_variant.clone(), "object_rotation".into());
    variant_map.insert(plane_variant.clone(), "floor_surface".into());

    println!("Hash map with {} mathematical type keys", variant_map.len());

    // Test retrieval
    if let Some(value) = variant_map.get(&aabb_variant) {
        println!("AABB key maps to: {:?}", value);
    }

    // Demonstrate equality and cloning
    println!("\n--- Equality and Cloning ---");
    let aabb_clone = aabb_variant.clone();
    let same_aabb = Variant::from(AABB::new(1.0, 2.0, 3.0, 10.0, 20.0, 30.0));
    let different_aabb = Variant::from(AABB::new(2.0, 3.0, 4.0, 11.0, 21.0, 31.0));

    println!("Original AABB == Clone: {}", aabb_variant == aabb_clone);
    println!("Original AABB == Same values: {}", aabb_variant == same_aabb);
    println!("Original AABB == Different values: {}", aabb_variant == different_aabb);

    // Demonstrate debug formatting
    println!("\n--- Debug Formatting ---");
    println!("AABB debug: {:?}", aabb_variant);
    println!("Quaternion debug: {:?}", quat_variant);
    println!("Plane debug: {:?}", plane_variant);

    println!("\n=== Demo Complete ===");
    println!("All mathematical types are now fully integrated with the Variant system!");
}
