//! Comprehensive demonstration of String, StringName, and NodePath integration in the Variant system.
//!
//! This example showcases the complete text type integration including:
//! - Type checking and value extraction
//! - Display formatting and hash map usage
//! - Collection integration (Array/Dictionary)
//! - Cross-type safety validation
//! - Conversion between text types

use verturion::core::variant::{Variant, Array, Dictionary, String as GodotString, StringName};
use verturion::core::scene::NodePath;
use std::collections::HashMap;

fn main() {
    println!("=== Verturion Text Type Integration Demo ===\n");

    // 1. Basic Text Type Creation and Variant Integration
    println!("1. Creating text types and converting to Variants:");
    
    let godot_string = GodotString::from("Hello, Verturion!");
    let string_name = StringName::from("player_node");
    let node_path = NodePath::from("/root/Player/Weapon");
    
    let string_var = Variant::from(godot_string.clone());
    let name_var = Variant::from(string_name.clone());
    let path_var = Variant::from(node_path.clone());
    
    println!("  GodotString: {}", string_var);
    println!("  StringName:  {}", name_var);
    println!("  NodePath:    {}", path_var);
    
    // 2. Type Checking
    println!("\n2. Type checking:");
    println!("  string_var.is_godot_string(): {}", string_var.is_godot_string());
    println!("  string_var.is_string_name():  {}", string_var.is_string_name());
    println!("  string_var.is_node_path():    {}", string_var.is_node_path());
    
    println!("  name_var.is_string_name():    {}", name_var.is_string_name());
    println!("  name_var.is_godot_string():   {}", name_var.is_godot_string());
    
    println!("  path_var.is_node_path():      {}", path_var.is_node_path());
    println!("  path_var.is_string_name():    {}", path_var.is_string_name());
    
    // 3. Value Extraction
    println!("\n3. Safe value extraction:");
    if let Some(extracted_string) = string_var.as_godot_string() {
        println!("  Extracted GodotString: '{}'", extracted_string.as_str());
    }
    
    if let Some(extracted_name) = name_var.as_string_name() {
        println!("  Extracted StringName: '{}'", extracted_name.as_str());
    }
    
    if let Some(extracted_path) = path_var.as_node_path() {
        println!("  Extracted NodePath: '{}'", extracted_path.as_str());
    }
    
    // 4. Cross-type Safety
    println!("\n4. Cross-type safety validation:");
    println!("  Attempting to extract StringName from GodotString variant:");
    match string_var.as_string_name() {
        Some(_) => println!("    ❌ Unexpected success!"),
        None => println!("    ✅ Correctly returned None"),
    }
    
    println!("  Attempting to extract NodePath from StringName variant:");
    match name_var.as_node_path() {
        Some(_) => println!("    ❌ Unexpected success!"),
        None => println!("    ✅ Correctly returned None"),
    }
    
    // 5. Hash Map Usage
    println!("\n5. Using text types as HashMap keys:");
    let mut text_map: HashMap<Variant, String> = HashMap::new();
    
    text_map.insert(string_var.clone(), "This is a GodotString value".to_string());
    text_map.insert(name_var.clone(), "This is a StringName value".to_string());
    text_map.insert(path_var.clone(), "This is a NodePath value".to_string());
    
    println!("  HashMap size: {}", text_map.len());
    
    for (key, value) in &text_map {
        if key.is_godot_string() {
            println!("    GodotString key -> {}", value);
        } else if key.is_string_name() {
            println!("    StringName key  -> {}", value);
        } else if key.is_node_path() {
            println!("    NodePath key    -> {}", value);
        }
    }
    
    // 6. Array Integration
    println!("\n6. Text types in Array collections:");
    let mut text_array = Array::new();
    text_array.push_back(string_var.clone());
    text_array.push_back(name_var.clone());
    text_array.push_back(path_var.clone());
    text_array.push_back(Variant::from("Regular string"));
    text_array.push_back(Variant::from(42)); // Mix with other types
    
    println!("  Array size: {}", text_array.size());
    println!("  Array contents:");
    for i in 0..text_array.size() {
        if let Some(item) = text_array.get(i) {
            if item.is_godot_string() {
                println!("    [{}] GodotString: {}", i, item);
            } else if item.is_string_name() {
                println!("    [{}] StringName:  {}", i, item);
            } else if item.is_node_path() {
                println!("    [{}] NodePath:    {}", i, item);
            } else if item.is_string() {
                println!("    [{}] String:      {}", i, item);
            } else {
                println!("    [{}] Other:       {}", i, item);
            }
        }
    }
    
    // 7. Dictionary Integration
    println!("\n7. Text types in Dictionary collections:");
    let mut text_dict = Dictionary::new();
    
    // Use text types as keys
    text_dict.set(Variant::from(GodotString::from("config")), Variant::from("Configuration data"));
    text_dict.set(Variant::from(StringName::from("player_name")), Variant::from("Hero"));
    text_dict.set(Variant::from(NodePath::from("/ui/health_bar")), Variant::from(100));
    
    // Use text types as values
    text_dict.set(Variant::from("greeting"), string_var.clone());
    text_dict.set(Variant::from("node_id"), name_var.clone());
    text_dict.set(Variant::from("scene_path"), path_var.clone());
    
    println!("  Dictionary size: {}", text_dict.size());
    println!("  Dictionary contents:");
    
    // Check specific keys
    if text_dict.has(&Variant::from(GodotString::from("config"))) {
        println!("    ✅ Found GodotString key 'config'");
    }
    if text_dict.has(&Variant::from(StringName::from("player_name"))) {
        println!("    ✅ Found StringName key 'player_name'");
    }
    if text_dict.has(&Variant::from(NodePath::from("/ui/health_bar"))) {
        println!("    ✅ Found NodePath key '/ui/health_bar'");
    }
    
    // 8. Equality and Comparison
    println!("\n8. Equality and comparison:");
    let string1 = Variant::from(GodotString::from("test"));
    let string2 = Variant::from(GodotString::from("test"));
    let string3 = Variant::from(GodotString::from("different"));
    
    println!("  GodotString('test') == GodotString('test'): {}", string1 == string2);
    println!("  GodotString('test') == GodotString('different'): {}", string1 == string3);
    
    // Cross-type inequality
    let same_content_different_types = [
        Variant::from(GodotString::from("player")),
        Variant::from(StringName::from("player")),
        Variant::from(NodePath::from("player")),
    ];
    
    println!("  Cross-type inequality (same content, different types):");
    for i in 0..same_content_different_types.len() {
        for j in i+1..same_content_different_types.len() {
            println!("    Type {} != Type {}: {}", 
                i, j, same_content_different_types[i] != same_content_different_types[j]);
        }
    }
    
    // 9. Cloning and Debug
    println!("\n9. Cloning and debug formatting:");
    let cloned_string = string_var.clone();
    let cloned_name = name_var.clone();
    let cloned_path = path_var.clone();
    
    println!("  Original == Clone (GodotString): {}", string_var == cloned_string);
    println!("  Original == Clone (StringName):  {}", name_var == cloned_name);
    println!("  Original == Clone (NodePath):    {}", path_var == cloned_path);
    
    println!("  Debug formatting:");
    println!("    GodotString: {:?}", string_var);
    println!("    StringName:  {:?}", name_var);
    println!("    NodePath:    {:?}", path_var);
    
    println!("\n=== Demo Complete ===");
    println!("✅ All text types successfully integrated into Variant system!");
    println!("✅ Type safety maintained across all operations");
    println!("✅ Collections support all text types as keys and values");
    println!("✅ Hash map compatibility confirmed");
    println!("✅ Cross-type safety validation working correctly");
}
