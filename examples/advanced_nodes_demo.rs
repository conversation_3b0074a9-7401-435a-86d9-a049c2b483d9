//! Advanced Nodes Demo
//!
//! This example demonstrates the advanced game development nodes in the Verturion
//! engine including AnimationPlayer for keyframe animation, LineEdit for text input,
//! and their integration with existing essential nodes (Timer, AudioStreamPlayer2D,
//! Camera2D) to create complete interactive game scenarios with animation, UI input,
//! and comprehensive game mechanics.

use verturion::core::scene::nodes::{
    Timer, AudioStreamPlayer2D, AttenuationModel, Camera2D,
    AnimationPlayer, Animation, AnimationTrack, AnimationMode,
    LineEdit, TextAlign, VirtualKeyboardType
};
use verturion::core::signal::{SignalManager, Callable, ConnectionFlags};
use verturion::core::math::Vector2;
use verturion::core::variant::Variant;
use std::cell::RefCell;
use std::rc::Rc;

fn main() {
    println!("=== Verturion Advanced Nodes Demo ===\n");

    // Demonstrate AnimationPlayer functionality
    demo_animation_system();

    // Demonstrate LineEdit functionality
    demo_input_system();

    // Demonstrate integrated interactive game scenario
    demo_interactive_game_scenario();
}

fn demo_animation_system() {
    println!("1. Animation System:");

    let mut signal_manager = SignalManager::new();
    let mut character_animator = AnimationPlayer::new("CharacterAnimator");
    let mut ui_animator = AnimationPlayer::new("UIAnimator");

    // Create character walk animation
    let mut walk_animation = Animation::new("walk".to_string());
    walk_animation.set_length(1.0);
    walk_animation.set_loop(true);

    // Add position track for walk cycle
    let mut position_track = AnimationTrack::new("position.x".to_string());
    position_track.add_keyframe(0.0, Variant::Float(0.0));
    position_track.add_keyframe(0.5, Variant::Float(10.0));
    position_track.add_keyframe(1.0, Variant::Float(0.0));
    walk_animation.add_track(position_track);

    // Add sprite frame track
    let mut sprite_track = AnimationTrack::new("sprite.frame".to_string());
    sprite_track.add_keyframe(0.0, Variant::Int(0));
    sprite_track.add_keyframe(0.25, Variant::Int(1));
    sprite_track.add_keyframe(0.5, Variant::Int(2));
    sprite_track.add_keyframe(0.75, Variant::Int(3));
    sprite_track.add_keyframe(1.0, Variant::Int(0));
    walk_animation.add_track(sprite_track);

    character_animator.add_animation(walk_animation);

    // Create UI fade animation
    let mut fade_animation = Animation::new("fade_in".to_string());
    fade_animation.set_length(2.0);
    fade_animation.set_loop(false);

    let mut opacity_track = AnimationTrack::new("modulate.a".to_string());
    opacity_track.add_keyframe(0.0, Variant::Float(0.0));
    opacity_track.add_keyframe(2.0, Variant::Float(1.0));
    fade_animation.add_track(opacity_track);

    ui_animator.add_animation(fade_animation);

    // Register animation signals
    signal_manager.register_signal(character_animator.get_animation_finished_signal().clone());
    signal_manager.register_signal(character_animator.get_animation_started_signal().clone());
    signal_manager.register_signal(ui_animator.get_animation_finished_signal().clone());

    // Create animation event tracker
    let animation_events = Rc::new(RefCell::new(Vec::<String>::new()));

    // Connect animation signals
    let events_char = animation_events.clone();
    let char_started = Callable::function("on_char_animation_started", move |data| {
        let args = data.args();
        if let Some(Variant::String(anim_name)) = args.get(0) {
            events_char.borrow_mut().push(format!("Character started: {}", anim_name));
            println!("   🏃 Character animation started: {}", anim_name);
        }
        Ok(None)
    });

    let events_ui = animation_events.clone();
    let ui_finished = Callable::function("on_ui_animation_finished", move |data| {
        let args = data.args();
        if let Some(Variant::String(anim_name)) = args.get(0) {
            events_ui.borrow_mut().push(format!("UI finished: {}", anim_name));
            println!("   ✨ UI animation finished: {}", anim_name);
        }
        Ok(None)
    });

    signal_manager.connect(character_animator.get_animation_started_signal().id(), char_started, ConnectionFlags::default()).unwrap();
    signal_manager.connect(ui_animator.get_animation_finished_signal().id(), ui_finished, ConnectionFlags::default()).unwrap();

    // Configure and start animations
    character_animator.set_speed_scale(1.5);
    character_animator.set_playback_mode(AnimationMode::Loop);
    ui_animator.set_speed_scale(0.8);

    println!("   Configured character walk animation (1s loop) and UI fade animation (2s)");

    // Start animations
    character_animator.play(&mut signal_manager, Some("walk".to_string()));
    ui_animator.play(&mut signal_manager, Some("fade_in".to_string()));

    // Simulate animation updates
    let mut total_time = 0.0;
    let delta = 0.1;

    while total_time < 3.0 {
        total_time += delta;

        // Update animations
        let _char_finished = character_animator.update(delta, &mut signal_manager);
        let ui_finished = ui_animator.update(delta, &mut signal_manager);

        // Log animation progress
        if total_time.fract() < 0.1 {
            println!("     Time: {:.1}s - Character pos: {:.1}s, UI pos: {:.1}s",
                     total_time,
                     character_animator.get_current_animation_position(),
                     ui_animator.get_current_animation_position());
        }

        // Check for animation completion
        if ui_finished {
            println!("   🎬 UI fade animation completed!");
        }

        // Demonstrate animation control
        if total_time > 1.5 && total_time < 1.6 {
            character_animator.pause();
            println!("   ⏸️ Character animation paused");
        }

        if total_time > 2.0 && total_time < 2.1 {
            character_animator.resume();
            println!("   ▶️ Character animation resumed");
        }
    }

    println!("   Animation events: {:?}", *animation_events.borrow());
    println!();
}

fn demo_input_system() {
    println!("2. Input System:");

    let mut signal_manager = SignalManager::new();
    let mut username_input = LineEdit::new("UsernameInput");
    let mut password_input = LineEdit::new("PasswordInput");
    let mut search_input = LineEdit::new("SearchInput");

    // Configure username input
    username_input.set_placeholder_text("Enter username".to_string());
    username_input.set_max_length(20);
    username_input.set_text_align(TextAlign::Left);
    username_input.set_select_all_on_focus(true);

    // Configure password input
    password_input.set_placeholder_text("Enter password".to_string());
    password_input.set_secret(true);
    password_input.set_max_length(50);
    password_input.set_virtual_keyboard_type(VirtualKeyboardType::Password);

    // Configure search input
    search_input.set_placeholder_text("Search items...".to_string());
    search_input.set_clear_button_enabled(true);
    search_input.set_virtual_keyboard_type(VirtualKeyboardType::Default);

    // Register input signals
    signal_manager.register_signal(username_input.get_text_changed_signal().clone());
    signal_manager.register_signal(username_input.get_text_submitted_signal().clone());
    signal_manager.register_signal(password_input.get_text_changed_signal().clone());
    signal_manager.register_signal(password_input.get_text_submitted_signal().clone());
    signal_manager.register_signal(search_input.get_text_changed_signal().clone());

    // Create input event tracker
    let input_events = Rc::new(RefCell::new(Vec::<String>::new()));

    // Connect input signals
    let events_user = input_events.clone();
    let username_changed = Callable::function("on_username_changed", move |data| {
        let args = data.args();
        if let Some(Variant::String(text)) = args.get(0) {
            events_user.borrow_mut().push(format!("Username: {}", text));
            println!("   👤 Username changed: '{}'", text);
        }
        Ok(None)
    });

    let events_pass = input_events.clone();
    let password_changed = Callable::function("on_password_changed", move |data| {
        let args = data.args();
        if let Some(Variant::String(text)) = args.get(0) {
            events_pass.borrow_mut().push(format!("Password: {} chars", text.len()));
            println!("   🔒 Password changed: {} characters", text.len());
        }
        Ok(None)
    });

    let events_search = input_events.clone();
    let search_changed = Callable::function("on_search_changed", move |data| {
        let args = data.args();
        if let Some(Variant::String(text)) = args.get(0) {
            events_search.borrow_mut().push(format!("Search: {}", text));
            println!("   🔍 Search changed: '{}'", text);
        }
        Ok(None)
    });

    let events_submit = input_events.clone();
    let form_submitted1 = Callable::function("on_form_submitted1", move |data| {
        let args = data.args();
        if let Some(Variant::String(text)) = args.get(0) {
            events_submit.borrow_mut().push(format!("Submitted: {}", text));
            println!("   📝 Form submitted: '{}'", text);
        }
        Ok(None)
    });

    let events_submit2 = input_events.clone();
    let form_submitted2 = Callable::function("on_form_submitted2", move |data| {
        let args = data.args();
        if let Some(Variant::String(text)) = args.get(0) {
            events_submit2.borrow_mut().push(format!("Submitted: {}", text));
            println!("   📝 Form submitted: '{}'", text);
        }
        Ok(None)
    });

    signal_manager.connect(username_input.get_text_changed_signal().id(), username_changed, ConnectionFlags::default()).unwrap();
    signal_manager.connect(password_input.get_text_changed_signal().id(), password_changed, ConnectionFlags::default()).unwrap();
    signal_manager.connect(search_input.get_text_changed_signal().id(), search_changed, ConnectionFlags::default()).unwrap();
    signal_manager.connect(username_input.get_text_submitted_signal().id(), form_submitted1, ConnectionFlags::default()).unwrap();
    signal_manager.connect(password_input.get_text_submitted_signal().id(), form_submitted2, ConnectionFlags::default()).unwrap();

    println!("   Configured username, password, and search inputs with validation");

    // Simulate user input interactions
    println!("   Simulating user input interactions:");

    // Username input
    username_input.grab_focus(&mut signal_manager);
    username_input.insert_text_at_cursor("player123".to_string(), &mut signal_manager);
    username_input.select(6, 9);
    username_input.insert_text_at_cursor("456".to_string(), &mut signal_manager);

    // Password input
    password_input.grab_focus(&mut signal_manager);
    password_input.insert_text_at_cursor("mySecretPassword".to_string(), &mut signal_manager);

    // Search input
    search_input.grab_focus(&mut signal_manager);
    search_input.insert_text_at_cursor("magic sword".to_string(), &mut signal_manager);
    search_input.set_cursor_position(5);
    search_input.insert_text_at_cursor(" fire".to_string(), &mut signal_manager);

    // Test input validation and submission
    println!("   Testing input validation and submission:");

    // Validate inputs
    assert!(username_input.is_text_valid());
    assert!(password_input.is_text_valid());
    assert!(search_input.is_text_valid());

    // Submit forms
    username_input.submit_text(&mut signal_manager);
    password_input.submit_text(&mut signal_manager);

    // Test input manipulation
    println!("   Testing advanced input manipulation:");

    // Test selection and deletion
    search_input.select_all();
    search_input.delete_selection(&mut signal_manager);
    search_input.insert_text_at_cursor("health potion".to_string(), &mut signal_manager);

    // Test cursor movement
    search_input.move_cursor(-7, false);
    search_input.insert_text_at_cursor("mana ".to_string(), &mut signal_manager);

    // Display final states
    println!("   Final input states:");
    println!("     Username: '{}' (display: '{}')", username_input.get_text(), username_input.get_display_text());
    println!("     Password: '{}' (display: '{}')", password_input.get_text(), password_input.get_display_text());
    println!("     Search: '{}' (display: '{}')", search_input.get_text(), search_input.get_display_text());

    println!("   Input events: {:?}", *input_events.borrow());
    println!();
}

fn demo_interactive_game_scenario() {
    println!("3. Interactive Game Scenario:");
    println!("   Simulating a complete RPG character creation and gameplay system");

    let mut signal_manager = SignalManager::new();

    // Create all game systems
    let mut game_timer = Timer::new("GameTimer");
    let mut animation_timer = Timer::new("AnimationTimer");
    let mut main_camera = Camera2D::new("MainCamera");
    let mut character_animator = AnimationPlayer::new("CharacterAnimator");
    let mut ui_animator = AnimationPlayer::new("UIAnimator");
    let mut ambient_audio = AudioStreamPlayer2D::new("AmbientAudio");
    let mut sfx_audio = AudioStreamPlayer2D::new("SFXAudio");
    let mut name_input = LineEdit::new("CharacterName");
    let mut chat_input = LineEdit::new("ChatInput");

    // Configure game timers
    game_timer.set_wait_time(30.0); // 30 second demo
    game_timer.set_one_shot(true);
    animation_timer.set_wait_time(2.0); // Animation cycle timer
    animation_timer.set_one_shot(false);

    // Configure camera
    main_camera.set_current(true);
    main_camera.set_zoom(Vector2::new(1.5, 1.5));
    main_camera.set_smoothing_enabled(true);
    main_camera.set_smoothing_speed(3.0);
    main_camera.set_limit_left(-1000.0);
    main_camera.set_limit_right(1000.0);
    main_camera.set_limit_top(-600.0);
    main_camera.set_limit_bottom(600.0);

    // Create character animations
    let mut idle_animation = Animation::new("idle".to_string());
    idle_animation.set_length(2.0);
    idle_animation.set_loop(true);

    let mut idle_track = AnimationTrack::new("sprite.frame".to_string());
    idle_track.add_keyframe(0.0, Variant::Int(0));
    idle_track.add_keyframe(1.0, Variant::Int(1));
    idle_track.add_keyframe(2.0, Variant::Int(0));
    idle_animation.add_track(idle_track);

    let mut walk_animation = Animation::new("walk".to_string());
    walk_animation.set_length(0.8);
    walk_animation.set_loop(true);

    let mut walk_track = AnimationTrack::new("sprite.frame".to_string());
    walk_track.add_keyframe(0.0, Variant::Int(2));
    walk_track.add_keyframe(0.2, Variant::Int(3));
    walk_track.add_keyframe(0.4, Variant::Int(4));
    walk_track.add_keyframe(0.6, Variant::Int(5));
    walk_track.add_keyframe(0.8, Variant::Int(2));
    walk_animation.add_track(walk_track);

    character_animator.add_animation(idle_animation);
    character_animator.add_animation(walk_animation);
    character_animator.set_autoplay("idle".to_string());

    // Create UI animations
    let mut menu_fade = Animation::new("menu_fade".to_string());
    menu_fade.set_length(1.5);
    menu_fade.set_loop(false);

    let mut fade_track = AnimationTrack::new("modulate.a".to_string());
    fade_track.add_keyframe(0.0, Variant::Float(0.0));
    fade_track.add_keyframe(1.5, Variant::Float(1.0));
    menu_fade.add_track(fade_track);

    ui_animator.add_animation(menu_fade);

    // Configure audio
    ambient_audio.set_stream(Some("res://audio/forest_ambient.ogg".to_string()));
    ambient_audio.set_volume_db(-10.0);
    ambient_audio.set_bus("Ambient".to_string());
    ambient_audio.set_attenuation(AttenuationModel::Disabled);

    sfx_audio.set_stream(Some("res://audio/ui_click.wav".to_string()));
    sfx_audio.set_volume_db(-5.0);
    sfx_audio.set_bus("SFX".to_string());
    sfx_audio.set_max_distance(100.0);
    sfx_audio.set_attenuation(AttenuationModel::InverseDistance);

    // Configure input fields
    name_input.set_placeholder_text("Enter character name".to_string());
    name_input.set_max_length(20);
    name_input.set_text_align(TextAlign::Center);
    name_input.set_select_all_on_focus(true);

    chat_input.set_placeholder_text("Type a message...".to_string());
    chat_input.set_max_length(100);
    chat_input.set_clear_button_enabled(true);

    // Register all signals
    signal_manager.register_signal(game_timer.get_timeout_signal().clone());
    signal_manager.register_signal(animation_timer.get_timeout_signal().clone());
    signal_manager.register_signal(character_animator.get_animation_finished_signal().clone());
    signal_manager.register_signal(ui_animator.get_animation_finished_signal().clone());
    signal_manager.register_signal(ambient_audio.get_finished_signal().clone());
    signal_manager.register_signal(sfx_audio.get_finished_signal().clone());
    signal_manager.register_signal(name_input.get_text_submitted_signal().clone());
    signal_manager.register_signal(chat_input.get_text_submitted_signal().clone());

    // Create comprehensive game state
    let game_state = Rc::new(RefCell::new(InteractiveGameState::new()));

    // Connect game timer
    let state_timer = game_state.clone();
    let game_complete = Callable::function("on_game_complete", move |_| {
        let mut state = state_timer.borrow_mut();
        state.game_completed = true;
        state.final_score = state.score + state.bonus_points;
        println!("   🏆 Game completed! Final score: {} (bonus: {})",
                 state.final_score, state.bonus_points);
        Ok(None)
    });

    // Connect animation timer
    let state_anim = game_state.clone();
    let anim_cycle = Callable::function("on_animation_cycle", move |_| {
        let mut state = state_anim.borrow_mut();
        state.animation_cycles += 1;
        println!("   🎭 Animation cycle #{}", state.animation_cycles);
        Ok(None)
    });

    // Connect character name input
    let state_name = game_state.clone();
    let name_submitted = Callable::function("on_name_submitted", move |data| {
        let args = data.args();
        if let Some(Variant::String(name)) = args.get(0) {
            let mut state = state_name.borrow_mut();
            state.character_name = name.clone();
            state.score += 100;
            println!("   👤 Character created: '{}' (+100 points)", name);
        }
        Ok(None)
    });

    // Connect chat input
    let state_chat = game_state.clone();
    let chat_submitted = Callable::function("on_chat_submitted", move |data| {
        let args = data.args();
        if let Some(Variant::String(message)) = args.get(0) {
            let mut state = state_chat.borrow_mut();
            state.messages_sent += 1;
            state.score += 25;
            println!("   💬 Message sent: '{}' (+25 points)", message);
        }
        Ok(None)
    });

    // Connect all signals
    signal_manager.connect(game_timer.get_timeout_signal().id(), game_complete, ConnectionFlags::default()).unwrap();
    signal_manager.connect(animation_timer.get_timeout_signal().id(), anim_cycle, ConnectionFlags::default()).unwrap();
    signal_manager.connect(name_input.get_text_submitted_signal().id(), name_submitted, ConnectionFlags::default()).unwrap();
    signal_manager.connect(chat_input.get_text_submitted_signal().id(), chat_submitted, ConnectionFlags::default()).unwrap();

    // Start game systems
    game_timer.start(&mut signal_manager);
    animation_timer.start(&mut signal_manager);
    character_animator.play(&mut signal_manager, None); // Use autoplay
    ui_animator.play(&mut signal_manager, Some("menu_fade".to_string()));
    ambient_audio.play(&mut signal_manager);

    println!("   🎮 Interactive RPG demo started!");

    // Simulate complete interactive game session
    let mut total_time = 0.0;
    let delta = 0.5;
    let mut player_position = Vector2::new(0.0, 0.0);

    // Define game scenario events
    let scenario_events = vec![
        (1.0, "Character creation phase"),
        (3.0, "Exploration begins"),
        (6.0, "Combat encounter"),
        (9.0, "Social interaction"),
        (12.0, "Treasure discovery"),
        (15.0, "Boss battle"),
    ];

    let mut event_index = 0;
    let mut last_movement_time = 0.0;

    while total_time < 18.0 && !game_state.borrow().game_completed {
        total_time += delta;

        // Process scenario events
        if event_index < scenario_events.len() && total_time >= scenario_events[event_index].0 {
            let (_, event_name) = &scenario_events[event_index];
            println!("   📖 Scenario: {}", event_name);

            match event_index {
                0 => {
                    // Character creation
                    name_input.grab_focus(&mut signal_manager);
                    name_input.insert_text_at_cursor("Aragorn".to_string(), &mut signal_manager);
                    name_input.submit_text(&mut signal_manager);
                    sfx_audio.play(&mut signal_manager);
                }
                1 => {
                    // Start exploration
                    character_animator.play(&mut signal_manager, Some("walk".to_string()));
                    main_camera.shake(5.0, 0.3);
                }
                2 => {
                    // Combat encounter
                    character_animator.set_speed_scale(2.0);
                    main_camera.shake(12.0, 0.8);
                    sfx_audio.set_position(player_position);
                    sfx_audio.play(&mut signal_manager);
                    game_state.borrow_mut().score += 200;
                }
                3 => {
                    // Social interaction
                    chat_input.grab_focus(&mut signal_manager);
                    chat_input.insert_text_at_cursor("Hello, fellow adventurer!".to_string(), &mut signal_manager);
                    chat_input.submit_text(&mut signal_manager);
                    chat_input.clear(&mut signal_manager);
                }
                4 => {
                    // Treasure discovery
                    character_animator.play(&mut signal_manager, Some("idle".to_string()));
                    game_state.borrow_mut().bonus_points += 500;
                    main_camera.set_zoom(Vector2::new(2.0, 2.0));
                }
                5 => {
                    // Boss battle
                    character_animator.set_speed_scale(3.0);
                    main_camera.shake(20.0, 1.5);
                    game_state.borrow_mut().score += 1000;
                }
                _ => {}
            }
            event_index += 1;
        }

        // Simulate player movement
        if total_time - last_movement_time > 2.0 {
            let movement_patterns = vec![
                Vector2::new(100.0, 0.0),
                Vector2::new(200.0, 150.0),
                Vector2::new(50.0, 300.0),
                Vector2::new(-150.0, 200.0),
                Vector2::new(-300.0, 0.0),
                Vector2::new(0.0, -100.0),
            ];

            let pattern_index = ((total_time / 2.0) as usize) % movement_patterns.len();
            player_position = movement_patterns[pattern_index];
            last_movement_time = total_time;

            println!("     🏃 Player moved to: {}", player_position);
        }

        // Update all systems
        game_timer.update(delta, &mut signal_manager);
        animation_timer.update(delta, &mut signal_manager);
        main_camera.update(delta as f32, Some(player_position));
        character_animator.update(delta as f32, &mut signal_manager);
        ui_animator.update(delta as f32, &mut signal_manager);
        ambient_audio.update(delta as f32, &mut signal_manager);
        sfx_audio.update(delta as f32, &mut signal_manager);

        // Update game state
        let mut state = game_state.borrow_mut();
        state.play_time = total_time;

        // Add periodic score bonuses
        if total_time.fract() < 0.1 && total_time > 5.0 {
            state.score += 10;
        }
    }

    // Final game statistics
    let final_state = game_state.borrow();
    println!("\n   📊 Final Interactive Game Statistics:");
    println!("     Character Name: '{}'", final_state.character_name);
    println!("     Play Time: {:.1}s", final_state.play_time);
    println!("     Score: {}", final_state.score);
    println!("     Bonus Points: {}", final_state.bonus_points);
    println!("     Final Score: {}", final_state.final_score);
    println!("     Messages Sent: {}", final_state.messages_sent);
    println!("     Animation Cycles: {}", final_state.animation_cycles);
    println!("     Game Completed: {}", final_state.game_completed);

    // System final states
    println!("     Camera Position: {}", main_camera.get_position());
    println!("     Camera Zoom: {}", main_camera.get_zoom());
    println!("     Current Animation: {:?}", character_animator.get_current_animation());
    println!("     Animation Playing: {}", character_animator.is_playing());
    println!("     Ambient Audio Playing: {}", ambient_audio.is_playing());
    println!("     Character Name Input: '{}'", name_input.get_text());
    println!("     Chat Input Focused: {}", chat_input.is_focused());

    println!("\n   🎉 Interactive game scenario completed successfully!");
    println!("   All advanced nodes (AnimationPlayer, LineEdit) integrated seamlessly");
    println!("   with essential nodes (Timer, AudioStreamPlayer2D, Camera2D)!");
}

// Extended game state for interactive scenario
#[derive(Debug)]
struct InteractiveGameState {
    character_name: String,
    play_time: f64,
    score: i32,
    bonus_points: i32,
    final_score: i32,
    messages_sent: i32,
    animation_cycles: i32,
    game_completed: bool,
}

impl InteractiveGameState {
    fn new() -> Self {
        Self {
            character_name: String::new(),
            play_time: 0.0,
            score: 0,
            bonus_points: 0,
            final_score: 0,
            messages_sent: 0,
            animation_cycles: 0,
            game_completed: false,
        }
    }
}
