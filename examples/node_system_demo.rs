//! Node System Demo
//!
//! This example demonstrates the basic usage of Verturion's Node system,
//! including creating nodes, building scene trees, and using the Variant system.

use verturion::core::scene::{Node, NodePath};
use verturion::core::variant::Variant;
use verturion::core::math::Vector2;

fn main() {
    println!("=== Verturion Node System Demo ===\n");

    // Create a simple scene tree
    demo_basic_scene_tree();
    
    // Demonstrate node navigation
    demo_node_navigation();
    
    // Show Variant integration
    demo_variant_integration();
    
    // Demonstrate node duplication
    demo_node_duplication();
}

fn demo_basic_scene_tree() {
    println!("1. Basic Scene Tree Creation:");
    
    // Create root node
    let mut root = Node::new("Root");
    println!("   Created root: {}", root);
    
    // Create child nodes
    let mut player = Node::new("Player");
    let weapon = Node::new("Weapon");
    let inventory = Node::new("Inventory");
    
    // Build the scene tree
    player.add_child(weapon.clone());
    player.add_child(inventory.clone());
    root.add_child(player.clone());
    
    // Add more nodes to demonstrate hierarchy
    let mut ui = Node::new("UI");
    let health_bar = Node::new("HealthBar");
    let minimap = Node::new("Minimap");
    
    ui.add_child(health_bar.clone());
    ui.add_child(minimap.clone());
    root.add_child(ui.clone());
    
    println!("   Root has {} children", root.get_child_count());
    println!("   Player has {} children", player.get_child_count());
    println!("   UI has {} children", ui.get_child_count());
    
    // Show the tree structure
    println!("   Scene tree structure:");
    print_node_tree(&root, 0);
    println!();
}

fn demo_node_navigation() {
    println!("2. Node Navigation:");
    
    // Create a scene tree
    let mut root = Node::new("Root");
    let mut level = Node::new("Level");
    let mut player = Node::new("Player");
    let weapon = Node::new("Weapon");
    
    player.add_child(weapon.clone());
    level.add_child(player.clone());
    root.add_child(level.clone());
    
    // Demonstrate path-based navigation
    println!("   Finding nodes by path:");
    
    if let Some(found_player) = root.get_node("Level/Player") {
        println!("   Found player: {}", found_player.get_name());
    }
    
    if let Some(found_weapon) = root.get_node("Level/Player/Weapon") {
        println!("   Found weapon: {}", found_weapon.get_name());
    }
    
    // Demonstrate has_node
    println!("   Checking node existence:");
    println!("   Has 'Level/Player': {}", root.has_node("Level/Player"));
    println!("   Has 'Level/Enemy': {}", root.has_node("Level/Enemy"));
    
    // Show node paths
    println!("   Node paths:");
    println!("   Weapon path: {}", weapon.get_path());
    println!("   Player path: {}", player.get_path());
    println!();
}

fn demo_variant_integration() {
    println!("3. Variant System Integration:");
    
    // Create nodes and convert to variants
    let player = Node::new("Player");
    let enemy = Node::new("Enemy");
    
    let player_var = Variant::from(player.clone());
    let enemy_var = Variant::from(enemy.clone());
    
    println!("   Created node variants:");
    println!("   Player variant: {}", player_var);
    println!("   Enemy variant: {}", enemy_var);
    
    // Demonstrate type checking
    println!("   Type checking:");
    println!("   Player variant is_node(): {}", player_var.is_node());
    println!("   Player variant is_string(): {}", player_var.is_string());
    
    // Extract nodes from variants
    if let Some(extracted_player) = player_var.as_node() {
        println!("   Extracted player name: {}", extracted_player.get_name());
        println!("   Extracted player ID: {}", extracted_player.get_id());
    }
    
    // Use in collections
    use verturion::core::variant::{Array, Dictionary};
    
    let mut node_array = Array::new();
    node_array.push_back(player_var.clone());
    node_array.push_back(enemy_var.clone());
    node_array.push_back(Variant::from("Some string"));
    
    println!("   Array with {} items:", node_array.size());
    for i in 0..node_array.size() {
        if let Some(item) = node_array.get(i) {
            if item.is_node() {
                println!("     [{}] Node: {}", i, item.as_node().unwrap().get_name());
            } else {
                println!("     [{}] Other: {}", i, item);
            }
        }
    }
    
    let mut node_dict = Dictionary::new();
    node_dict.set(Variant::from("player"), player_var);
    node_dict.set(Variant::from("enemy"), enemy_var);
    
    println!("   Dictionary with {} items", node_dict.size());
    if let Some(player_from_dict) = node_dict.get(&Variant::from("player")) {
        if let Some(node) = player_from_dict.as_node() {
            println!("     Retrieved player: {}", node.get_name());
        }
    }
    println!();
}

fn demo_node_duplication() {
    println!("4. Node Duplication:");
    
    let original = Node::new("OriginalNode");
    println!("   Original: {} (ID: {})", original.get_name(), original.get_id());
    
    // Clone creates a reference to the same node
    let cloned = original.clone();
    println!("   Cloned: {} (ID: {})", cloned.get_name(), cloned.get_id());
    println!("   Clone is same node: {}", original == cloned);
    
    // Duplicate creates a new node with same properties
    let duplicated = original.duplicate();
    println!("   Duplicated: {} (ID: {})", duplicated.get_name(), duplicated.get_id());
    println!("   Duplicate is different node: {}", original != duplicated);
    println!();
}

fn print_node_tree(node: &Node, depth: usize) {
    let indent = "  ".repeat(depth);
    println!("{}├─ {} (ID: {})", indent, node.get_name(), node.get_id());
    
    let children = node.get_children();
    for child in children {
        print_node_tree(&child, depth + 1);
    }
}
