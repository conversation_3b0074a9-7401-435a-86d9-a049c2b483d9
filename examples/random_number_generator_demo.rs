use verturion::core::variant::random_number_generator::RandomNumberGenerator;

fn main() {
    println!("=== RandomNumberGenerator Demo ===\n");

    // Create a new RNG with time-based seed
    let mut rng = RandomNumberGenerator::new();
    println!("Created RNG with seed: {}", rng.get_seed());

    // Generate some random integers
    println!("\n--- Random Integers ---");
    for i in 0..5 {
        println!("randi() #{}: {}", i + 1, rng.randi());
    }

    // Generate random integers in a range
    println!("\n--- Random Integers in Range [1, 6] (dice roll) ---");
    for i in 0..10 {
        println!("Dice roll #{}: {}", i + 1, rng.randi_range(1, 6));
    }

    // Generate random floats
    println!("\n--- Random Floats [0.0, 1.0) ---");
    for i in 0..5 {
        println!("randf() #{}: {:.6}", i + 1, rng.randf());
    }

    // Generate random floats in a range
    println!("\n--- Random Floats in Range [10.0, 20.0) ---");
    for i in 0..5 {
        println!("randf_range(10.0, 20.0) #{}: {:.3}", i + 1, rng.randf_range(10.0, 20.0));
    }

    // Generate random booleans
    println!("\n--- Random Booleans ---");
    for i in 0..10 {
        println!("randb() #{}: {}", i + 1, rng.randb());
    }

    // Generate random booleans with probability
    println!("\n--- Random Booleans with 70% probability ---");
    for i in 0..10 {
        println!("randb_probability(0.7) #{}: {}", i + 1, rng.randb_probability(0.7));
    }

    // Shuffle an array
    println!("\n--- Array Shuffling ---");
    let mut numbers = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    println!("Original: {:?}", numbers);
    rng.shuffle(&mut numbers);
    println!("Shuffled: {:?}", numbers);

    // Choose random elements
    println!("\n--- Random Choice ---");
    let colors = vec!["red", "green", "blue", "yellow", "purple"];
    for i in 0..5 {
        if let Some(color) = rng.choose(&colors) {
            println!("Random color #{}: {}", i + 1, color);
        }
    }

    // Demonstrate deterministic behavior with seeded RNG
    println!("\n--- Deterministic Behavior ---");
    let mut rng1 = RandomNumberGenerator::from_seed(42);
    let mut rng2 = RandomNumberGenerator::from_seed(42);

    println!("Both RNGs seeded with 42:");
    for i in 0..3 {
        let val1 = rng1.randi();
        let val2 = rng2.randi();
        println!("  Iteration {}: RNG1={}, RNG2={}, Equal={}", i + 1, val1, val2, val1 == val2);
    }

    // State management
    println!("\n--- State Management ---");
    let mut rng = RandomNumberGenerator::from_seed(123);
    let value1 = rng.randi();
    println!("Generated value: {}", value1);

    let state = rng.get_state();
    println!("Saved state: {:?}", state);

    let value2 = rng.randi();
    let value3 = rng.randi();
    println!("Generated more values: {}, {}", value2, value3);

    rng.set_state(state);
    let value4 = rng.randi();
    println!("After restoring state, next value: {} (should equal {})", value4, value2);
    println!("Values match: {}", value2 == value4);

    println!("\n=== Demo Complete ===");
}
