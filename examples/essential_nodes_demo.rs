//! Essential Nodes Demo
//!
//! This example demonstrates the essential game development nodes in the Verturion
//! engine including Timer, AudioStreamPlayer2D, and Camera2D working together in
//! practical game scenarios. It showcases time-based events, spatial audio, and
//! camera management for complete game development workflows.

use verturion::core::scene::nodes::{
    Timer, TimerMode, AudioStreamPlayer2D, AttenuationModel, Camera2D, AnchorMode
};
use verturion::core::signal::{SignalManager, SignalData, Callable, ConnectionFlags};
use verturion::core::math::Vector2;
use std::cell::RefCell;
use std::rc::Rc;

fn main() {
    println!("=== Verturion Essential Nodes Demo ===\n");

    // Demonstrate Timer functionality
    demo_timer_system();

    // Demonstrate AudioStreamPlayer2D functionality
    demo_audio_system();

    // Demonstrate Camera2D functionality
    demo_camera_system();

    // Demonstrate integrated game scenario
    demo_integrated_game_scenario();
}

fn demo_timer_system() {
    println!("1. Timer System:");

    let mut signal_manager = SignalManager::new();
    let mut game_timer = Timer::new("GameTimer");
    let mut respawn_timer = Timer::new("RespawnTimer");

    // Configure timers
    game_timer.set_wait_time(60.0); // 1 minute game timer
    game_timer.set_one_shot(true);

    respawn_timer.set_wait_time(3.0); // 3 second respawn
    respawn_timer.set_one_shot(false); // Repeating

    // Register timer signals
    signal_manager.register_signal(game_timer.get_timeout_signal().clone());
    signal_manager.register_signal(respawn_timer.get_timeout_signal().clone());

    // Create game state tracker
    let game_state = Rc::new(RefCell::new(GameState::new()));

    // Connect game timer timeout
    let game_state_timer = game_state.clone();
    let game_timeout_callable = Callable::function("on_game_timeout", move |_| {
        let mut state = game_state_timer.borrow_mut();
        state.game_finished = true;
        println!("   🏁 Game finished! Final score: {}", state.score);
        Ok(None)
    });

    // Connect respawn timer timeout
    let game_state_respawn = game_state.clone();
    let respawn_callable = Callable::function("on_respawn", move |_| {
        let mut state = game_state_respawn.borrow_mut();
        state.lives += 1;
        state.respawn_count += 1;
        println!("   💚 Player respawned! Lives: {}, Total respawns: {}",
                 state.lives, state.respawn_count);
        Ok(None)
    });

    // Connect signals
    signal_manager.connect(game_timer.get_timeout_signal().id(), game_timeout_callable, ConnectionFlags::default()).unwrap();
    signal_manager.connect(respawn_timer.get_timeout_signal().id(), respawn_callable, ConnectionFlags::default()).unwrap();

    // Start timers
    game_timer.start(&mut signal_manager);
    respawn_timer.start(&mut signal_manager);

    println!("   Started game timer (60s) and respawn timer (3s repeating)");

    // Simulate game loop
    let mut total_time = 0.0;
    let delta = 0.5; // 500ms updates

    while total_time < 10.0 { // Run for 10 seconds
        total_time += delta;

        // Update timers
        game_timer.update(delta, &mut signal_manager);
        respawn_timer.update(delta, &mut signal_manager);

        // Simulate game events
        if total_time > 2.0 && total_time < 2.5 {
            game_state.borrow_mut().score += 100;
            println!("   🎯 Score increased to {}", game_state.borrow().score);
        }

        if total_time > 5.0 && total_time < 5.5 {
            game_state.borrow_mut().lives -= 1;
            println!("   💔 Player lost a life! Lives: {}", game_state.borrow().lives);
        }
    }

    println!("   Timer demo completed. Game state: {:?}", *game_state.borrow());
    println!();
}

fn demo_audio_system() {
    println!("2. Audio System:");

    let mut signal_manager = SignalManager::new();
    let mut background_music = AudioStreamPlayer2D::new("BackgroundMusic");
    let mut explosion_sound = AudioStreamPlayer2D::new("ExplosionSound");
    let mut footsteps = AudioStreamPlayer2D::new("Footsteps");

    // Configure background music
    background_music.set_stream(Some("res://audio/background.ogg".to_string()));
    background_music.set_volume_db(-8.0); // Quieter background
    background_music.set_bus("Music".to_string());
    background_music.set_position(Vector2::new(0.0, 0.0)); // Center
    background_music.set_max_distance(1000.0);
    background_music.set_attenuation(AttenuationModel::Disabled); // Global music

    // Configure explosion sound
    explosion_sound.set_stream(Some("res://audio/explosion.wav".to_string()));
    explosion_sound.set_volume_db(-3.0);
    explosion_sound.set_bus("SFX".to_string());
    explosion_sound.set_position(Vector2::new(200.0, 100.0));
    explosion_sound.set_max_distance(300.0);
    explosion_sound.set_attenuation(AttenuationModel::InverseSquareDistance);

    // Configure footsteps
    footsteps.set_stream(Some("res://audio/footstep.wav".to_string()));
    footsteps.set_volume_db(-6.0);
    footsteps.set_pitch_scale(1.2); // Slightly faster
    footsteps.set_bus("SFX".to_string());
    footsteps.set_max_distance(150.0);
    footsteps.set_attenuation(AttenuationModel::InverseDistance);

    // Register finished signals
    signal_manager.register_signal(background_music.get_finished_signal().clone());
    signal_manager.register_signal(explosion_sound.get_finished_signal().clone());
    signal_manager.register_signal(footsteps.get_finished_signal().clone());

    // Create audio event tracker
    let audio_events = Rc::new(RefCell::new(Vec::<String>::new()));

    // Connect finished signals
    let events_music = audio_events.clone();
    let music_finished = Callable::function("on_music_finished", move |_| {
        events_music.borrow_mut().push("Background music finished".to_string());
        println!("   🎵 Background music finished");
        Ok(None)
    });

    let events_explosion = audio_events.clone();
    let explosion_finished = Callable::function("on_explosion_finished", move |_| {
        events_explosion.borrow_mut().push("Explosion sound finished".to_string());
        println!("   💥 Explosion sound finished");
        Ok(None)
    });

    signal_manager.connect(background_music.get_finished_signal().id(), music_finished, ConnectionFlags::default()).unwrap();
    signal_manager.connect(explosion_sound.get_finished_signal().id(), explosion_finished, ConnectionFlags::default()).unwrap();

    // Start audio playback
    background_music.play(&mut signal_manager);
    explosion_sound.play(&mut signal_manager);

    println!("   Started background music and explosion sound");

    // Simulate listener movement and audio updates
    let listener_positions = vec![
        Vector2::new(0.0, 0.0),    // At center
        Vector2::new(100.0, 50.0), // Moving towards explosion
        Vector2::new(200.0, 100.0), // At explosion location
        Vector2::new(300.0, 150.0), // Moving away
    ];

    for (i, listener_pos) in listener_positions.iter().enumerate() {
        println!("   Listener at position: {}", listener_pos);

        // Calculate spatial audio for explosion
        let attenuation = explosion_sound.calculate_attenuation(*listener_pos);
        let panning = explosion_sound.calculate_panning(*listener_pos);

        println!("     Explosion - Attenuation: {:.3}, Panning: {:.3}", attenuation, panning);

        // Update footsteps position to follow listener
        footsteps.set_position(*listener_pos);

        // Play footsteps at new position
        if i > 0 {
            footsteps.play(&mut signal_manager);
            println!("     👣 Footstep played at {}", listener_pos);
        }

        // Update audio systems
        background_music.update(2.5, &mut signal_manager);
        explosion_sound.update(2.5, &mut signal_manager);
        footsteps.update(0.3, &mut signal_manager); // Short footstep
    }

    println!("   Audio events: {:?}", *audio_events.borrow());
    println!();
}

fn demo_camera_system() {
    println!("3. Camera System:");

    let mut main_camera = Camera2D::new("MainCamera");
    let mut ui_camera = Camera2D::new("UICamera");

    // Configure main game camera
    main_camera.set_current(true);
    main_camera.set_zoom(Vector2::new(1.5, 1.5)); // Zoom in
    main_camera.set_smoothing_enabled(true);
    main_camera.set_smoothing_speed(3.0);
    main_camera.set_limit_left(-500.0);
    main_camera.set_limit_right(500.0);
    main_camera.set_limit_top(-300.0);
    main_camera.set_limit_bottom(300.0);
    main_camera.set_offset(Vector2::new(0.0, -50.0)); // Look ahead

    // Configure UI camera (no limits, no smoothing)
    ui_camera.set_current(false);
    ui_camera.set_zoom(Vector2::new(1.0, 1.0));
    ui_camera.set_smoothing_enabled(false);

    println!("   Configured main camera with smoothing and limits");
    println!("   Configured UI camera for static overlay");

    // Simulate player movement and camera following
    let player_positions = vec![
        Vector2::new(0.0, 0.0),
        Vector2::new(100.0, 50.0),
        Vector2::new(250.0, 100.0),
        Vector2::new(600.0, 200.0), // Beyond camera limits
        Vector2::new(-600.0, -400.0), // Beyond other side
    ];

    println!("   Simulating player movement and camera following:");

    for (i, player_pos) in player_positions.iter().enumerate() {
        println!("     Frame {}: Player at {}", i + 1, player_pos);

        // Update camera to follow player
        main_camera.update(0.016, Some(*player_pos)); // 60 FPS

        let camera_pos = main_camera.get_position();
        println!("       Camera moved to: {}", camera_pos);

        // Verify camera respects limits
        assert!(camera_pos.x >= -500.0 && camera_pos.x <= 500.0);
        assert!(camera_pos.y >= -300.0 && camera_pos.y <= 300.0);

        // Trigger screen shake on certain events
        if i == 2 {
            main_camera.shake(15.0, 0.5);
            println!("       💥 Screen shake triggered!");
        }

        // Update shake
        if main_camera.is_shaking() {
            let shake_offset = main_camera.get_shake_offset();
            println!("       📳 Shake offset: {}", shake_offset);
        }
    }

    // Test camera zoom changes
    println!("   Testing dynamic zoom:");
    main_camera.set_zoom(Vector2::new(2.0, 2.0));
    println!("     Zoomed to 2x: {}", main_camera.get_zoom());

    main_camera.set_zoom(Vector2::new(0.5, 0.5));
    println!("     Zoomed to 0.5x: {}", main_camera.get_zoom());

    // Test force position update
    main_camera.force_update_scroll(Vector2::new(0.0, 0.0));
    println!("     Force updated camera to origin: {}", main_camera.get_position());

    println!();
}

// Helper struct for tracking game state
#[derive(Debug)]
struct GameState {
    score: i32,
    lives: i32,
    respawn_count: i32,
    game_finished: bool,
}

impl GameState {
    fn new() -> Self {
        Self {
            score: 0,
            lives: 3,
            respawn_count: 0,
            game_finished: false,
        }
    }
}

fn demo_integrated_game_scenario() {
    println!("4. Integrated Game Scenario:");
    println!("   Simulating a complete 2D action game with all essential nodes");

    let mut signal_manager = SignalManager::new();

    // Create game systems
    let mut game_timer = Timer::new("LevelTimer");
    let mut power_up_timer = Timer::new("PowerUpTimer");
    let mut main_camera = Camera2D::new("GameCamera");
    let mut ambient_audio = AudioStreamPlayer2D::new("AmbientSound");
    let mut action_audio = AudioStreamPlayer2D::new("ActionSound");

    // Configure level timer (30 second level)
    game_timer.set_wait_time(30.0);
    game_timer.set_one_shot(true);

    // Configure power-up timer (spawns every 5 seconds)
    power_up_timer.set_wait_time(5.0);
    power_up_timer.set_one_shot(false);

    // Configure game camera
    main_camera.set_current(true);
    main_camera.set_zoom(Vector2::new(1.2, 1.2));
    main_camera.set_smoothing_enabled(true);
    main_camera.set_smoothing_speed(4.0);
    main_camera.set_limit_left(-800.0);
    main_camera.set_limit_right(800.0);
    main_camera.set_limit_top(-600.0);
    main_camera.set_limit_bottom(600.0);

    // Configure ambient audio
    ambient_audio.set_stream(Some("res://audio/forest_ambient.ogg".to_string()));
    ambient_audio.set_volume_db(-12.0);
    ambient_audio.set_bus("Ambient".to_string());
    ambient_audio.set_attenuation(AttenuationModel::Disabled);

    // Configure action audio
    action_audio.set_stream(Some("res://audio/sword_clash.wav".to_string()));
    action_audio.set_volume_db(-5.0);
    action_audio.set_bus("SFX".to_string());
    action_audio.set_max_distance(200.0);
    action_audio.set_attenuation(AttenuationModel::InverseDistance);

    // Register all signals
    signal_manager.register_signal(game_timer.get_timeout_signal().clone());
    signal_manager.register_signal(power_up_timer.get_timeout_signal().clone());
    signal_manager.register_signal(ambient_audio.get_finished_signal().clone());
    signal_manager.register_signal(action_audio.get_finished_signal().clone());

    // Create comprehensive game state
    let game_state = Rc::new(RefCell::new(IntegratedGameState::new()));

    // Connect level timer
    let state_level = game_state.clone();
    let level_complete = Callable::function("on_level_complete", move |_| {
        let mut state = state_level.borrow_mut();
        state.level_completed = true;
        state.final_score = state.score + (state.time_bonus * 10);
        println!("   🏆 Level completed! Final score: {} (bonus: {})",
                 state.final_score, state.time_bonus * 10);
        Ok(None)
    });

    // Connect power-up timer
    let state_powerup = game_state.clone();
    let power_up_spawn = Callable::function("on_power_up_spawn", move |_| {
        let mut state = state_powerup.borrow_mut();
        state.power_ups_spawned += 1;
        state.score += 50;
        println!("   ⭐ Power-up spawned! Total: {}, Score: {}",
                 state.power_ups_spawned, state.score);
        Ok(None)
    });

    // Connect audio finished signals
    let state_audio = game_state.clone();
    let ambient_loop = Callable::function("on_ambient_finished", move |_| {
        let mut state = state_audio.borrow_mut();
        state.audio_loops += 1;
        println!("   🎵 Ambient audio loop #{}", state.audio_loops);
        Ok(None)
    });

    // Connect all signals
    signal_manager.connect(game_timer.get_timeout_signal().id(), level_complete, ConnectionFlags::default()).unwrap();
    signal_manager.connect(power_up_timer.get_timeout_signal().id(), power_up_spawn, ConnectionFlags::default()).unwrap();
    signal_manager.connect(ambient_audio.get_finished_signal().id(), ambient_loop, ConnectionFlags::default()).unwrap();

    // Start game systems
    game_timer.start(&mut signal_manager);
    power_up_timer.start(&mut signal_manager);
    ambient_audio.play(&mut signal_manager);

    println!("   🎮 Game started! Level timer: 30s, Power-ups every 5s");

    // Simulate complete game session
    let mut total_time = 0.0;
    let delta = 0.5; // 500ms updates
    let mut player_position = Vector2::new(0.0, 0.0);

    // Define player movement pattern
    let movement_pattern = vec![
        Vector2::new(100.0, 0.0),   // Move right
        Vector2::new(200.0, 100.0), // Move right-up
        Vector2::new(100.0, 200.0), // Move left-up
        Vector2::new(-100.0, 100.0), // Move left-down
        Vector2::new(-200.0, 0.0),   // Move left
        Vector2::new(0.0, -100.0),   // Return to center
    ];

    let mut movement_index = 0;
    let mut last_action_time = 0.0;

    while total_time < 12.0 && !game_state.borrow().level_completed {
        total_time += delta;

        // Update player movement
        if total_time - last_action_time > 2.0 && movement_index < movement_pattern.len() {
            player_position = movement_pattern[movement_index];
            movement_index += 1;
            last_action_time = total_time;

            println!("   🏃 Player moved to: {}", player_position);

            // Trigger action audio at player position
            action_audio.set_position(player_position);
            action_audio.play(&mut signal_manager);

            // Trigger screen shake for combat
            if movement_index % 2 == 0 {
                main_camera.shake(8.0, 0.3);
                println!("     ⚔️ Combat action! Screen shake triggered");
            }

            // Update score for movement/exploration
            game_state.borrow_mut().score += 25;
        }

        // Update all systems
        game_timer.update(delta, &mut signal_manager);
        power_up_timer.update(delta, &mut signal_manager);
        main_camera.update(delta as f32, Some(player_position));
        ambient_audio.update(delta as f32, &mut signal_manager);
        action_audio.update(delta as f32, &mut signal_manager);

        // Calculate time bonus (decreases over time)
        let remaining_time = 30.0 - total_time;
        if remaining_time > 0.0 {
            game_state.borrow_mut().time_bonus = (remaining_time / 30.0 * 100.0) as i32;
        }

        // Simulate random events
        if total_time > 6.0 && total_time < 6.5 {
            println!("   🔥 Boss battle! Intense screen shake!");
            main_camera.shake(20.0, 1.0);
            game_state.borrow_mut().score += 200;
        }

        if total_time > 9.0 && total_time < 9.5 {
            println!("   💎 Rare item collected!");
            game_state.borrow_mut().score += 500;
        }
    }

    // Final game state
    let final_state = game_state.borrow();
    println!("\n   📊 Final Game Statistics:");
    println!("     Score: {}", final_state.score);
    println!("     Final Score: {}", final_state.final_score);
    println!("     Power-ups Spawned: {}", final_state.power_ups_spawned);
    println!("     Audio Loops: {}", final_state.audio_loops);
    println!("     Time Bonus: {}%", final_state.time_bonus);
    println!("     Level Completed: {}", final_state.level_completed);

    // Camera final state
    println!("     Final Camera Position: {}", main_camera.get_position());
    println!("     Camera Zoom: {}", main_camera.get_zoom());
    println!("     Camera Shaking: {}", main_camera.is_shaking());

    // Audio final state
    println!("     Ambient Playing: {}", ambient_audio.is_playing());
    println!("     Action Audio Playing: {}", action_audio.is_playing());

    println!("\n   🎉 Integrated game scenario completed successfully!");
    println!("   All essential nodes (Timer, AudioStreamPlayer2D, Camera2D) worked together seamlessly!");
}

// Extended game state for integrated scenario
#[derive(Debug)]
struct IntegratedGameState {
    score: i32,
    final_score: i32,
    power_ups_spawned: i32,
    audio_loops: i32,
    time_bonus: i32,
    level_completed: bool,
}

impl IntegratedGameState {
    fn new() -> Self {
        Self {
            score: 0,
            final_score: 0,
            power_ups_spawned: 0,
            audio_loops: 0,
            time_bonus: 100,
            level_completed: false,
        }
    }
}
